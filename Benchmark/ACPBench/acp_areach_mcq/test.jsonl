{"id": 5945395448725160032, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c45 on board. The cars are at locations as follows: c29, c19, c36, c21, c8, c44, c13, c11, c38, c2, c20, c37, c26, c30, c40, c14, c12, c9, c43, c0, and c22 are at l0; c1, c39, c28, c6, c23, c17, c18, c27, c48, c33, c24, c46, c47, c35, c7, c15, c16, c42, c34, c4, c5, c3, c41, c31, c32, c10, c25, and c49 are at l1.", "question": "Which of the following actions can eventually be applied? A. board the car c20 at location l0. B. travel by sea from location c43 to location c4. C. debark the car c2 to location c8 from the ferry. D. board the car c26 at location c23.", "choices": {"label": ["A", "B", "C", "D"], "text": ["board the car c20 at location l0", "travel by sea from location c43 to location c4", "debark the car c2 to location c8 from the ferry", "board the car c26 at location c23"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 8865770558839849461, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l4, with the car c47 on board. The cars are at locations as follows: c28, c14, c9, c29, c38, c41, c8, c0, c27, c30, c46, and c5 are at l4; c23, c37, c13, c20, c7, c31, c34, c12, c6, and c25 are at l3; c4, c18, c36, c24, c42, c45, c16, c17, c48, c10, and c1 are at l0; c11, c19, c33, c2, c15, c44, c32, and c49 are at l1; c22, c35, c21, c3, c43, c39, c40, and c26 are at l2.", "question": "Which of the following actions can eventually be applied? A. sail from location c47 to location c8. B. sail from location c12 to location c49. C. embark the car c33 at location l1 on to the ferry. D. embark the car l3 at location c7 on to the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["sail from location c47 to location c8", "sail from location c12 to location c49", "embark the car c33 at location l1 on to the ferry", "embark the car l3 at location c7 on to the ferry"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 7580961157121846567, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c9, c7, and c2 are at l0; c8, c1, c4, and c5 are at l1; c6, c3, and c0 are at l2.", "question": "Which of the following actions can eventually be applied? A. travel by sea from location c4 to location l0. B. board car c1 at location c9. C. unload the car l2 from the ferry to location c5. D. unload the car c3 from the ferry to location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel by sea from location c4 to location l0", "board car c1 at location c9", "unload the car l2 from the ferry to location c5", "unload the car c3 from the ferry to location l0"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -7762910174775461862, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1, with the car c0 on board. The cars are at locations as follows: c2 is at l1; c1 is at l0.", "question": "Which of the following actions can eventually be applied? A. sail from location c0 to location c2. B. board car c0 at location c2. C. board car c1 at location l0. D. board car l0 at location c1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["sail from location c0 to location c2", "board car c0 at location c2", "board car c1 at location l0", "board car l0 at location c1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 698839260684053327, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l2, with the car c40 on board. The cars are at locations as follows: c26, c28, c14, c20, c29, c35, c38, c0, c47, c36, c2, and c45 are at l4; c23, c39, c42, c37, c15, c22, c3, c13, c49, c7, c4, c43, c31, and c5 are at l3; c21, c19, c48, c33, c24, c46, c34, c44, and c32 are at l1; c27, c18, c6, c11, c41, c25, c9, c17, c10, and c1 are at l0; c8, c16, c30, and c12 are at l2.", "question": "Which of the following actions can eventually be applied? A. load the car c29 at location c0 on to the ferry. B. debark car c27 to location c25 from the ferry. C. debark car c40 to location l3 from the ferry. D. debark car c22 to location c12 from the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load the car c29 at location c0 on to the ferry", "debark car c27 to location c25 from the ferry", "debark car c40 to location l3 from the ferry", "debark car c22 to location c12 from the ferry"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 5967824783105565768, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l4, with the car c0 on board. The cars are at locations as follows: c1 is at l3; c2 is at l4.", "question": "Which of the following actions can eventually be applied? A. unload the car l3 from the ferry to location c0. B. unload the car l4 from the ferry to location l1. C. embark the car l3 at location c2 on to the ferry. D. travel by sea from location l2 to location l4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unload the car l3 from the ferry to location c0", "unload the car l4 from the ferry to location l1", "embark the car l3 at location c2 on to the ferry", "travel by sea from location l2 to location l4"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -2655862187112764159, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c9, c7, and c6 are at l0; c8, c1, c2, c4, c5, and c0 are at l1; c3 is at l2.", "question": "Which of the following actions can eventually be applied? A. board car c7 at location c1. B. debark the car c3 to location c0 from the ferry. C. debark the car c1 to location l0 from the ferry. D. board car c5 at location c0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["board car c7 at location c1", "debark the car c3 to location c0 from the ferry", "debark the car c1 to location l0 from the ferry", "board car c5 at location c0"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 7057624437091831260, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c38 on board. The cars are at locations as follows: c29, c36, c6, c21, c8, c44, c13, c11, c2, c33, c35, c20, c37, c25, c26, c30, c14, c12, c9, c0, and c22 are at l0; c1, c39, c28, c45, c23, c17, c18, c19, c27, c48, c24, c46, c47, c7, c15, c16, c40, c42, c34, c4, c5, c3, c41, c43, c31, c32, c10, and c49 are at l1.", "question": "Which of the following actions can eventually be applied? A. unload the car c20 from the ferry to location c1. B. board the car l1 at the location c32. C. unload the car c21 from the ferry to location c39. D. unload the car c0 from the ferry to location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unload the car c20 from the ferry to location c1", "board the car l1 at the location c32", "unload the car c21 from the ferry to location c39", "unload the car c0 from the ferry to location l1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 2985568666475827396, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c17 on board. The cars are at locations as follows: c29, c19, c18, c6, c8, c44, c13, c11, c2, c3, c33, c35, c20, c25, c30, c23, c14, c12, c9, c0, and c22 are at l0; c21, c1, c39, c28, c45, c37, c27, c48, c26, c24, c38, c46, c47, c7, c15, c16, c40, c42, c34, c4, c5, c41, c36, c43, c31, c32, c10, and c49 are at l1.", "question": "Which of the following actions can eventually be applied? A. debark the car c28 to location c45 from the ferry. B. board car c34 at location c15. C. board car c22 at location l0. D. travel by sea from location c39 to location c28.", "choices": {"label": ["A", "B", "C", "D"], "text": ["debark the car c28 to location c45 from the ferry", "board car c34 at location c15", "board car c22 at location l0", "travel by sea from location c39 to location c28"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -1742370745479040009, "group": "reachable_action_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l4 location and it is empty. The cars are at locations as follows: c1, c0, and c2 are at l3.", "question": "Which of the following actions can eventually be applied? A. travel by sea from location c0 to location l2. B. travel by sea from location l1 to location l2. C. travel by sea from location c0 to location c1. D. unload the car l1 from the ferry to location l2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel by sea from location c0 to location l2", "travel by sea from location l1 to location l2", "travel by sea from location c0 to location c1", "unload the car l1 from the ferry to location l2"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -1474170633806229538, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-2, l4-0, and l4-1 are in c4; l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l3-1, l3-0, and l3-2 are in c3; l0-0, l0-1, and l0-2 are in c0. Currently, p2 and t2 are at l2-1, t3 is at l3-0, t4 and a0 are at l4-0, t0 is at l0-2, t1 is at l1-2, p1 is at l3-1, p0 and p3 are in a0.", "question": "Which of the following actions can eventually be applied? A. load object a0 into truck t0 at location p1. B. unload object l4-1 from truck l2-1 at location c2. C. remove the object p3 from the airplane a0 and place it on the location l1-0. D. load object l4-0 into truck c2 at location l4-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load object a0 into truck t0 at location p1", "unload object l4-1 from truck l2-1 at location c2", "remove the object p3 from the airplane a0 and place it on the location l1-0", "load object l4-0 into truck c2 at location l4-1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 5550655872393039993, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p4 and t1 are at l1-0, a0 and t0 are at l0-0, t2 is at l2-1, p1 and p3 are at l2-0, p2 is at l0-1, p0 is in a0.", "question": "Which of the following actions can eventually be applied? A. remove the object l1-0 from the airplane p4 and place it on the location p0. B. load the object p3 from location l2-0 onto the airplane a0. C. navigate the truck l0-1 from location t2 in city p0 to location l1-0 in the same city. D. fly the airplane p4 from airport p1 to airport l1-2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["remove the object l1-0 from the airplane p4 and place it on the location p0", "load the object p3 from location l2-0 onto the airplane a0", "navigate the truck l0-1 from location t2 in city p0 to location l1-0 in the same city", "fly the airplane p4 from airport p1 to airport l1-2"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 4008075837916923612, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p0, p3, t1, and p2 are at l1-2, t2 is at l2-1, a0 and p1 are at l1-0, t0 and p4 are at l0-0.", "question": "Which of the following actions can eventually be applied? A. offload the object t2 from the airplane l1-2 at location p2. B. drive truck t2 from location l2-0 in city c2 to location l2-2 in the same city. C. drive truck l0-2 from location c0 in city l0-1 to location l1-2 in the same city. D. operate the airplane l2-0 from airport t1 to airport a0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["offload the object t2 from the airplane l1-2 at location p2", "drive truck t2 from location l2-0 in city c2 to location l2-2 in the same city", "drive truck l0-2 from location c0 in city l0-1 to location l1-2 in the same city", "operate the airplane l2-0 from airport t1 to airport a0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -8142953292802760641, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-3, l1-0, l1-5, l1-7, l1-6, l1-9, l1-2, l1-1, l1-4, and l1-8 are in c1; l4-0, l4-4, l4-6, l4-2, l4-3, l4-5, l4-9, l4-1, l4-8, and l4-7 are in c4; l3-3, l3-5, l3-6, l3-1, l3-4, l3-8, l3-7, l3-9, l3-2, and l3-0 are in c3; l2-3, l2-1, l2-9, l2-4, l2-8, l2-6, l2-0, l2-7, l2-2, and l2-5 are in c2; l0-5, l0-7, l0-3, l0-9, l0-0, l0-1, l0-6, l0-4, l0-8, and l0-2 are in c0. Currently, p3 is at l2-9, t3 and p2 are at l3-0, t1 is at l1-1, t4 is at l4-0, t0 is at l0-2, a0 is at l0-0, p1 is at l1-8, p0 is at l2-4, t2 is at l2-0.", "question": "Which of the following actions can eventually be applied? A. drive the truck t4 in city c4 from location l4-8 to location l4-2. B. place the object c1 into the truck l3-1 at location l1-1. C. place the object l4-0 into the truck c1 at location l3-1. D. unload object t3 from airplane l2-6 at location l1-4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drive the truck t4 in city c4 from location l4-8 to location l4-2", "place the object c1 into the truck l3-1 at location l1-1", "place the object l4-0 into the truck c1 at location l3-1", "unload object t3 from airplane l2-6 at location l1-4"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -6563286363467899485, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p2 and a0 are at l1-0, t1 is at l1-1, t0 is at l0-2, p0, p1, and p3 are in t0.", "question": "Which of the following actions can eventually be applied? A. navigate the truck l0-0 from its current location l1-1 in city p0 to the new location l0-2 within the same city. B. load the object p0 from location t0 onto the airplane p3. C. remove the object p1 from the truck t0 and place it on the location l0-1. D. place the object p0 into the truck l0-1 at location p1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate the truck l0-0 from its current location l1-1 in city p0 to the new location l0-2 within the same city", "load the object p0 from location t0 onto the airplane p3", "remove the object p1 from the truck t0 and place it on the location l0-1", "place the object p0 into the truck l0-1 at location p1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -8594491423422192622, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p4 is at l1-0, p0, a0, and t0 are at l0-0, t2 is at l2-1, p1 and p3 are at l2-0, t1 is at l1-2, p2 is at l0-1.", "question": "Which of the following actions can eventually be applied? A. drive truck t2 from location l2-2 in city c2 to location l2-0 in the same city. B. unload object l1-1 from airplane p3 at location l1-0. C. drive truck c1 from location t0 in city l0-1 to location p1 in the same city. D. fly airplane l1-1 from airport l1-2 to airport t0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drive truck t2 from location l2-2 in city c2 to location l2-0 in the same city", "unload object l1-1 from airplane p3 at location l1-0", "drive truck c1 from location t0 in city l0-1 to location p1 in the same city", "fly airplane l1-1 from airport l1-2 to airport t0"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -4624695102354256075, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-2, l4-0, and l4-1 are in c4; l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l3-1, l3-0, and l3-2 are in c3; l0-0, l0-1, and l0-2 are in c0. Currently, p0 and a0 are at l3-0, t3 and p3 are at l3-2, t4 is at l4-0, t0 is at l0-2, t1 is at l1-2, p2 and t2 are at l2-2, p1 is in t3.", "question": "Which of the following actions can eventually be applied? A. load object c0 into airplane l0-2 at location a0. B. remove the object l1-0 from the truck c2 and place it on the location t4. C. fly the airplane a0 from location l0-0 to location l2-0. D. remove the object l0-0 from the truck l0-2 and place it on the location l3-2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load object c0 into airplane l0-2 at location a0", "remove the object l1-0 from the truck c2 and place it on the location t4", "fly the airplane a0 from location l0-0 to location l2-0", "remove the object l0-0 from the truck l0-2 and place it on the location l3-2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -6673225145075556987, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-3, l1-0, l1-5, l1-7, l1-6, l1-9, l1-2, l1-1, l1-4, and l1-8 are in c1; l4-0, l4-4, l4-6, l4-2, l4-3, l4-5, l4-9, l4-1, l4-8, and l4-7 are in c4; l3-3, l3-5, l3-6, l3-1, l3-4, l3-8, l3-7, l3-9, l3-2, and l3-0 are in c3; l2-3, l2-1, l2-9, l2-4, l2-8, l2-6, l2-0, l2-7, l2-2, and l2-5 are in c2; l0-5, l0-7, l0-3, l0-9, l0-0, l0-1, l0-6, l0-4, l0-8, and l0-2 are in c0. Currently, p3 is at l2-9, a0 and t2 are at l2-0, t3 is at l3-0, t4 is at l4-0, t0 is at l0-2, t1 is at l1-0, p0 is at l2-4, p1 is in t2, p2 is in t4.", "question": "Which of the following actions can eventually be applied? A. load the object p2 from location l3-2 into the truck l3-8. B. fly airplane l3-5 from airport c2 to airport l2-5. C. drive truck t4 from location l4-1 in city c4 to location l4-8 in the same city. D. unload object l4-5 from airplane l1-8 at location l4-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load the object p2 from location l3-2 into the truck l3-8", "fly airplane l3-5 from airport c2 to airport l2-5", "drive truck t4 from location l4-1 in city c4 to location l4-8 in the same city", "unload object l4-5 from airplane l1-8 at location l4-0"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 12915953209085223, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-2, l4-0, and l4-1 are in c4; l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l3-1, l3-0, and l3-2 are in c3; l0-0, l0-1, and l0-2 are in c0. Currently, p2 and t2 are at l2-1, p3 is at l4-1, t3 is at l3-0, a0 is at l0-0, t4 is at l4-2, t1 is at l1-2, p1 is at l3-1, t0 is at l0-1, p0 is in a0.", "question": "Which of the following actions can eventually be applied? A. operate the airplane l4-1 from airport c4 to airport l0-1. B. unload the object l0-2 from the airplane l2-2 at location c0. C. navigate the truck t1 which is in location l1-1 in city c1 to another location l1-0 in the same city. D. operate the airplane p2 from airport a0 to airport l0-2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["operate the airplane l4-1 from airport c4 to airport l0-1", "unload the object l0-2 from the airplane l2-2 at location c0", "navigate the truck t1 which is in location l1-1 in city c1 to another location l1-0 in the same city", "operate the airplane p2 from airport a0 to airport l0-2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -6324340464747286456, "group": "reachable_action_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, p0 is at l1-1, p2, t1, and a0 are at l1-0, t0 is at l0-1, p3 and p1 are in t1.", "question": "Which of the following actions can eventually be applied? A. remove the object c0 from the truck p0 and place it on the location a0. B. load the object p3 from location l1-1 into the truck t1. C. remove the object l1-1 from the airplane t0 and place it on the location l1-0. D. navigate the truck l0-0 from its current location t1 in city a0 to the new location p0 within the same city.", "choices": {"label": ["A", "B", "C", "D"], "text": ["remove the object c0 from the truck p0 and place it on the location a0", "load the object p3 from location l1-1 into the truck t1", "remove the object l1-1 from the airplane t0 and place it on the location l1-0", "navigate the truck l0-0 from its current location t1 in city a0 to the new location p0 within the same city"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 7644848574864135557, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_1, block_3, and block_4. The following block(s) is stacked on top of another block: block_2 is on block_3.", "question": "Which of the following actions can eventually be applied? A. unstack object block_4 from object block_4. B. place the object block_3 on top of the object block_3. C. place the object block_2 on top of the object block_4. D. unstack object block_5 from object block_5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unstack object block_4 from object block_4", "place the object block_3 on top of the object block_3", "place the object block_2 on top of the object block_4", "unstack object block_5 from object block_5"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -2481914697002720276, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_6, block_14, block_2, block_1, block_10, block_13, block_8, block_16, and block_20. The following block(s) are stacked on top of another block: block_18 is on block_5, block_3 is on block_13, block_12 is on block_7, block_9 is on block_2, block_7 is on block_10, block_15 is on block_9, block_5 is on block_14, block_19 is on block_16, block_17 is on block_12, and block_11 is on block_1.", "question": "Which of the following actions can eventually be applied? A. stack the object block_16 on top of the object block_16. B. stack the object block_11 on top of the object block_11. C. stack the object block_17 on top of the object block_19. D. unstack the object block_8 from the object block_8.", "choices": {"label": ["A", "B", "C", "D"], "text": ["stack the object block_16 on top of the object block_16", "stack the object block_11 on top of the object block_11", "stack the object block_17 on top of the object block_19", "unstack the object block_8 from the object block_8"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 5851302287941330142, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_9. The following block(s) are on the table: block_6, block_10, block_13, block_15, and block_20. The following block(s) are stacked on top of another block: block_18 is on block_5, block_2 is on block_14, block_1 is on block_17, block_3 is on block_13, block_5 is on block_1, block_11 is on block_8, block_12 is on block_7, block_14 is on block_20, block_7 is on block_10, block_19 is on block_16, block_17 is on block_12, block_4 is on block_6, block_16 is on block_11, and block_8 is on block_4.", "question": "Which of the following actions can eventually be applied? A. unstack object block_14 from object block_14. B. unstack object block_9 from object block_2. C. place the object block_16 on top of the object block_16. D. unstack object block_1 from object block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unstack object block_14 from object block_14", "unstack object block_9 from object block_2", "place the object block_16 on top of the object block_16", "unstack object block_1 from object block_1"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -8305347071817214920, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_6, block_5, block_1, block_10, block_8, block_16, and block_20. The following block(s) are stacked on top of another block: block_18 is on block_5, block_2 is on block_14, block_3 is on block_13, block_12 is on block_7, block_9 is on block_2, block_14 is on block_20, block_13 is on block_4, block_7 is on block_10, block_15 is on block_9, block_19 is on block_16, block_4 is on block_11, block_17 is on block_12, and block_11 is on block_1.", "question": "Which of the following actions can eventually be applied? A. remove the object block_5 from on top of the object block_5. B. remove the object block_2 from on top of the object block_2. C. place the object block_15 on top of the object block_18. D. place the object block_11 on top of the object block_11.", "choices": {"label": ["A", "B", "C", "D"], "text": ["remove the object block_5 from on top of the object block_5", "remove the object block_2 from on top of the object block_2", "place the object block_15 on top of the object block_18", "place the object block_11 on top of the object block_11"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -8146842966636474759, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_10. The following block(s) are on the table: block_18, block_6, block_14, block_1, block_7, and block_12. The following block(s) are stacked on top of another block: block_19 is on block_3, block_3 is on block_13, block_9 is on block_15, block_13 is on block_4, block_16 is on block_20, block_20 is on block_8, block_5 is on block_14, block_4 is on block_11, block_17 is on block_12, block_8 is on block_7, block_11 is on block_1, block_2 is on block_18, and block_15 is on block_16.", "question": "Which of the following actions can eventually be applied? A. unstack object block_14 from object block_14. B. stack object block_7 on top of object block_7. C. unstack object block_9 from object block_15. D. stack object block_3 on top of object block_3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unstack object block_14 from object block_14", "stack object block_7 on top of object block_7", "unstack object block_9 from object block_15", "stack object block_3 on top of object block_3"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -1100319636333095822, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_20. The following block(s) are on the table: block_6, block_14, block_1, block_10, block_19, block_7, and block_16. The following block(s) are stacked on top of another block: block_18 is on block_5, block_3 is on block_13, block_2 is on block_12, block_9 is on block_2, block_13 is on block_4, block_15 is on block_9, block_5 is on block_14, block_12 is on block_17, block_17 is on block_3, block_4 is on block_11, block_8 is on block_7, and block_11 is on block_1.", "question": "Which of the following actions can eventually be applied? A. unstack object block_17 from object block_17. B. stack the object block_10 on top of the object block_18. C. unstack object block_13 from object block_13. D. stack the object block_5 on top of the object block_5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["unstack object block_17 from object block_17", "stack the object block_10 on top of the object block_18", "unstack object block_13 from object block_13", "stack the object block_5 on top of the object block_5"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -4041748659253133232, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_7. The following block(s) are on the table: block_3, block_10, block_8, and block_2. The following block(s) are stacked on top of another block: block_1 is on block_8, block_6 is on block_1, block_9 is on block_5, block_5 is on block_2, and block_4 is on block_9.", "question": "Which of the following actions can eventually be applied? A. stack the object block_8 on top of the object block_8. B. stack the object block_3 on top of the object block_6. C. unstack the object block_10 from the object block_10. D. unstack the object block_8 from the object block_8.", "choices": {"label": ["A", "B", "C", "D"], "text": ["stack the object block_8 on top of the object block_8", "stack the object block_3 on top of the object block_6", "unstack the object block_10 from the object block_10", "unstack the object block_8 from the object block_8"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -1582908400704320277, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_3 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_5, block_1 is on block_3, and block_4 is on block_1.", "question": "Which of the following actions can eventually be applied? A. place the object block_1 on top of the object block_1. B. place the object block_2 on top of the object block_2. C. place the object block_5 on top of the object block_2. D. unstack the object block_5 from the object block_5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object block_1 on top of the object block_1", "place the object block_2 on top of the object block_2", "place the object block_5 on top of the object block_2", "unstack the object block_5 from the object block_5"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 328318336256516274, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_18, block_6, block_14, block_1, block_10, block_19, block_7, and block_12. The following block(s) are stacked on top of another block: block_3 is on block_13, block_9 is on block_2, block_13 is on block_4, block_16 is on block_20, block_20 is on block_8, block_5 is on block_14, block_4 is on block_11, block_17 is on block_12, block_8 is on block_7, block_11 is on block_1, block_2 is on block_18, and block_15 is on block_16.", "question": "Which of the following actions can eventually be applied? A. place the object block_13 on top of the object block_15. B. remove the object block_3 from on top of the object block_3. C. remove the object block_6 from on top of the object block_6. D. remove the object block_18 from on top of the object block_18.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object block_13 on top of the object block_15", "remove the object block_3 from on top of the object block_3", "remove the object block_6 from on top of the object block_6", "remove the object block_18 from on top of the object block_18"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 111141248671009401, "group": "reachable_action_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_10. The following block(s) are on the table: block_1, block_8, and block_2. The following block(s) are stacked on top of another block: block_6 is on block_1, block_9 is on block_5, block_5 is on block_2, block_7 is on block_3, block_3 is on block_4, and block_4 is on block_9.", "question": "Which of the following actions can eventually be applied? A. place the object block_10 on top of the object block_10. B. unstack the object block_5 from the object block_5. C. place the object block_8 on top of the object block_8. D. unstack the object block_7 from the object block_3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object block_10 on top of the object block_10", "unstack the object block_5 from the object block_5", "place the object block_8 on top of the object block_8", "unstack the object block_7 from the object block_3"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 5261291322069755787, "group": "reachable_action_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f4-1f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-1 is at position f3-2f. Key key0-2 is at position f4-3f. Key key0-0 is at position f2-2f.", "question": "Which of the following actions can eventually be applied? A. pick up the key key0-1 from the current position f1-4f and loose the key key0-1 which is being held. B. pick up the key key0-0 from the current position f2-2f and loose the key key0-0 which is being held. C. pick up the key key0-2 from the current position f0-4f and loose the key key0-2 which is being held. D. move from place f3-0f to place f2-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up the key key0-1 from the current position f1-4f and loose the key key0-1 which is being held", "pick up the key key0-0 from the current position f2-2f and loose the key key0-0 which is being held", "pick up the key key0-2 from the current position f0-4f and loose the key key0-2 which is being held", "move from place f3-0f to place f2-0f"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 2243248281338501313, "group": "reachable_action_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-2f and is holding key0-0. All the positions are open except the following: f0-1f has shape0 shaped lock, f2-1f has shape0 shaped lock. Key key0-1 is at position f4-1f.", "question": "Which of the following actions can eventually be applied? A. move from f3-3f to f3-4f. B. pick up the key key0-0 from the current position f4-1f and loose the key key0-0 which is being held. C. pick up the key key0-0 from the current position f3-2f and loose the key key0-0 which is being held. D. pick up the key key0-0 from the current position f2-2f and loose the key key0-0 which is being held.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move from f3-3f to f3-4f", "pick up the key key0-0 from the current position f4-1f and loose the key key0-0 which is being held", "pick up the key key0-0 from the current position f3-2f and loose the key key0-0 which is being held", "pick up the key key0-0 from the current position f2-2f and loose the key key0-0 which is being held"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -7177993410900896004, "group": "reachable_action_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-3f and is holding key0-0. All the positions are open except the following: f0-1f has shape0 shaped lock, f2-1f has shape0 shaped lock. Key key0-1 is at position f4-1f.", "question": "Which of the following actions can eventually be applied? A. put down key key0-0 at current position place f3-2f. B. pick up the key key0-0 at the current position place f3-4f and loose the key key0-0 being held. C. pick up the key key0-0 at the current position place f1-1f and loose the key key0-0 being held. D. pick up the key key0-0 at the current position place f0-3f and loose the key key0-0 being held.", "choices": {"label": ["A", "B", "C", "D"], "text": ["put down key key0-0 at current position place f3-2f", "pick up the key key0-0 at the current position place f3-4f and loose the key key0-0 being held", "pick up the key key0-0 at the current position place f1-1f and loose the key key0-0 being held", "pick up the key key0-0 at the current position place f0-3f and loose the key key0-0 being held"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -2510633121753744706, "group": "reachable_action_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-3 is of shape shape0.  Currently, the robot is at position f1-3f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f0-2f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-0f has shape0 shaped lock. Key key0-1 is at position f4-2f. Key key0-3 is at position f3-1f. Key key0-2 is at position f3-0f. Key key0-0 is at position f0-0f.", "question": "Which of the following actions can eventually be applied? A. pick up key key0-1 at current position place f2-4f and loose key key0-1 being held. B. pick up key key0-3 at current position place f3-3f and loose key key0-3 being held. C. pick up key key0-1 at current position place f1-0f and loose key key0-1 being held. D. travel from the current position f1-1f to the next position f1-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up key key0-1 at current position place f2-4f and loose key key0-1 being held", "pick up key key0-3 at current position place f3-3f and loose key key0-3 being held", "pick up key key0-1 at current position place f1-0f and loose key key0-1 being held", "travel from the current position f1-1f to the next position f1-0f"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 4359517235859990187, "group": "reachable_action_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-3 is of shape shape0.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f0-2f has shape0 shaped lock. Key key0-2 is at position f4-0f. Key key0-0 is at position f0-1f. Key key0-1 is at position f1-4f. Key key0-3 is at position f0-1f.", "question": "Which of the following actions can eventually be applied? A. travel from the current position f2-4f to the next position f2-3f. B. pick up the key key0-2 at the current position place f1-0f and loose the key key0-2 being held. C. pick up the key key0-2 at the current position place f0-0f and loose the key key0-2 being held. D. pick up the key key0-1 at the current position place f1-4f and loose the key key0-1 being held.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel from the current position f2-4f to the next position f2-3f", "pick up the key key0-2 at the current position place f1-0f and loose the key key0-2 being held", "pick up the key key0-2 at the current position place f0-0f and loose the key key0-2 being held", "pick up the key key0-1 at the current position place f1-4f and loose the key key0-1 being held"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -6973482488865194474, "group": "reachable_action_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f1-1f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-1 is at position f1-2f. Key key0-2 is at position f4-3f. Key key0-0 is at position f2-2f.", "question": "Which of the following actions can eventually be applied? A. transition from the current position f0-2f to the next position f1-2f. B. pick up the key key0-0 at the current position place f0-0f and loose the key key0-0 being held. C. pick up the key key0-2 at the current position place f2-3f and loose the key key0-2 being held. D. pick up the key key0-2 at the current position place f3-4f and loose the key key0-2 being held.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transition from the current position f0-2f to the next position f1-2f", "pick up the key key0-0 at the current position place f0-0f and loose the key key0-0 being held", "pick up the key key0-2 at the current position place f2-3f and loose the key key0-2 being held", "pick up the key key0-2 at the current position place f3-4f and loose the key key0-2 being held"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 1833589868204437940, "group": "reachable_action_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f4-3f and is holding key0-1. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-0 is at position f2-2f. Key key0-2 is at position f1-4f.", "question": "Which of the following actions can eventually be applied? A. pick up the key key0-0 from the current position f1-1f and loose the key key0-0 which is being held. B. move from f3-3f to f3-4f. C. pick up the key key0-0 from the current position f0-2f and loose the key key0-0 which is being held. D. pick up the key key0-2 from the current position f2-1f and loose the key key0-2 which is being held.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up the key key0-0 from the current position f1-1f and loose the key key0-0 which is being held", "move from f3-3f to f3-4f", "pick up the key key0-0 from the current position f0-2f and loose the key key0-0 which is being held", "pick up the key key0-2 from the current position f2-1f and loose the key key0-2 which is being held"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 769909828929155165, "group": "reachable_action_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-3 is of shape shape0.  Currently, the robot is at position f1-3f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f0-2f has shape0 shaped lock. Key key0-2 is at position f4-0f. Key key0-1 is at position f1-2f. Key key0-0 is at position f0-1f. Key key0-3 is at position f0-1f.", "question": "Which of the following actions can eventually be applied? A. pick up the key key0-1 at the current position place f1-1f and loose the key key0-1 being held. B. transition from the current position f2-4f to the next position f3-4f. C. pick up the key key0-1 at the current position place f1-2f and loose the key key0-1 being held. D. pick up the key key0-0 at the current position place f4-4f and loose the key key0-0 being held.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up the key key0-1 at the current position place f1-1f and loose the key key0-1 being held", "transition from the current position f2-4f to the next position f3-4f", "pick up the key key0-1 at the current position place f1-2f and loose the key key0-1 being held", "pick up the key key0-0 at the current position place f4-4f and loose the key key0-0 being held"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 9085725697432284398, "group": "reachable_action_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f3-3f and is holding key0-0. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-1 is at position f1-2f. Key key0-2 is at position f3-3f.", "question": "Which of the following actions can eventually be applied? A. pick up key key0-0 at current position place f3-4f and loose key key0-0 being held. B. pick up key key0-1 at current position place f0-3f and loose key key0-1 being held. C. pick up key key0-2 at current position place f4-3f and loose key key0-2 being held. D. move from f3-2f to f4-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up key key0-0 at current position place f3-4f and loose key key0-0 being held", "pick up key key0-1 at current position place f0-3f and loose key key0-1 being held", "pick up key key0-2 at current position place f4-3f and loose key key0-2 being held", "move from f3-2f to f4-2f"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 6603850658646114359, "group": "reachable_action_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0, Key key0-2 is of shape shape0.  Currently, the robot is at position f4-4f and is holding key0-2. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-1 is at position f2-2f. Key key0-0 is at position f1-3f.", "question": "Which of the following actions can eventually be applied? A. pick up key key0-0 at current position place f0-2f and loose key key0-0 being held. B. move from f3-3f to f3-4f. C. pick up key key0-0 at current position place f1-0f and loose key key0-0 being held. D. pick up key key0-0 at current position place f0-0f and loose key key0-0 being held.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up key key0-0 at current position place f0-2f and loose key key0-0 being held", "move from f3-3f to f3-4f", "pick up key key0-0 at current position place f1-0f and loose key key0-0 being held", "pick up key key0-0 at current position place f0-0f and loose key key0-0 being held"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -6107359359615492169, "group": "reachable_action_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_8 is to the right of tile_7, and tile_6 is to the right of tile_5. Further, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_3 is down from tile_6, tile_2 is down from tile_5, and tile_4 is down from tile_7 Currently, robot robot1 is at tile_1 and holding color black and robot robot2 is at tile_3 and holding color white; tile_4, tile_7, tile_2, and tile_6 are clear; tile_8 is painted white, tile_9 is painted black, and tile_5 is painted black.", "question": "Which of the following actions can eventually be applied? A. move package robot1 up from tile tile_1 to tile tile_4. B. use truck robot1 to load the tile tile_6 downwards from the tile tile_9 with the color black. C. repaint the car robot2 from color black to color black. D. paint tile tile_1 down from tile tile_4 with color black using robot robot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move package robot1 up from tile tile_1 to tile tile_4", "use truck robot1 to load the tile tile_6 downwards from the tile tile_9 with the color black", "repaint the car robot2 from color black to color black", "paint tile tile_1 down from tile tile_4 with color black using robot robot1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 4370972626645746626, "group": "reachable_action_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_17 is to the right of tile_16, tile_19 is to the right of tile_18, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_5 is to the right of tile_4, tile_13 is to the right of tile_12, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_7 is to the right of tile_6, tile_4 is to the right of tile_3, tile_12 is to the right of tile_11, tile_8 is to the right of tile_7, tile_10 is to the right of tile_9, tile_14 is to the right of tile_13, tile_20 is to the right of tile_19, and tile_15 is to the right of tile_14. Further, tile_6 is down from tile_11, tile_13 is down from tile_18, tile_7 is down from tile_12, tile_14 is down from tile_19, tile_9 is down from tile_14, tile_12 is down from tile_17, tile_10 is down from tile_15, tile_2 is down from tile_7, tile_11 is down from tile_16, tile_8 is down from tile_13, tile_3 is down from tile_8, tile_1 is down from tile_6, tile_4 is down from tile_9, tile_15 is down from tile_20, and tile_5 is down from tile_10 Currently, robot robot2 is at tile_3 and holding color white and robot robot1 is at tile_4 and holding color white; tile_1, tile_5, and tile_2 are clear; tile_19 is painted black, tile_17 is painted black, tile_7 is painted black, tile_9 is painted black, tile_16 is painted white, tile_18 is painted white, tile_12 is painted white, tile_14 is painted white, tile_8 is painted white, tile_11 is painted black, tile_10 is painted white, tile_13 is painted black, tile_20 is painted white, tile_6 is painted white, and tile_15 is painted black.", "question": "Which of the following actions can eventually be applied? A. use truck robot1 to load the tile tile_20 above the tile tile_15 with the color white. B. use truck robot1 to load the tile tile_4 downwards from the tile tile_9 with the color black. C. move the robot robot2 from the tile tile_2 to the tile on its left tile_1. D. move package robot2 up from tile tile_13 to tile tile_18.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use truck robot1 to load the tile tile_20 above the tile tile_15 with the color white", "use truck robot1 to load the tile tile_4 downwards from the tile tile_9 with the color black", "move the robot robot2 from the tile tile_2 to the tile on its left tile_1", "move package robot2 up from tile tile_13 to tile tile_18"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -4395205674574061997, "group": "reachable_action_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 24 tiles. The tiles locations are: tile_3 is to the right of tile_2, tile_12 is to the right of tile_11, tile_14 is to the right of tile_13, tile_20 is to the right of tile_19, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_17 is to the right of tile_16, tile_21 is to the right of tile_20, tile_24 is to the right of tile_23, tile_5 is to the right of tile_4, tile_4 is to the right of tile_3, tile_8 is to the right of tile_7, tile_22 is to the right of tile_21, tile_15 is to the right of tile_14, tile_6 is to the right of tile_5, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_23 is to the right of tile_22, tile_10 is to the right of tile_9, and tile_16 is to the right of tile_15. Further, tile_12 is down from tile_18, tile_7 is down from tile_13, tile_17 is down from tile_23, tile_1 is down from tile_7, tile_16 is down from tile_22, tile_10 is down from tile_16, tile_8 is down from tile_14, tile_9 is down from tile_15, tile_2 is down from tile_8, tile_14 is down from tile_20, tile_15 is down from tile_21, tile_11 is down from tile_17, tile_3 is down from tile_9, tile_6 is down from tile_12, tile_4 is down from tile_10, tile_5 is down from tile_11, tile_18 is down from tile_24, and tile_13 is down from tile_19 Currently, robot robot1 is at tile_18 and holding color black and robot robot2 is at tile_9 and holding color black; tile_24, tile_14, tile_1, tile_11, tile_6, tile_3, tile_10, tile_8, tile_7, tile_15, tile_5, tile_22, tile_21, tile_2, tile_4, tile_16, tile_13, tile_12, and tile_17 are clear; tile_19 is painted white, tile_20 is painted black, and tile_23 is painted white.", "question": "Which of the following actions can eventually be applied? A. use truck robot1 to load the tile tile_3 downwards from the tile tile_9 with the color black. B. paint tile tile_6 down from tile tile_12 with color black using robot robot1. C. use truck robot1 to load the tile tile_2 downwards from the tile tile_8 with the color black. D. use truck robot1 to load the tile tile_5 downwards from the tile tile_11 with the color black.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use truck robot1 to load the tile tile_3 downwards from the tile tile_9 with the color black", "paint tile tile_6 down from tile tile_12 with color black using robot robot1", "use truck robot1 to load the tile tile_2 downwards from the tile tile_8 with the color black", "use truck robot1 to load the tile tile_5 downwards from the tile tile_11 with the color black"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 2115487770909795588, "group": "reachable_action_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_9 is to the right of tile_8, tile_11 is to the right of tile_10, tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_12 is to the right of tile_11, tile_8 is to the right of tile_7, and tile_6 is to the right of tile_5. Further, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_3 is down from tile_6, tile_9 is down from tile_12, tile_2 is down from tile_5, tile_7 is down from tile_10, and tile_4 is down from tile_7 Currently, robot robot1 is at tile_1 and holding color black and robot robot2 is at tile_7 and holding color white; tile_3, tile_4, and tile_2 are clear; tile_6 is painted white, tile_8 is painted white, tile_9 is painted black, tile_11 is painted black, tile_10 is painted white, tile_5 is painted black, and tile_12 is painted white.", "question": "Which of the following actions can eventually be applied? A. change the color of robot robot1 from color white to color black. B. use truck robot2 to load the tile tile_6 above the tile tile_3 with the color white. C. use truck robot2 to load the tile tile_12 above the tile tile_9 with the color white. D. use truck robot1 to load the tile tile_4 downwards from the tile tile_7 with the color white.", "choices": {"label": ["A", "B", "C", "D"], "text": ["change the color of robot robot1 from color white to color black", "use truck robot2 to load the tile tile_6 above the tile tile_3 with the color white", "use truck robot2 to load the tile tile_12 above the tile tile_9 with the color white", "use truck robot1 to load the tile tile_4 downwards from the tile tile_7 with the color white"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 6144148077600307382, "group": "reachable_action_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_9 is to the right of tile_8, tile_11 is to the right of tile_10, tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_12 is to the right of tile_11, tile_8 is to the right of tile_7, and tile_6 is to the right of tile_5. Further, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_3 is down from tile_6, tile_9 is down from tile_12, tile_2 is down from tile_5, tile_7 is down from tile_10, and tile_4 is down from tile_7 Currently, robot robot1 is at tile_2 and holding color white and robot robot2 is at tile_1 and holding color white; tile_3, tile_4, tile_5, tile_9, and tile_6 are clear; tile_7 is painted black, tile_10 is painted white, tile_11 is painted black, tile_8 is painted white, and tile_12 is painted white.", "question": "Which of the following actions can eventually be applied? A. paint the tile tile_5 above the tile tile_2 with color black using the robot robot1. B. move package robot2 up from tile tile_7 to tile tile_10. C. move the crate robot1 from the tile tile_11 to the tile tile_8 going downwards. D. use truck robot2 to load the tile tile_6 downwards from the tile tile_9 with the color black.", "choices": {"label": ["A", "B", "C", "D"], "text": ["paint the tile tile_5 above the tile tile_2 with color black using the robot robot1", "move package robot2 up from tile tile_7 to tile tile_10", "move the crate robot1 from the tile tile_11 to the tile tile_8 going downwards", "use truck robot2 to load the tile tile_6 downwards from the tile tile_9 with the color black"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 205097598182529424, "group": "reachable_action_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 24 tiles. The tiles locations are: tile_3 is to the right of tile_2, tile_12 is to the right of tile_11, tile_14 is to the right of tile_13, tile_20 is to the right of tile_19, tile_11 is to the right of tile_10, tile_2 is to the right of tile_1, tile_17 is to the right of tile_16, tile_21 is to the right of tile_20, tile_24 is to the right of tile_23, tile_5 is to the right of tile_4, tile_4 is to the right of tile_3, tile_8 is to the right of tile_7, tile_22 is to the right of tile_21, tile_15 is to the right of tile_14, tile_6 is to the right of tile_5, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_23 is to the right of tile_22, tile_10 is to the right of tile_9, and tile_16 is to the right of tile_15. Further, tile_12 is down from tile_18, tile_7 is down from tile_13, tile_17 is down from tile_23, tile_1 is down from tile_7, tile_16 is down from tile_22, tile_10 is down from tile_16, tile_8 is down from tile_14, tile_9 is down from tile_15, tile_2 is down from tile_8, tile_14 is down from tile_20, tile_15 is down from tile_21, tile_11 is down from tile_17, tile_3 is down from tile_9, tile_6 is down from tile_12, tile_4 is down from tile_10, tile_5 is down from tile_11, tile_18 is down from tile_24, and tile_13 is down from tile_19 Currently, robot robot2 is at tile_4 and holding color black and robot robot1 is at tile_21 and holding color white; tile_1, tile_11, tile_6, tile_3, tile_10, tile_7, tile_15, tile_5, tile_18, tile_2, tile_9, tile_16, tile_13, tile_12, and tile_17 are clear; tile_19 is painted white, tile_14 is painted white, tile_8 is painted black, tile_22 is painted black, tile_20 is painted black, tile_23 is painted white, and tile_24 is painted black.", "question": "Which of the following actions can eventually be applied? A. move crate robot2 from tile tile_20 to the right tile tile tile_21. B. move robot robot1 down from tile tile_16 to tile tile_10. C. use truck robot1 to load the tile tile_10 downwards from the tile tile_16 with the color black. D. move crate robot1 from tile tile_21 to the right tile tile tile_22.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move crate robot2 from tile tile_20 to the right tile tile tile_21", "move robot robot1 down from tile tile_16 to tile tile_10", "use truck robot1 to load the tile tile_10 downwards from the tile tile_16 with the color black", "move crate robot1 from tile tile_21 to the right tile tile tile_22"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 4688307094242821467, "group": "reachable_action_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_17 is to the right of tile_16, tile_19 is to the right of tile_18, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_5 is to the right of tile_4, tile_13 is to the right of tile_12, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_7 is to the right of tile_6, tile_4 is to the right of tile_3, tile_12 is to the right of tile_11, tile_8 is to the right of tile_7, tile_10 is to the right of tile_9, tile_14 is to the right of tile_13, tile_20 is to the right of tile_19, and tile_15 is to the right of tile_14. Further, tile_6 is down from tile_11, tile_13 is down from tile_18, tile_7 is down from tile_12, tile_14 is down from tile_19, tile_9 is down from tile_14, tile_12 is down from tile_17, tile_10 is down from tile_15, tile_2 is down from tile_7, tile_11 is down from tile_16, tile_8 is down from tile_13, tile_3 is down from tile_8, tile_1 is down from tile_6, tile_4 is down from tile_9, tile_15 is down from tile_20, and tile_5 is down from tile_10 Currently, robot robot2 is at tile_4 and holding color white and robot robot1 is at tile_6 and holding color white; tile_1, tile_14, tile_11, tile_3, tile_7, tile_19, tile_5, tile_2, and tile_9 are clear; tile_17 is painted black, tile_16 is painted white, tile_18 is painted white, tile_12 is painted white, tile_8 is painted white, tile_10 is painted white, tile_13 is painted black, tile_20 is painted white, and tile_15 is painted black.", "question": "Which of the following actions can eventually be applied? A. use truck robot1 to load the tile tile_15 above the tile tile_10 with the color black. B. move the crate robot2 from the tile tile_10 to the tile on its left tile_9. C. use truck robot2 to load the tile tile_7 above the tile tile_2 with the color white. D. navigate robot robot2 from tile tile_3 to tile tile_2 to its left.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use truck robot1 to load the tile tile_15 above the tile tile_10 with the color black", "move the crate robot2 from the tile tile_10 to the tile on its left tile_9", "use truck robot2 to load the tile tile_7 above the tile tile_2 with the color white", "navigate robot robot2 from tile tile_3 to tile tile_2 to its left"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -2902791944914678027, "group": "reachable_action_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_8 is to the right of tile_7, and tile_6 is to the right of tile_5. Further, tile_5 is down from tile_8, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_3 is down from tile_6, tile_2 is down from tile_5, and tile_4 is down from tile_7 Currently, robot robot1 is at tile_1 and holding color white and robot robot2 is at tile_2 and holding color black; is clear; tile_6 is painted white, tile_7 is painted black, tile_8 is painted white, tile_9 is painted black, tile_4 is painted white, and tile_5 is painted black.", "question": "Which of the following actions can eventually be applied? A. move the crate robot1 from tile tile_3 to tile tile_6 going upwards. B. use truck robot2 to load the tile tile_5 above the tile tile_2 with the color white. C. use truck robot2 to load the tile tile_1 downwards from the tile tile_4 with the color white. D. change the color of robot robot2 from color white to color black.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move the crate robot1 from tile tile_3 to tile tile_6 going upwards", "use truck robot2 to load the tile tile_5 above the tile tile_2 with the color white", "use truck robot2 to load the tile tile_1 downwards from the tile tile_4 with the color white", "change the color of robot robot2 from color white to color black"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 139725226631261315, "group": "reachable_action_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 3 robots and 20 tiles. The tiles locations are: tile_17 is to the right of tile_16, tile_19 is to the right of tile_18, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_5 is to the right of tile_4, tile_13 is to the right of tile_12, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_7 is to the right of tile_6, tile_4 is to the right of tile_3, tile_12 is to the right of tile_11, tile_8 is to the right of tile_7, tile_10 is to the right of tile_9, tile_14 is to the right of tile_13, tile_20 is to the right of tile_19, and tile_15 is to the right of tile_14. Further, tile_6 is down from tile_11, tile_13 is down from tile_18, tile_7 is down from tile_12, tile_14 is down from tile_19, tile_9 is down from tile_14, tile_12 is down from tile_17, tile_10 is down from tile_15, tile_2 is down from tile_7, tile_11 is down from tile_16, tile_8 is down from tile_13, tile_3 is down from tile_8, tile_1 is down from tile_6, tile_4 is down from tile_9, tile_15 is down from tile_20, and tile_5 is down from tile_10 Currently, robot robot3 is at tile_4 and holding color black, robot robot1 is at tile_2 and holding color white, and robot robot2 is at tile_13 and holding color white; tile_1, tile_11, tile_6, tile_3, tile_8, tile_7, tile_5, and tile_18 are clear; tile_19 is painted black, tile_17 is painted black, tile_9 is painted black, tile_16 is painted white, tile_12 is painted white, tile_14 is painted white, tile_10 is painted white, tile_20 is painted white, and tile_15 is painted black.", "question": "Which of the following actions can eventually be applied? A. use truck robot3 to load the tile tile_16 above the tile tile_11 with the color black. B. use truck robot2 to load the tile tile_13 above the tile tile_8 with the color black. C. use truck robot3 to load the tile tile_12 above the tile tile_7 with the color white. D. move the robot robot1 from the tile tile_3 to the tile on its left tile_2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use truck robot3 to load the tile tile_16 above the tile tile_11 with the color black", "use truck robot2 to load the tile tile_13 above the tile tile_8 with the color black", "use truck robot3 to load the tile tile_12 above the tile tile_7 with the color white", "move the robot robot1 from the tile tile_3 to the tile on its left tile_2"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -147718126209274476, "group": "reachable_action_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_17 is to the right of tile_16, tile_19 is to the right of tile_18, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_5 is to the right of tile_4, tile_13 is to the right of tile_12, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_7 is to the right of tile_6, tile_4 is to the right of tile_3, tile_12 is to the right of tile_11, tile_8 is to the right of tile_7, tile_10 is to the right of tile_9, tile_14 is to the right of tile_13, tile_20 is to the right of tile_19, and tile_15 is to the right of tile_14. Further, tile_6 is down from tile_11, tile_13 is down from tile_18, tile_7 is down from tile_12, tile_14 is down from tile_19, tile_9 is down from tile_14, tile_12 is down from tile_17, tile_10 is down from tile_15, tile_2 is down from tile_7, tile_11 is down from tile_16, tile_8 is down from tile_13, tile_3 is down from tile_8, tile_1 is down from tile_6, tile_4 is down from tile_9, tile_15 is down from tile_20, and tile_5 is down from tile_10 Currently, robot robot1 is at tile_5 and holding color white and robot robot2 is at tile_8 and holding color black; tile_1, tile_14, tile_11, tile_6, tile_3, tile_10, tile_7, tile_15, tile_19, tile_2, tile_9, tile_4, and tile_12 are clear; tile_17 is painted black, tile_16 is painted white, tile_18 is painted white, tile_13 is painted black, and tile_20 is painted white.", "question": "Which of the following actions can eventually be applied? A. use truck robot2 to load the tile tile_1 downwards from the tile tile_6 with the color white. B. paint the tile tile_1 down from tile tile_6 with color black using the robot robot2. C. navigate truck robot2 from city tile_19 to city tile_20. D. use truck robot1 to load the tile tile_10 above the tile tile_5 with the color black.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use truck robot2 to load the tile tile_1 downwards from the tile tile_6 with the color white", "paint the tile tile_1 down from tile tile_6 with color black using the robot robot2", "navigate truck robot2 from city tile_19 to city tile_20", "use truck robot1 to load the tile tile_10 above the tile tile_5 with the color black"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 5168208065347769991, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball11. Additionally, ball5, ball15, ball2, ball9, ball14, ball4, ball10, and ball6 are at room1, ball7 and ball8 are at room3, ball12, ball1, ball3, and ball13 are at room2.", "question": "Which of the following actions can eventually be applied? A. pick up the object room1 with robot robot1 using left1 gripper from room room2. B. pick up the object room2 with robot robot1 using right1 gripper from room room3. C. move robot robot1 from room room3 to room room2. D. use robot robot1 with right1 gripper to place the object room2 in room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up the object room1 with robot robot1 using left1 gripper from room room2", "pick up the object room2 with robot robot1 using right1 gripper from room room3", "move robot robot1 from room room3 to room room2", "use robot robot1 with right1 gripper to place the object room2 in room room3"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -552221678129268995, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball1 is at room4, ball3 is at room10, ball2 is at room2.", "question": "Which of the following actions can eventually be applied? A. transfer the robot robot1 from room room9 to room room4. B. place the object room9 in the room room7 from the left1 gripper of the robot robot1. C. use the robot robot1 equipped with left1 gripper to retrieve the object room3 from room room6. D. use the robot robot1 equipped with right1 gripper to retrieve the object room8 from room room1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transfer the robot robot1 from room room9 to room room4", "place the object room9 in the room room7 from the left1 gripper of the robot robot1", "use the robot robot1 equipped with left1 gripper to retrieve the object room3 from room room6", "use the robot robot1 equipped with right1 gripper to retrieve the object room8 from room room1"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -1386566616074454206, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball15. Additionally, ball5 and ball7 are at room3, ball2, ball9, ball14, ball8, ball4, ball11, ball10, and ball6 are at room1, ball12, ball1, ball3, and ball13 are at room2.", "question": "Which of the following actions can eventually be applied? A. use the right1 gripper of robot robot1 to drop the object left1 in room room1. B. use the right1 gripper of robot robot1 to drop the object room3 in room room1. C. use the robot robot1 equipped with left1 gripper to retrieve the object ball8 from room room1. D. use the right1 gripper of robot robot1 to drop the object room1 in room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the right1 gripper of robot robot1 to drop the object left1 in room room1", "use the right1 gripper of robot robot1 to drop the object room3 in room room1", "use the robot robot1 equipped with left1 gripper to retrieve the object ball8 from room room1", "use the right1 gripper of robot robot1 to drop the object room1 in room room2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 6997523119699466595, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball7. Additionally, ball6 and ball5 are at room2, ball2, ball4, and ball3 are at room1, ball1 is at room3.", "question": "Which of the following actions can eventually be applied? A. use the left1 gripper of robot robot1 to pick up the object right1 from room room3. B. use the right1 gripper of robot robot1 to pick up the object room3 from room room2. C. use the left1 gripper of robot robot1 to pick up the object room1 from room room3. D. transfer the robot robot1 from room room1 to room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the left1 gripper of robot robot1 to pick up the object right1 from room room3", "use the right1 gripper of robot robot1 to pick up the object room3 from room room2", "use the left1 gripper of robot robot1 to pick up the object room1 from room room3", "transfer the robot robot1 from room room1 to room room3"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 4403592822595862768, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball2 is at room1, ball3 is at room5, ball1 is at room2.", "question": "Which of the following actions can eventually be applied? A. use the right1 gripper of robot robot1 to pick up the object room5 from room room1. B. move the robot robot1 from room room5 to room room1. C. place the object room4 in the room room2 using the robot robot1 with right1 gripper. D. place the object left1 in the room room5 using the robot robot1 with right1 gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the right1 gripper of robot robot1 to pick up the object room5 from room room1", "move the robot robot1 from room room5 to room room1", "place the object room4 in the room room2 using the robot robot1 with right1 gripper", "place the object left1 in the room room5 using the robot robot1 with right1 gripper"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -6351308511618536989, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball5, and right gripper is carrying the ball ball3. Additionally, ball2, ball4, and ball1 are at room1, ball7 is at room3, ball6 is at room2.", "question": "Which of the following actions can eventually be applied? A. pick up the object room3 with the robot robot1 using the left1 gripper from the room room1. B. place the object room3 in the room room2 using the robot robot1 with left1 gripper. C. place the object room2 in the room room1 using the robot robot1 with right1 gripper. D. transfer the robot robot1 from room room3 to room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up the object room3 with the robot robot1 using the left1 gripper from the room room1", "place the object room3 in the room room2 using the robot robot1 with left1 gripper", "place the object room2 in the room room1 using the robot robot1 with right1 gripper", "transfer the robot robot1 from room room3 to room room2"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 907446152243268719, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball3. Additionally, ball2 is at room1, ball1 and ball4 are at room2.", "question": "Which of the following actions can eventually be applied? A. grasp the object ball1 from room room2 with the left1 gripper of robot robot1. B. use robot robot1 with right1 gripper to place the object left1 in room room1. C. use robot robot1 with left1 gripper to place the object right1 in room room2. D. grasp the object right1 from room room1 with the left1 gripper of robot robot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["grasp the object ball1 from room room2 with the left1 gripper of robot robot1", "use robot robot1 with right1 gripper to place the object left1 in room room1", "use robot robot1 with left1 gripper to place the object right1 in room room2", "grasp the object right1 from room room1 with the left1 gripper of robot robot1"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -8649513778453739, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball7. Additionally, ball6 and ball5 are at room2, ball2, ball4, and ball3 are at room1, ball1 is at room3.", "question": "Which of the following actions can eventually be applied? A. place the object room3 in the room room1 from the right1 gripper of the robot robot1. B. place the object right1 in the room room2 from the left1 gripper of the robot robot1. C. move robot robot1 from room room3 to room room1. D. grasp the object left1 from room room2 with the right1 gripper of robot robot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object room3 in the room room1 from the right1 gripper of the robot robot1", "place the object right1 in the room room2 from the left1 gripper of the robot robot1", "move robot robot1 from room room3 to room room1", "grasp the object left1 from room room2 with the right1 gripper of robot robot1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 3322071119175369768, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball10, and right gripper is carrying the ball ball12. Additionally, ball5, ball2, ball9, ball14, ball4, ball11, and ball6 are at room1, ball7 and ball8 are at room3, ball13, ball1, ball3, and ball15 are at room2.", "question": "Which of the following actions can eventually be applied? A. use the robot robot1 equipped with left1 gripper to retrieve the object room3 from room room1. B. use the robot robot1 equipped with left1 gripper to retrieve the object right1 from room room2. C. drop the object ball10 in the left1 gripper of the robot robot1 at the room room1. D. use the robot robot1 equipped with right1 gripper to retrieve the object room1 from room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the robot robot1 equipped with left1 gripper to retrieve the object room3 from room room1", "use the robot robot1 equipped with left1 gripper to retrieve the object right1 from room room2", "drop the object ball10 in the left1 gripper of the robot robot1 at the room room1", "use the robot robot1 equipped with right1 gripper to retrieve the object room1 from room room2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -4617601423969873452, "group": "reachable_action_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3, right gripper is free, and left gripper is carrying the ball ball7. Additionally, ball6 and ball5 are at room2, ball2, ball4, and ball3 are at room1, ball1 is at room3.", "question": "Which of the following actions can eventually be applied? A. move the robot robot1 from room room2 to room room3. B. use the robot robot1 equipped with left1 gripper to retrieve the object room2 from room room3. C. drop the object room3 in the right1 gripper of the robot robot1 at the room room1. D. use the robot robot1 equipped with right1 gripper to retrieve the object room1 from room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move the robot robot1 from room room2 to room room3", "use the robot robot1 equipped with left1 gripper to retrieve the object room2 from room room3", "drop the object room3 in the right1 gripper of the robot robot1 at the room room1", "use the robot robot1 equipped with right1 gripper to retrieve the object room1 from room room3"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 6487160052682438399, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint4 to waypoint1, waypoint1 to waypoint4. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0, waypoint4, and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2, waypoint3, waypoint1, and waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint4. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode colour. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following actions can eventually be applied? A. take an image of the objective objective0 in mode high_res using the camera camera1 on the rover rover0 from the waypoint waypoint4. B. collect a soil sample from waypoint waypoint1 using rover rover1 and deposit it in the store store1. C. calibrate the camera camera1 on the rover rover1 for the objective objective0 at waypoint waypoint1. D. communicate the image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["take an image of the objective objective0 in mode high_res using the camera camera1 on the rover rover0 from the waypoint waypoint4", "collect a soil sample from waypoint waypoint1 using rover rover1 and deposit it in the store store1", "calibrate the camera camera1 on the rover rover1 for the objective objective0 at waypoint waypoint1", "communicate the image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -7198711353834196187, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 and camera0 on board. Rover rover0 has camera2 on board. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective1. Camera camera2 supports low_res and colour. Camera camera1 supports colour. Camera camera0 supports colour and low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint5, waypoint6 to waypoint5, waypoint1 to waypoint5, waypoint0 to waypoint5, waypoint5 to waypoint0, waypoint4 to waypoint5, waypoint5 to waypoint4, waypoint3 to waypoint0, waypoint0 to waypoint3, waypoint5 to waypoint6, waypoint5 to waypoint1, waypoint5 to waypoint2. Rover rover1 can traverse from waypoint2 to waypoint4, waypoint4 to waypoint2, waypoint6 to waypoint0, waypoint5 to waypoint0, waypoint0 to waypoint3, waypoint1 to waypoint6, waypoint6 to waypoint1, waypoint3 to waypoint0, waypoint0 to waypoint6, waypoint0 to waypoint2, waypoint0 to waypoint5, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint3: waypoint2, waypoint6, waypoint1, waypoint5, waypoint0, and waypoint4. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, waypoint4, waypoint1, and waypoint5. Waypoint(s) are visible from waypoint5: waypoint0, waypoint4, waypoint3, waypoint2, waypoint6, and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2, waypoint6, waypoint5, and waypoint3. Waypoint(s) are visible from waypoint4: waypoint2, waypoint6, waypoint1, waypoint5, and waypoint3. Waypoint(s) are visible from waypoint6: waypoint3, waypoint4, waypoint1, waypoint5, and waypoint0. Waypoint(s) are visible from waypoint1: waypoint4, waypoint3, waypoint2, waypoint6, and waypoint5. Objective objective1 is visible from waypoint5, waypoint4, waypoint2, and waypoint0. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint5. Rocks can be sampled at the following location(s): waypoint4, waypoint6, and waypoint2. Soil can be sampled at the following location(s): waypoint1, waypoint4, waypoint6, and waypoint3. Rovers rover0 and rover1 are available. Image objective1 was communicated in mode high_res. Image objective1 was communicated in mode colour. Image objective0 was communicated in mode high_res. Rover rover1 has rock analyzed in waypoint waypoint3. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has image objective1 in mode colour. Rover rover1 has image objective0 in mode high_res. Rover rover1 has image objective1 in mode high_res. Rover rover1 has its camera camera0 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following actions can eventually be applied? A. communicate rock data from rover rover0 at waypoint waypoint4 about waypoint waypoint4 to lander general at waypoint waypoint3. B. sample the rock at waypoint waypoint1 with rover rover0 and store it in store store1. C. communicate soil data from rover rover0 at waypoint waypoint4 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint2. D. communicate soil data from rover rover0 at waypoint waypoint3 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate rock data from rover rover0 at waypoint waypoint4 about waypoint waypoint4 to lander general at waypoint waypoint3", "sample the rock at waypoint waypoint1 with rover rover0 and store it in store store1", "communicate soil data from rover rover0 at waypoint waypoint4 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint2", "communicate soil data from rover rover0 at waypoint waypoint3 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint5"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -7915196621233420275, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 and camera0 on board. Rover rover1 has camera2 on board. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports high_res and colour. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint2. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has its camera camera0 calibrated. Rover rover1 has its camera camera2 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following actions can eventually be applied? A. transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint0 through waypoint waypoint1. B. transmit soil data from rover rover0 located at waypoint waypoint0 to lander general located at waypoint waypoint2 using soil analysis of waypoint waypoint1. C. capture an image of objective objective0 in mode colour using the camera camera0 on the rover rover0 from waypoint waypoint1. D. sample the rock at waypoint waypoint2 with rover rover1 and store it in store store1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint0 through waypoint waypoint1", "transmit soil data from rover rover0 located at waypoint waypoint0 to lander general located at waypoint waypoint2 using soil analysis of waypoint waypoint1", "capture an image of objective objective0 in mode colour using the camera camera0 on the rover rover0 from waypoint waypoint1", "sample the rock at waypoint waypoint2 with rover rover1 and store it in store store1"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 3013667983380637275, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports high_res and low_res. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint1; Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Which of the following actions can eventually be applied? A. communicate the soil data from the rover rover1 at waypoint waypoint1 with the soil analysis of waypoint waypoint2 to the lander general at waypoint waypoint0. B. calibrate the camera camera0 on the rover rover1 for the objective objective1 at waypoint waypoint1. C. communicate the soil data from the rover rover1 at waypoint waypoint1 with the soil analysis of waypoint waypoint0 to the lander general at waypoint waypoint2. D. transmit image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate the soil data from the rover rover1 at waypoint waypoint1 with the soil analysis of waypoint waypoint2 to the lander general at waypoint waypoint0", "calibrate the camera camera0 on the rover rover1 for the objective objective1 at waypoint waypoint1", "communicate the soil data from the rover rover1 at waypoint waypoint1 with the soil analysis of waypoint waypoint0 to the lander general at waypoint waypoint2", "transmit image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -1882865811202470280, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 and camera0 on board. Rover rover1 has camera2 on board. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports high_res and colour. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint1. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective0 in mode low_res. Rover rover1 has image objective1 in mode colour. Store(s) store1 and store0 are full. ", "question": "Which of the following actions can eventually be applied? A. communicate rock data from rover rover0 at waypoint waypoint0 about waypoint waypoint2 to lander general at waypoint waypoint1. B. take an image of the objective objective0 in mode low_res using the camera camera2 on the rover rover0 from the waypoint waypoint1. C. take an image of the objective objective2 in mode high_res using the camera camera2 on the rover rover1 from the waypoint waypoint1. D. sample soil at waypoint waypoint1 with rover rover1 and store in store store0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate rock data from rover rover0 at waypoint waypoint0 about waypoint waypoint2 to lander general at waypoint waypoint1", "take an image of the objective objective0 in mode low_res using the camera camera2 on the rover rover0 from the waypoint waypoint1", "take an image of the objective objective2 in mode high_res using the camera camera2 on the rover rover1 from the waypoint waypoint1", "sample soil at waypoint waypoint1 with rover rover1 and store in store store0"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 6812860318522027324, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports high_res and low_res. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Soil data was communicated from waypoint waypoint1; Image objective1 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Which of the following actions can eventually be applied? A. communicate the image data of target objective0 in mode colour from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint0. B. sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store1. C. navigate the rover rover1 from waypoint waypoint0 to waypoint waypoint2. D. communicate the rock data from the rover rover0 at waypoint waypoint1 to the lander general at waypoint waypoint0 via waypoint waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate the image data of target objective0 in mode colour from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint0", "sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store1", "navigate the rover rover1 from waypoint waypoint0 to waypoint waypoint2", "communicate the rock data from the rover rover0 at waypoint waypoint1 to the lander general at waypoint waypoint0 via waypoint waypoint2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -2834564015319690960, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports high_res and low_res. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint2. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode low_res. Rover rover1 has image objective1 in mode low_res. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following actions can eventually be applied? A. move the rover rover1 from waypoint waypoint2 to waypoint waypoint1. B. collect a soil sample from waypoint waypoint0 using rover rover0 and deposit it in the store store0. C. communicate the soil data from rover rover1 at waypoint waypoint2 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1. D. sample a rock at waypoint waypoint2 using rover rover0, then store it in the storage unit store0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move the rover rover1 from waypoint waypoint2 to waypoint waypoint1", "collect a soil sample from waypoint waypoint0 using rover rover0 and deposit it in the store store0", "communicate the soil data from rover rover1 at waypoint waypoint2 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1", "sample a rock at waypoint waypoint2 using rover rover0, then store it in the storage unit store0"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -1272646515910504541, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports high_res and low_res. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint0. Rovers rover0 and rover1 are available. Image objective0 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Which of the following actions can eventually be applied? A. take a picture of the objective objective1 in mode colour using the camera camera1 mounted on the rover rover1 from the waypoint waypoint0. B. sample the rock at waypoint waypoint2 with rover rover1 and store it in store store1. C. sample the soil at waypoint waypoint0 using the rover rover1, then store it in the storage unit store0. D. guide the rover rover0 from waypoint waypoint2 to waypoint waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["take a picture of the objective objective1 in mode colour using the camera camera1 mounted on the rover rover1 from the waypoint waypoint0", "sample the rock at waypoint waypoint2 with rover rover1 and store it in store store1", "sample the soil at waypoint waypoint0 using the rover rover1, then store it in the storage unit store0", "guide the rover rover0 from waypoint waypoint2 to waypoint waypoint1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -6397785812748491525, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 and camera0 on board. Rover rover1 has camera2 on board. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports high_res and colour. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint0 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Store(s) store1 and store0 are full. ", "question": "Which of the following actions can eventually be applied? A. take an image of the objective objective2 in mode low_res using the camera camera2 on the rover rover0 from the waypoint waypoint2. B. navigate rover rover0 from waypoint waypoint0 to waypoint waypoint2. C. communicate the soil data from rover rover0 at waypoint waypoint1 with the soil analysis of waypoint waypoint2 to lander general at waypoint waypoint0. D. sample a rock at waypoint waypoint1 using rover rover1, then store it in the storage unit store0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["take an image of the objective objective2 in mode low_res using the camera camera2 on the rover rover0 from the waypoint waypoint2", "navigate rover rover0 from waypoint waypoint0 to waypoint waypoint2", "communicate the soil data from rover rover0 at waypoint waypoint1 with the soil analysis of waypoint waypoint2 to lander general at waypoint waypoint0", "sample a rock at waypoint waypoint1 using rover rover1, then store it in the storage unit store0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 1951659920792967428, "group": "reachable_action_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint4 to waypoint1, waypoint1 to waypoint4. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0, waypoint4, and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2, waypoint3, waypoint1, and waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Image objective0 was communicated in mode low_res. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following actions can eventually be applied? A. calibrate the camera camera0 on the rover rover1 for the objective objective1 at the waypoint waypoint3. B. sample soil at waypoint waypoint3 with rover rover0 and store in the store store1. C. communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint0. D. calibrate the camera camera0 on the rover rover0 for the objective objective0 at the waypoint waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["calibrate the camera camera0 on the rover rover1 for the objective objective1 at the waypoint waypoint3", "sample soil at waypoint waypoint3 with rover rover0 and store in the store store1", "communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint0", "calibrate the camera camera0 on the rover rover0 for the objective objective0 at the waypoint waypoint1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -6075815408890411444, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x0-y3, loc-x2-y3, loc-x1-y3, loc-x3-y0, loc-x3-y3, loc-x2-y0, loc-x2-y2, loc-x1-y2, loc-x3-y2, loc-x0-y1, loc-x2-y1, loc-x0-y2, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. push box to place loc-x2-y0 from place loc-x3-y0. B. push box to place loc-x0-y2 from place loc-x1-y2. C. push box to place loc-x2-y2 from place loc-x2-y3. D. move from place loc-x2-y2 to place loc-x3-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["push box to place loc-x2-y0 from place loc-x3-y0", "push box to place loc-x0-y2 from place loc-x1-y2", "push box to place loc-x2-y2 from place loc-x2-y3", "move from place loc-x2-y2 to place loc-x3-y2"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -3511075786723286968, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x2-y3, and loc-x0-y4. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x3-y1, loc-x3-y0, loc-x1-y0, loc-x2-y0, loc-x2-y2, loc-x0-y0, loc-x3-y2, loc-x0-y1, loc-x2-y1, loc-x0-y2, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. push box to place loc-x3-y0 from place loc-x2-y0. B. move from place loc-x3-y0 to place loc-x3-y1. C. push box to place loc-x2-y2 from place loc-x3-y2. D. push box to place loc-x3-y2 from place loc-x3-y3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["push box to place loc-x3-y0 from place loc-x2-y0", "move from place loc-x3-y0 to place loc-x3-y1", "push box to place loc-x2-y2 from place loc-x3-y2", "push box to place loc-x3-y2 from place loc-x3-y3"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 924450961983325525, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x3-y1.The following places have been visited: loc-x3-y1, loc-x3-y0, loc-x1-y0, loc-x2-y0, loc-x0-y0, loc-x0-y1, and loc-x0-y2.", "question": "Which of the following actions can eventually be applied? A. check that position loc-x2-y1 is connected to position loc-x3-y1. B. travel from loc-x3-y2 to loc-x3-y3. C. check that position loc-x0-y0 is connected to position loc-x1-y0. D. check that position loc-x1-y3 is connected to position loc-x1-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["check that position loc-x2-y1 is connected to position loc-x3-y1", "travel from loc-x3-y2 to loc-x3-y3", "check that position loc-x0-y0 is connected to position loc-x1-y0", "check that position loc-x1-y3 is connected to position loc-x1-y2"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -7147522314818090201, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x1-y0, loc-x0-y2, and loc-x3-y3. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x0-y3, loc-x2-y3, loc-x1-y3, loc-x3-y0, loc-x2-y2, loc-x1-y2, loc-x3-y2, loc-x0-y1, loc-x2-y1, loc-x2-y0, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. transition from the current position loc-x1-y2 to the next position loc-x1-y3. B. push box to place loc-x2-y1 from place loc-x1-y1. C. push box to place loc-x0-y1 from place loc-x0-y0. D. push box to place loc-x2-y1 from place loc-x2-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transition from the current position loc-x1-y2 to the next position loc-x1-y3", "push box to place loc-x2-y1 from place loc-x1-y1", "push box to place loc-x0-y1 from place loc-x0-y0", "push box to place loc-x2-y1 from place loc-x2-y0"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 969811954434223403, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x0-y4, loc-x3-y3, loc-x1-y4, loc-x2-y1, loc-x0-y3, loc-x1-y0, loc-x2-y4, loc-x2-y0, loc-x0-y2, loc-x1-y1, loc-x3-y0, loc-x1-y3, loc-x1-y2, loc-x0-y0, loc-x3-y2, loc-x0-y1, loc-x3-y1, and loc-x3-y4.", "question": "Which of the following actions can eventually be applied? A. move to place loc-x2-y0 from place loc-x3-y0. B. push box to place loc-x1-y4 from place loc-x0-y4. C. push box to place loc-x1-y0 from place loc-x1-y1. D. push box to place loc-x1-y1 from place loc-x0-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move to place loc-x2-y0 from place loc-x3-y0", "push box to place loc-x1-y4 from place loc-x0-y4", "push box to place loc-x1-y0 from place loc-x1-y1", "push box to place loc-x1-y1 from place loc-x0-y1"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 3878761865268448015, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x1-y2.The following places have been visited: loc-x3-y0, loc-x1-y2, loc-x2-y1, loc-x0-y1, loc-x2-y0, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. navigate from loc-x3-y3 to loc-x3-y2. B. push box to place loc-x2-y1 from place loc-x2-y2. C. push box to place loc-x2-y2 from place loc-x1-y2. D. push box to place loc-x0-y3 from place loc-x0-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate from loc-x3-y3 to loc-x3-y2", "push box to place loc-x2-y1 from place loc-x2-y2", "push box to place loc-x2-y2 from place loc-x1-y2", "push box to place loc-x0-y3 from place loc-x0-y2"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 8642872307201871194, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x0-y3, loc-x2-y3, loc-x1-y3, loc-x3-y0, loc-x3-y3, loc-x2-y0, loc-x2-y2, loc-x0-y0, loc-x3-y2, loc-x0-y1, loc-x2-y1, loc-x0-y2, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. check that position loc-x3-y2 is connected to position loc-x2-y2. B. check that position loc-x1-y3 is connected to position loc-x1-y2. C. travel from loc-x1-y1 to loc-x2-y1. D. check that position loc-x1-y1 is connected to position loc-x1-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["check that position loc-x3-y2 is connected to position loc-x2-y2", "check that position loc-x1-y3 is connected to position loc-x1-y2", "travel from loc-x1-y1 to loc-x2-y1", "check that position loc-x1-y1 is connected to position loc-x1-y2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 4342940561922644601, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x3-y1, loc-x3-y0, loc-x1-y3, loc-x1-y0, loc-x2-y0, loc-x1-y2, loc-x0-y0, loc-x0-y1, loc-x2-y1, loc-x0-y2, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. push box to place loc-x2-y4 from place loc-x3-y4. B. go to loc-x0-y4 from loc-x1-y4. C. push box to place loc-x2-y1 from place loc-x1-y1. D. push box to place loc-x1-y0 from place loc-x0-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["push box to place loc-x2-y4 from place loc-x3-y4", "go to loc-x0-y4 from loc-x1-y4", "push box to place loc-x2-y1 from place loc-x1-y1", "push box to place loc-x1-y0 from place loc-x0-y0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -6167993661751947168, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x1-y0, loc-x0-y2, and loc-x3-y3. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x3-y0, loc-x1-y3, loc-x2-y2, loc-x1-y2, loc-x2-y0, loc-x2-y1, loc-x2-y3, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. check that position loc-x1-y3 is connected to position loc-x2-y3. B. navigate from loc-x2-y2 to loc-x2-y1. C. check that position loc-x0-y0 is connected to position loc-x0-y1. D. check that position loc-x3-y0 is connected to position loc-x2-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["check that position loc-x1-y3 is connected to position loc-x2-y3", "navigate from loc-x2-y2 to loc-x2-y1", "check that position loc-x0-y0 is connected to position loc-x0-y1", "check that position loc-x3-y0 is connected to position loc-x2-y0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 62243796472324350, "group": "reachable_action_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x2-y3, and loc-x0-y4. Currently, the robot is in place loc-x0-y1.The following places have been visited: loc-x1-y0, loc-x0-y0, loc-x0-y1, loc-x0-y2, and loc-x1-y1.", "question": "Which of the following actions can eventually be applied? A. push box to place loc-x3-y2 from place loc-x3-y3. B. push box to place loc-x1-y1 from place loc-x2-y1. C. push box to place loc-x1-y0 from place loc-x2-y0. D. go to loc-x0-y0 from loc-x1-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["push box to place loc-x3-y2 from place loc-x3-y3", "push box to place loc-x1-y1 from place loc-x2-y1", "push box to place loc-x1-y0 from place loc-x2-y0", "go to loc-x0-y0 from loc-x1-y0"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 3749773197430832624, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 hoists, 3 crates, 2 trucks, 4 depots, 6 pallets, numbered consecutively. Currently, pallet4, pallet2, pallet1, pallet3, pallet5, and pallet0 are clear; hoist2, hoist0, hoist3, hoist1, hoist5, and hoist4 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at depot2, pallet5 is at distributor1, pallet3 is at depot3, hoist3 is at depot3, pallet4 is at distributor0, truck1 is at depot1, hoist0 is at depot0, hoist5 is at distributor1, pallet1 is at depot1, pallet2 is at depot2, truck0 is at depot1, and hoist4 is at distributor0; crate2 is in truck0, crate1 is in truck0, and crate0 is in truck1.", "question": "Which of the following actions can eventually be applied? A. drop crate crate2 from hoist hoist5 onto surface crate2 at place distributor1. B. navigate the truck truck0 from the place distributor0 to the place depot0. C. drop crate crate0 from hoist hoist0 onto surface crate0 at place depot0. D. drop crate crate0 from hoist hoist3 onto surface crate0 at place depot3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drop crate crate2 from hoist hoist5 onto surface crate2 at place distributor1", "navigate the truck truck0 from the place distributor0 to the place depot0", "drop crate crate0 from hoist hoist0 onto surface crate0 at place depot0", "drop crate crate0 from hoist hoist3 onto surface crate0 at place depot3"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -8538385564574127774, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 5 hoists, 2 crates, 2 trucks, 3 depots, 5 pallets, numbered consecutively. Currently, pallet4, pallet2, pallet3, crate0, and pallet0 are clear; hoist2, hoist0, hoist3, hoist1, and hoist4 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at depot2, truck0 is at depot2, pallet4 is at distributor1, hoist4 is at distributor1, hoist0 is at depot0, hoist3 is at distributor0, crate0 is at depot1, pallet3 is at distributor0, pallet1 is at depot1, truck1 is at distributor0, and pallet2 is at depot2; crate0 is on pallet1; crate1 is in truck0.", "question": "Which of the following actions can eventually be applied? A. lift the crate crate0 from the ground crate0 at position distributor0 using the hoist hoist3. B. drive the truck truck0 from depot1 to depot0. C. lift the crate crate1 from the ground crate1 at position distributor1 using the hoist hoist4. D. place the crate crate0 on the surface crate0 at the place depot0 using the hoist hoist0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["lift the crate crate0 from the ground crate0 at position distributor0 using the hoist hoist3", "drive the truck truck0 from depot1 to depot0", "lift the crate crate1 from the ground crate1 at position distributor1 using the hoist hoist4", "place the crate crate0 on the surface crate0 at the place depot0 using the hoist hoist0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 6127648645863522488, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 5 hoists, 2 crates, 2 trucks, 3 depots, 5 pallets, numbered consecutively. Currently, pallet4, pallet2, pallet3, crate0, and pallet0 are clear; hoist2, hoist0, hoist3, hoist1, and hoist4 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at depot2, pallet4 is at distributor1, hoist4 is at distributor1, truck1 is at depot2, hoist0 is at depot0, hoist3 is at distributor0, crate0 is at depot1, pallet3 is at distributor0, truck0 is at distributor1, pallet1 is at depot1, and pallet2 is at depot2; crate0 is on pallet1; crate1 is in truck0.", "question": "Which of the following actions can eventually be applied? A. drop the crate crate0 from the hoist hoist3 onto the surface crate0 at the place distributor0. B. lift the crate crate1 from the ground crate1 at position depot2 using the hoist hoist2. C. navigate the truck truck0 from place depot2 to place depot1. D. lift the crate crate1 from the ground crate1 at position distributor0 using the hoist hoist3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drop the crate crate0 from the hoist hoist3 onto the surface crate0 at the place distributor0", "lift the crate crate1 from the ground crate1 at position depot2 using the hoist hoist2", "navigate the truck truck0 from place depot2 to place depot1", "lift the crate crate1 from the ground crate1 at position distributor0 using the hoist hoist3"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -2607126123297445134, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 4 hoists, 2 crates, 2 trucks, 2 depots, 4 pallets, numbered consecutively. Currently, pallet2, pallet1, pallet3, and crate0 are clear; hoist2, hoist0, hoist3, and hoist1 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at distributor0, pallet3 is at distributor1, hoist3 is at distributor1, hoist0 is at depot0, truck0 is at depot0, pallet2 is at distributor0, pallet1 is at depot1, crate0 is at depot0, and truck1 is at distributor1; crate0 is on pallet0; crate1 is in truck0.", "question": "Which of the following actions can eventually be applied? A. lift the crate crate0 from the surface crate0 at place depot1 using the hoist hoist1. B. lift the crate crate0 from the surface crate0 at place distributor1 using the hoist hoist3. C. drop crate crate0 from hoist hoist1 onto surface crate0 at place depot1. D. drive the truck truck1 from distributor0 to depot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["lift the crate crate0 from the surface crate0 at place depot1 using the hoist hoist1", "lift the crate crate0 from the surface crate0 at place distributor1 using the hoist hoist3", "drop crate crate0 from hoist hoist1 onto surface crate0 at place depot1", "drive the truck truck1 from distributor0 to depot1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 836843583898960047, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 hoists, 3 crates, 2 trucks, 4 depots, 6 pallets, numbered consecutively. Currently, pallet4, pallet2, pallet1, pallet3, pallet5, and pallet0 are clear; hoist2, hoist0, hoist3, hoist5, and hoist4 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at depot2, pallet5 is at distributor1, pallet3 is at depot3, hoist3 is at depot3, pallet4 is at distributor0, hoist0 is at depot0, truck1 is at depot3, hoist5 is at distributor1, pallet1 is at depot1, pallet2 is at depot2, truck0 is at depot1, and hoist4 is at distributor0; crate1 is in truck0 and crate0 is in truck0; hoist1 is lifting crate2.", "question": "Which of the following actions can eventually be applied? A. lower the crate crate2 from the hoist hoist1 onto the surface crate2 at the place depot1. B. drive the truck truck1 from distributor1 to depot2. C. lower the crate crate1 from the hoist hoist2 onto the surface crate1 at the place depot2. D. lower the crate crate0 from the hoist hoist4 onto the surface crate0 at the place distributor0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["lower the crate crate2 from the hoist hoist1 onto the surface crate2 at the place depot1", "drive the truck truck1 from distributor1 to depot2", "lower the crate crate1 from the hoist hoist2 onto the surface crate1 at the place depot2", "lower the crate crate0 from the hoist hoist4 onto the surface crate0 at the place distributor0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 1685508010509923358, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 4 hoists, 2 crates, 2 trucks, 2 depots, 4 pallets, numbered consecutively. Currently, pallet2, pallet1, pallet3, and pallet0 are clear; hoist2, hoist0, hoist3, and hoist1 are available; hoist1 is at depot1, truck1 is at depot0, pallet0 is at depot0, hoist2 is at distributor0, pallet3 is at distributor1, hoist3 is at distributor1, hoist0 is at depot0, truck0 is at depot0, pallet2 is at distributor0, and pallet1 is at depot1; crate1 is in truck1 and crate0 is in truck0.", "question": "Which of the following actions can eventually be applied? A. place the crate crate0 on the surface crate0 at the place distributor0 using the hoist hoist2. B. place the crate crate0 on the surface crate0 at the place distributor1 using the hoist hoist3. C. lift crate crate1 from surface crate1 at place depot0 using hoist hoist0. D. navigate the truck truck0 from place depot1 to place distributor1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the crate crate0 on the surface crate0 at the place distributor0 using the hoist hoist2", "place the crate crate0 on the surface crate0 at the place distributor1 using the hoist hoist3", "lift crate crate1 from surface crate1 at place depot0 using hoist hoist0", "navigate the truck truck0 from place depot1 to place distributor1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -39542021048999593, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 4 hoists, 2 crates, 2 trucks, 2 depots, 4 pallets, numbered consecutively. Currently, crate1, pallet2, pallet1, and crate0 are clear; hoist2, hoist0, hoist3, and hoist1 are available; hoist1 is at depot1, truck1 is at depot0, pallet0 is at depot0, hoist2 is at distributor0, pallet3 is at distributor1, hoist3 is at distributor1, hoist0 is at depot0, truck0 is at depot0, pallet2 is at distributor0, crate1 is at distributor1, pallet1 is at depot1, and crate0 is at depot0; crate1 is on pallet3 and crate0 is on pallet0.", "question": "Which of the following actions can eventually be applied? A. drop crate crate1 from hoist hoist0 onto surface crate1 at place depot0. B. lift the crate crate0 from the ground crate0 at position depot0 using the hoist hoist0. C. lift the crate crate1 from the ground crate1 at position depot1 using the hoist hoist1. D. drive truck truck0 from place distributor1 to place depot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drop crate crate1 from hoist hoist0 onto surface crate1 at place depot0", "lift the crate crate0 from the ground crate0 at position depot0 using the hoist hoist0", "lift the crate crate1 from the ground crate1 at position depot1 using the hoist hoist1", "drive truck truck0 from place distributor1 to place depot1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 2892472738699186528, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 9 hoists, 2 crates, 2 trucks, 7 depots, 9 pallets, numbered consecutively. Currently, crate1, pallet6, pallet4, pallet7, pallet2, pallet1, pallet8, pallet5, and pallet0 are clear; hoist7, hoist2, hoist0, hoist3, hoist5, hoist6, hoist8, and hoist4 are available; hoist1 is at depot1, truck1 is at depot0, pallet0 is at depot0, hoist2 is at depot2, pallet5 is at depot5, pallet3 is at depot3, pallet6 is at depot6, hoist3 is at depot3, hoist5 is at depot5, hoist6 is at depot6, hoist4 is at depot4, hoist0 is at depot0, truck0 is at depot6, pallet7 is at distributor0, hoist7 is at distributor0, hoist8 is at distributor1, pallet1 is at depot1, pallet4 is at depot4, crate1 is at depot3, pallet8 is at distributor1, and pallet2 is at depot2; crate1 is on pallet3; hoist1 is lifting crate0.", "question": "Which of the following actions can eventually be applied? A. drop the crate crate0 from the hoist hoist2 onto the surface crate0 at the place depot2. B. navigate the truck truck0 from the place depot4 to the place depot1. C. lift the crate crate0 from the ground crate0 at position depot2 using the hoist hoist2. D. drop the crate crate0 from the hoist hoist3 onto the surface crate0 at the place depot3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drop the crate crate0 from the hoist hoist2 onto the surface crate0 at the place depot2", "navigate the truck truck0 from the place depot4 to the place depot1", "lift the crate crate0 from the ground crate0 at position depot2 using the hoist hoist2", "drop the crate crate0 from the hoist hoist3 onto the surface crate0 at the place depot3"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -3897161493366048111, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 12 hoists, 2 crates, 2 trucks, 10 depots, 12 pallets, numbered consecutively. Currently, pallet6, pallet4, pallet7, pallet11, pallet9, pallet10, pallet2, pallet1, pallet8, pallet3, pallet5, and pallet0 are clear; hoist7, hoist2, hoist0, hoist3, hoist1, hoist5, hoist10, hoist6, hoist8, and hoist4 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at depot2, hoist7 is at depot7, pallet7 is at depot7, hoist10 is at distributor0, pallet5 is at depot5, pallet3 is at depot3, pallet9 is at depot9, pallet10 is at distributor0, hoist9 is at depot9, hoist3 is at depot3, pallet6 is at depot6, hoist5 is at depot5, hoist6 is at depot6, truck0 is at depot4, pallet11 is at distributor1, hoist8 is at depot8, hoist4 is at depot4, hoist0 is at depot0, truck1 is at depot6, hoist11 is at distributor1, pallet1 is at depot1, pallet4 is at depot4, pallet8 is at depot8, and pallet2 is at depot2; hoist11 is lifting crate0 and hoist9 is lifting crate1.", "question": "Which of the following actions can eventually be applied? A. lower the crate crate1 from the hoist hoist9 and place it on the surface crate1 at location depot9. B. lower the crate crate1 from the hoist hoist10 and place it on the surface crate1 at location distributor0. C. lift crate crate1 from surface crate1 at place depot3 using hoist hoist3. D. drive the truck truck0 from depot8 to depot6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["lower the crate crate1 from the hoist hoist9 and place it on the surface crate1 at location depot9", "lower the crate crate1 from the hoist hoist10 and place it on the surface crate1 at location distributor0", "lift crate crate1 from surface crate1 at place depot3 using hoist hoist3", "drive the truck truck0 from depot8 to depot6"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 6903786467067770430, "group": "reachable_action_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 distributors, 6 hoists, 3 crates, 2 trucks, 4 depots, 6 pallets, numbered consecutively. Currently, pallet4, pallet2, pallet1, pallet3, pallet5, and pallet0 are clear; hoist2, hoist0, hoist3, hoist5, and hoist4 are available; hoist1 is at depot1, pallet0 is at depot0, hoist2 is at depot2, pallet5 is at distributor1, pallet3 is at depot3, hoist3 is at depot3, pallet4 is at distributor0, hoist0 is at depot0, truck0 is at distributor1, hoist5 is at distributor1, pallet1 is at depot1, truck1 is at distributor0, pallet2 is at depot2, and hoist4 is at distributor0; crate2 is in truck1 and crate1 is in truck1; hoist1 is lifting crate0.", "question": "Which of the following actions can eventually be applied? A. lower the crate crate1 from the hoist hoist3 and place it on the surface crate1 at location depot3. B. lower the crate crate2 from the hoist hoist2 and place it on the surface crate2 at location depot2. C. lift the crate crate0 from the ground crate0 at position depot3 using the hoist hoist3. D. unload the crate crate2 from the truck truck1 at the place depot0 using the hoist hoist0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["lower the crate crate1 from the hoist hoist3 and place it on the surface crate1 at location depot3", "lower the crate crate2 from the hoist hoist2 and place it on the surface crate2 at location depot2", "lift the crate crate0 from the ground crate0 at position depot3 using the hoist hoist3", "unload the crate crate2 from the truck truck1 at the place depot0 using the hoist hoist0"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 6765164008681333749, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and is holding gold. The following locations have hard rock: f1-2f, f1-3f, f3-3f, and f3-1f. The following locations have soft rock: f2-2f, f2-1f, f2-3f, and f3-2f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Which of the following actions can eventually be applied? A. move from location f1-0f to location f0-0f. B. pick up gold at location f2-1f. C. detonate bomb at loc f3-0f connected to loc f3-1f. D. detonate bomb at loc f2-1f connected to loc f3-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move from location f1-0f to location f0-0f", "pick up gold at location f2-1f", "detonate bomb at loc f3-0f connected to loc f3-1f", "detonate bomb at loc f2-1f connected to loc f3-1f"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -6318589795635451473, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-4f and is holding gold. The following locations have hard rock: f1-3f and f1-4f. The following locations have soft rock: f2-2f, f1-2f, f2-4f, f2-1f, and f2-3f. The gold is at f0-4f location. The laser is at f0-2f location.", "question": "Which of the following actions can eventually be applied? A. pick up gold at location f2-3f. B. move from location f0-1f to location f1-1f. C. pick up gold at location f2-2f. D. pick up gold at location f1-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up gold at location f2-3f", "move from location f0-1f to location f1-1f", "pick up gold at location f2-2f", "pick up gold at location f1-1f"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -8279183082541536469, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and is holding a laser. The following locations have hard rock: f1-3f, f1-1f, and f1-4f. The following locations have soft rock: f2-4f, f2-1f, f2-3f, and f2-2f. The gold is at f0-4f location.", "question": "Which of the following actions can eventually be applied? A. detonate the bomb at location f0-2f connected to location f1-1f. B. pick up gold at loc f0-1f. C. detonate the bomb at location f0-2f connected to location f1-3f. D. move to location f1-0f from location f1-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["detonate the bomb at location f0-2f connected to location f1-1f", "pick up gold at loc f0-1f", "detonate the bomb at location f0-2f connected to location f1-3f", "move to location f1-0f from location f1-1f"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -3220725258586485620, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-1f and is holding a laser. The following locations have hard rock: f0-2f. The following locations have soft rock: f1-2f, f2-1f, f2-3f, f2-2f, f0-3f, and f1-3f. The gold is at f0-3f location.", "question": "Which of the following actions can eventually be applied? A. pick up gold at location f0-0f. B. trigger the explosion of the bomb at location f1-1f, which is connected to location f2-0f. C. pick up the bomb at location f0-0f. D. trigger the explosion of the bomb at location f1-3f, which is connected to location f2-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up gold at location f0-0f", "trigger the explosion of the bomb at location f1-1f, which is connected to location f2-0f", "pick up the bomb at location f0-0f", "trigger the explosion of the bomb at location f1-3f, which is connected to location f2-0f"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -7505509187394772817, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-3f and f1-4f. The following locations have soft rock: f2-2f, f1-2f, f2-4f, f0-4f, f2-1f, and f2-3f. The gold is at f0-4f location. The laser is at f1-0f location.", "question": "Which of the following actions can eventually be applied? A. pick up the gold from location f1-2f. B. pick up the gold from location f2-4f. C. travel from location f1-0f to location f2-0f. D. detonate the bomb at location f1-2f connected to location f0-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up the gold from location f1-2f", "pick up the gold from location f2-4f", "travel from location f1-0f to location f2-0f", "detonate the bomb at location f1-2f connected to location f0-0f"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -280935932686001427, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and its arm is empty. The following locations have hard rock: f0-3f, f2-1f, and f2-2f. The following locations have soft rock: f2-3f, f0-2f, and f1-3f. The gold is at f1-3f location. The laser is at f0-0f location.", "question": "Which of the following actions can eventually be applied? A. retrieve gold from location f0-2f. B. retrieve gold from location f2-3f. C. detonate bomb at loc f1-1f connected to loc f2-0f. D. move from loc f0-1f to loc f0-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["retrieve gold from location f0-2f", "retrieve gold from location f2-3f", "detonate bomb at loc f1-1f connected to loc f2-0f", "move from loc f0-1f to loc f0-0f"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -106146135377521312, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f1-2f, f1-3f, f3-3f, f1-1f, and f3-1f. The following locations have soft rock: f2-2f, f0-3f, f2-3f, and f3-2f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Which of the following actions can eventually be applied? A. pick up gold at loc f3-3f. B. pick up the laser at loc f0-0f. C. detonate bomb at loc f0-2f connected to loc f3-0f. D. detonate bomb at loc f0-0f connected to loc f1-3f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up gold at loc f3-3f", "pick up the laser at loc f0-0f", "detonate bomb at loc f0-2f connected to loc f3-0f", "detonate bomb at loc f0-0f connected to loc f1-3f"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 3694754428459184945, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and its arm is empty. The following locations have hard rock: f0-2f. The following locations have soft rock: f1-2f, f2-1f, f2-3f, f2-2f, f0-3f, and f1-3f. The gold is at f0-3f location. The laser is at f1-1f location.", "question": "Which of the following actions can eventually be applied? A. detonate bomb at loc f2-1f connected to loc f2-0f. B. pick up the gold from location f1-1f. C. pick up the gold from location f2-1f. D. move from location f0-0f to location f1-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["detonate bomb at loc f2-1f connected to loc f2-0f", "pick up the gold from location f1-1f", "pick up the gold from location f2-1f", "move from location f0-0f to location f1-0f"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 7926207302220157418, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and is holding gold. The following locations have hard rock: f0-3f, f2-1f, and f2-2f. The following locations have soft rock: f2-3f and f0-2f. The gold is at f1-3f location. The laser is at f0-0f location.", "question": "Which of the following actions can eventually be applied? A. pick up gold at location f0-3f. B. detonate bomb at loc f0-2f connected to loc f2-0f. C. move from location f1-2f to location f1-3f. D. detonate bomb at loc f1-3f connected to loc f1-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up gold at location f0-3f", "detonate bomb at loc f0-2f connected to loc f2-0f", "move from location f1-2f to location f1-3f", "detonate bomb at loc f1-3f connected to loc f1-0f"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -5506508396710483997, "group": "reachable_action_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a laser. The following locations have hard rock: f1-3f and f1-4f. The following locations have soft rock: f0-4f, f1-2f, f2-4f, f2-1f, f2-3f, and f2-2f. The gold is at f0-4f location.", "question": "Which of the following actions can eventually be applied? A. trigger the explosion of the bomb at location f1-4f, which is connected to location f0-0f. B. pick up the gold from location f1-3f. C. move from loc f1-1f to loc f0-1f. D. pick up the gold from location f0-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["trigger the explosion of the bomb at location f1-4f, which is connected to location f0-0f", "pick up the gold from location f1-3f", "move from loc f1-1f to loc f0-1f", "pick up the gold from location f0-0f"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -7817342779877117274, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation3, star2, phenomenon6, star0, planet5, groundstation4, groundstation1. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite2 has following instruments onboard: instrument4. Satellite satellite1 has following instruments onboard: instrument3. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation3. Satellite satellite2 is pointing to planet5. Satellite satellite1 is pointing to groundstation3. Power is available on the following satellite(s): satellite1. Following instruments are powered on: instrument2, instrument4. Following instruments are calibrated: instrument4. A spectrograph0 mode image of target phenomenon6 is available. ", "question": "Which of the following actions can eventually be applied? A. turn instrument instrument2 on satellite satellite0 in direction star0. B. move cargo on satellite satellite2 from star2 to groundstation1. C. turn the satellite satellite1 to the direction star0 from the direction groundstation4. D. move cargo on satellite satellite0 from phenomenon6 to star2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["turn instrument instrument2 on satellite satellite0 in direction star0", "move cargo on satellite satellite2 from star2 to groundstation1", "turn the satellite satellite1 to the direction star0 from the direction groundstation4", "move cargo on satellite satellite0 from phenomenon6 to star2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 7511944160542481652, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation3, groundstation0, star4, planet5, star2, planet6, groundstation1. There are 3 image mode(s): infrared2, thermograph0, image1. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode infrared2 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation3. Following instruments are powered on: instrument0. Following instruments are calibrated: instrument0. A thermograph0 mode image of target planet6 is available. A infrared2 mode image of target planet6 is available. ", "question": "Which of the following actions can eventually be applied? A. transfer weight on satellite satellite0 from section planet5 to section groundstation0. B. transfer weight on satellite satellite0 from section star4 to section star2. C. capture an image in direction star4 in mode infrared2 using the instrument instrument0 on the satellite satellite0. D. turn instrument instrument0 on satellite satellite0 in direction star2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transfer weight on satellite satellite0 from section planet5 to section groundstation0", "transfer weight on satellite satellite0 from section star4 to section star2", "capture an image in direction star4 in mode infrared2 using the instrument instrument0 on the satellite satellite0", "turn instrument instrument0 on satellite satellite0 in direction star2"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": 8394254463183242922, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation3, planet7, star10, planet8, star9, groundstation1, groundstation4, groundstation6, star0, star2, groundstation5. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite2 has following instruments onboard: instrument5, instrument4, instrument3. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument6 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to star10. Satellite satellite1 is pointing to planet7. Satellite satellite5 is pointing to star9. Satellite satellite3 is pointing to star9. Satellite satellite2 is pointing to star0. Power is available on the following satellite(s): satellite3, satellite4, satellite1, satellite2, satellite5. Following instruments are powered on: instrument0. ", "question": "Which of the following actions can eventually be applied? A. change the direction of the satellite satellite0 from star0 to groundstation5. B. transfer weight on satellite satellite0 from section planet8 to section star0. C. transfer weight on satellite satellite4 from section groundstation5 to section planet7. D. transfer weight on satellite satellite0 from section groundstation3 to section star10.", "choices": {"label": ["A", "B", "C", "D"], "text": ["change the direction of the satellite satellite0 from star0 to groundstation5", "transfer weight on satellite satellite0 from section planet8 to section star0", "transfer weight on satellite satellite4 from section groundstation5 to section planet7", "transfer weight on satellite satellite0 from section groundstation3 to section star10"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -41648535468020841, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, phenomenon5, star3, star1, groundstation4, groundstation2. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument5, instrument3. Satellite satellite6 has following instruments onboard: instrument15, instrument17, instrument16. Satellite satellite4 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite5 has following instruments onboard: instrument14, instrument13, instrument12. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument3 supports image of mode image2 and its calibration target is star3. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite6 is pointing to groundstation0. Satellite satellite5 is pointing to groundstation0. Satellite satellite2 is pointing to star6. Satellite satellite0 is pointing to groundstation2. Satellite satellite3 is pointing to star6. Satellite satellite1 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Power is available on the following satellite(s): satellite0, satellite6, satellite3, satellite4, satellite1, satellite5. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. ", "question": "Which of the following actions can eventually be applied? A. move cargo on satellite satellite5 from star6 to groundstation2. B. move cargo on satellite satellite1 from groundstation0 to star6. C. turn instrument instrument6 on satellite satellite2 in direction star6. D. direct the satellite satellite2 to point in the direction star3 instead of star1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move cargo on satellite satellite5 from star6 to groundstation2", "move cargo on satellite satellite1 from groundstation0 to star6", "turn instrument instrument6 on satellite satellite2 in direction star6", "direct the satellite satellite2 to point in the direction star3 instead of star1"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": -2096029908515956907, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation3, planet7, star10, planet8, star9, groundstation1, groundstation4, groundstation6, star0, star2, groundstation5. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite2 has following instruments onboard: instrument5, instrument4, instrument3. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument6 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to groundstation5. Satellite satellite1 is pointing to planet8. Satellite satellite3 is pointing to star9. Satellite satellite2 is pointing to star0. Satellite satellite5 is pointing to groundstation4. Power is available on the following satellite(s): satellite0, satellite3, satellite4, satellite2, satellite5. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. A infrared1 mode image of target star10 is available. A infrared1 mode image of target planet8 is available. ", "question": "Which of the following actions can eventually be applied? A. point the satellite satellite3 to direction star0 instead of star10. B. transfer weight on satellite satellite0 from section groundstation3 to section groundstation5. C. install the camera instrument0 on the satellite satellite0 to face direction star0. D. transfer weight on satellite satellite4 from section planet8 to section groundstation1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["point the satellite satellite3 to direction star0 instead of star10", "transfer weight on satellite satellite0 from section groundstation3 to section groundstation5", "install the camera instrument0 on the satellite satellite0 to face direction star0", "transfer weight on satellite satellite4 from section planet8 to section groundstation1"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 8796916095734866221, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation3, groundstation0, star4, planet5, star2, planet6, groundstation1. There are 3 image mode(s): infrared2, thermograph0, image1. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode infrared2 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to planet5. Following instruments are powered on: instrument0. Following instruments are calibrated: instrument0. A thermograph0 mode image of target planet5 is available. A thermograph0 mode image of target groundstation3 is available. ", "question": "Which of the following actions can eventually be applied? A. change the direction of the satellite satellite0 from star4 to star2. B. move cargo on satellite satellite0 from planet6 to planet5. C. fire laser in direction groundstation3 in mode thermograph0 using the instrument instrument0 on satellite satellite0. D. move cargo on satellite satellite0 from star4 to groundstation0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["change the direction of the satellite satellite0 from star4 to star2", "move cargo on satellite satellite0 from planet6 to planet5", "fire laser in direction groundstation3 in mode thermograph0 using the instrument instrument0 on satellite satellite0", "move cargo on satellite satellite0 from star4 to groundstation0"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -5345401931712996754, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, phenomenon5, star3, star1, groundstation4, groundstation2. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument5, instrument3. Satellite satellite6 has following instruments onboard: instrument15, instrument17, instrument16. Satellite satellite4 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite5 has following instruments onboard: instrument14, instrument13, instrument12. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument3 supports image of mode image2 and its calibration target is star3. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite5 is pointing to groundstation0. Satellite satellite3 is pointing to star6. Satellite satellite0 is pointing to groundstation2. Satellite satellite2 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite4 is pointing to phenomenon5. Satellite satellite6 is pointing to groundstation0. Power is available on the following satellite(s): satellite0, satellite6, satellite3, satellite4, satellite1, satellite5. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. ", "question": "Which of the following actions can eventually be applied? A. turn instrument instrument10 on satellite satellite4 in direction groundstation0. B. turn the satellite satellite3 to the direction groundstation0 from the direction phenomenon5. C. turn instrument instrument2 on satellite satellite0 in direction star3. D. turn instrument instrument16 on satellite satellite6 in direction groundstation0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["turn instrument instrument10 on satellite satellite4 in direction groundstation0", "turn the satellite satellite3 to the direction groundstation0 from the direction phenomenon5", "turn instrument instrument2 on satellite satellite0 in direction star3", "turn instrument instrument16 on satellite satellite6 in direction groundstation0"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -3455087545663791697, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation3, planet7, star10, planet8, star9, groundstation1, groundstation4, groundstation6, star0, star2, groundstation5. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite2 has following instruments onboard: instrument5, instrument4, instrument3. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument6 supports image of mode infrared1 and its calibration target is star0. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to star10. Satellite satellite1 is pointing to groundstation1. Satellite satellite5 is pointing to groundstation5. Satellite satellite3 is pointing to star9. Satellite satellite2 is pointing to star0. Power is available on the following satellite(s): satellite0, satellite4, satellite1, satellite2, satellite5. Following instruments are powered on: instrument6. ", "question": "Which of the following actions can eventually be applied? A. move cargo on satellite satellite5 from star9 to star2. B. move cargo on satellite satellite3 from planet8 to star0. C. turn instrument instrument5 on satellite satellite2 in direction planet8. D. turn the satellite satellite5 from direction star2 to direction star0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move cargo on satellite satellite5 from star9 to star2", "move cargo on satellite satellite3 from planet8 to star0", "turn instrument instrument5 on satellite satellite2 in direction planet8", "turn the satellite satellite5 from direction star2 to direction star0"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 4055420724985207862, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, phenomenon5, star3, star1, groundstation4, groundstation2. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument5, instrument3. Satellite satellite6 has following instruments onboard: instrument15, instrument17, instrument16. Satellite satellite4 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite5 has following instruments onboard: instrument14, instrument13, instrument12. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument3 supports image of mode image2 and its calibration target is star3. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite6 is pointing to groundstation0. Satellite satellite2 is pointing to phenomenon5. Satellite satellite5 is pointing to phenomenon5. Satellite satellite3 is pointing to star6. Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Power is available on the following satellite(s): satellite0, satellite6, satellite3, satellite4, satellite5. Following instruments are powered on: instrument5, instrument6. Following instruments are calibrated: instrument6. ", "question": "Which of the following actions can eventually be applied? A. turn the satellite satellite6 to the direction phenomenon5 from the direction star3. B. transfer weight on satellite satellite3 from section phenomenon5 to section star1. C. transfer weight on satellite satellite0 from section star1 to section groundstation0. D. transfer weight on satellite satellite5 from section star6 to section star6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["turn the satellite satellite6 to the direction phenomenon5 from the direction star3", "transfer weight on satellite satellite3 from section phenomenon5 to section star1", "transfer weight on satellite satellite0 from section star1 to section groundstation0", "transfer weight on satellite satellite5 from section star6 to section star6"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 4778183966846948507, "group": "reachable_action_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, phenomenon5, star3, star1, groundstation4, groundstation2. There are 3 image mode(s): image2, image0, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument5, instrument3. Satellite satellite6 has following instruments onboard: instrument15, instrument17, instrument16. Satellite satellite4 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite5 has following instruments onboard: instrument14, instrument13, instrument12. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument3 supports image of mode image2 and its calibration target is star3. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument2 supports image of mode image2 and its calibration target is star1. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument5 supports image of mode image2 and its calibration target is star3. Instrument instrument10 supports image of mode image2 and its calibration target is groundstation4. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite5 is pointing to groundstation0. Satellite satellite4 is pointing to groundstation0. Satellite satellite0 is pointing to groundstation2. Satellite satellite2 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite3 is pointing to groundstation0. Satellite satellite6 is pointing to star6. Power is available on the following satellite(s): satellite0, satellite3, satellite1, satellite2, satellite4, satellite5. Following instruments are powered on: instrument16. Following instruments are calibrated: instrument16. A image1 mode image of target star6 is available. ", "question": "Which of the following actions can eventually be applied? A. fire laser in direction groundstation4 in mode image1 using the instrument instrument17 on satellite satellite6. B. fire laser in direction star3 in mode image2 using the instrument instrument15 on satellite satellite6. C. transfer weight on satellite satellite5 from section groundstation2 to section groundstation4. D. turn satellite satellite0 to point from groundstation4 direction to star3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["fire laser in direction groundstation4 in mode image1 using the instrument instrument17 on satellite satellite6", "fire laser in direction star3 in mode image2 using the instrument instrument15 on satellite satellite6", "transfer weight on satellite satellite5 from section groundstation2 to section groundstation4", "turn satellite satellite0 to point from groundstation4 direction to star3"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 2300972001521773338, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: xena, heidi, michelle, dave, vic, zoe, alice, and carol. There are 8 items/roles: iceskates, frisbee, quadcopter, necklace, zebra, slinky, whale, and guitar. Currently, zoe is assigned whale, heidi is assigned iceskates, michelle is assigned zebra, alice is assigned necklace, carol is assigned frisbee, dave is assigned guitar, vic is assigned quadcopter, and xena is assigned slinky.", "question": "Which of the following actions can eventually be applied? A. exchange whale of vic with zebra of dave. B. exchange zebra of xena with necklace of xena. C. exchange necklace of alice with frisbee of alice. D. exchange whale of dave with zebra of dave.", "choices": {"label": ["A", "B", "C", "D"], "text": ["exchange whale of vic with zebra of dave", "exchange zebra of xena with necklace of xena", "exchange necklace of alice with frisbee of alice", "exchange whale of dave with zebra of dave"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": 5363384699282263561, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: xena, ted, heidi, bob, dave, kevin, and alice. There are 7 items/roles: parsnip, quince, yam, ulluco, leek, mushroom, and valerian. Currently, heidi is assigned yam, dave is assigned valerian, xena is assigned quince, ted is assigned mushroom, alice is assigned ulluco, bob is assigned parsnip, and kevin is assigned leek.", "question": "Which of the following actions can eventually be applied? A. exchange ulluco of xena with leek of xena. B. exchange ulluco of kevin with valerian of kevin. C. exchange parsnip of bob with ulluco of ted. D. exchange mushroom of dave with parsnip of dave.", "choices": {"label": ["A", "B", "C", "D"], "text": ["exchange ulluco of xena with leek of xena", "exchange ulluco of kevin with valerian of kevin", "exchange parsnip of bob with ulluco of ted", "exchange mushroom of dave with parsnip of dave"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -2712966604549046117, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: xena, ted, heidi, bob, dave, kevin, and alice. There are 7 items/roles: parsnip, quince, yam, ulluco, leek, mushroom, and valerian. Currently, xena is assigned valerian, heidi is assigned leek, dave is assigned yam, kevin is assigned mushroom, ted is assigned quince, alice is assigned ulluco, and bob is assigned parsnip.", "question": "Which of the following actions can eventually be applied? A. trade ulluco of bob for leek of bob. B. trade parsnip of dave for valerian of ted. C. trade quince of heidi for ulluco of heidi. D. trade mushroom of bob for yam of bob.", "choices": {"label": ["A", "B", "C", "D"], "text": ["trade ulluco of bob for leek of bob", "trade parsnip of dave for valerian of ted", "trade quince of heidi for ulluco of heidi", "trade mushroom of bob for yam of bob"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 1789100020401752326, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: xena, quentin, bob, frank, vic, and liam. There are 6 items/roles: knead, sander, wrench, ratchet, pliers, and nibbler. Currently, frank is assigned sander, vic is assigned knead, liam is assigned pliers, bob is assigned ratchet, xena is assigned wrench, and quentin is assigned nibbler.", "question": "Which of the following actions can eventually be applied? A. swap vic:nibbler with vic:sander. B. swap frank:knead with xena:wrench. C. swap liam:pliers with liam:wrench. D. swap frank:wrench with frank:sander.", "choices": {"label": ["A", "B", "C", "D"], "text": ["swap vic:nibbler with vic:sander", "swap frank:knead with xena:wrench", "swap liam:pliers with liam:wrench", "swap frank:wrench with frank:sander"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 7647918843487028999, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: xena, ted, heidi, bob, dave, kevin, and alice. There are 7 items/roles: parsnip, quince, yam, ulluco, leek, mushroom, and valerian. Currently, bob is assigned valerian, xena is assigned parsnip, ted is assigned mushroom, alice is assigned yam, dave is assigned quince, kevin is assigned leek, and heidi is assigned ulluco.", "question": "Which of the following actions can eventually be applied? A. swap ted with alice, mushroom for ulluco. B. swap kevin with kevin, yam for quince. C. swap ted with ted, valerian for quince. D. swap heidi with heidi, yam for mushroom.", "choices": {"label": ["A", "B", "C", "D"], "text": ["swap ted with alice, mushroom for ulluco", "swap kevin with kevin, yam for quince", "swap ted with ted, valerian for quince", "swap heidi with heidi, yam for mushroom"]}, "query": "Which action is reachable from this state?", "answer": "A"}
{"id": -2987946268267597438, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: xena, heidi, michelle, dave, vic, zoe, alice, and carol. There are 8 items/roles: iceskates, frisbee, quadcopter, necklace, zebra, slinky, whale, and guitar. Currently, zoe is assigned whale, vic is assigned necklace, carol is assigned guitar, michelle is assigned zebra, heidi is assigned quadcopter, alice is assigned iceskates, dave is assigned frisbee, and xena is assigned slinky.", "question": "Which of the following actions can eventually be applied? A. exchange slinky of vic with zebra of vic. B. exchange guitar of heidi with frisbee of heidi. C. exchange quadcopter of heidi with frisbee of carol. D. exchange zebra of dave with slinky of dave.", "choices": {"label": ["A", "B", "C", "D"], "text": ["exchange slinky of vic with zebra of vic", "exchange guitar of heidi with frisbee of heidi", "exchange quadcopter of heidi with frisbee of carol", "exchange zebra of dave with slinky of dave"]}, "query": "Which action is reachable from this state?", "answer": "C"}
{"id": -4862122697158698648, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: xena, ted, heidi, bob, dave, kevin, and alice. There are 7 items/roles: parsnip, quince, yam, ulluco, leek, mushroom, and valerian. Currently, bob is assigned quince, alice is assigned parsnip, dave is assigned valerian, kevin is assigned yam, ted is assigned ulluco, xena is assigned leek, and heidi is assigned mushroom.", "question": "Which of the following actions can eventually be applied? A. swap ted:ulluco with ted:parsnip. B. swap xena:leek with ted:parsnip. C. swap heidi:ulluco with heidi:valerian. D. swap xena:quince with xena:mushroom.", "choices": {"label": ["A", "B", "C", "D"], "text": ["swap ted:ulluco with ted:parsnip", "swap xena:leek with ted:parsnip", "swap heidi:ulluco with heidi:valerian", "swap xena:quince with xena:mushroom"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": -1355629327753333971, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: xena, heidi, michelle, dave, vic, zoe, alice, and carol. There are 8 items/roles: iceskates, frisbee, quadcopter, necklace, zebra, slinky, whale, and guitar. Currently, dave is assigned slinky, xena is assigned frisbee, heidi is assigned zebra, michelle is assigned necklace, vic is assigned guitar, carol is assigned whale, zoe is assigned iceskates, and alice is assigned quadcopter.", "question": "Which of the following actions can eventually be applied? A. exchange slinky of xena with necklace of xena. B. exchange iceskates of vic with quadcopter of vic. C. exchange frisbee of michelle with iceskates of michelle. D. exchange whale of carol with slinky of xena.", "choices": {"label": ["A", "B", "C", "D"], "text": ["exchange slinky of xena with necklace of xena", "exchange iceskates of vic with quadcopter of vic", "exchange frisbee of michelle with iceskates of michelle", "exchange whale of carol with slinky of xena"]}, "query": "Which action is reachable from this state?", "answer": "D"}
{"id": 9039276937033102247, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: xena, heidi, michelle, dave, vic, zoe, alice, and carol. There are 8 items/roles: iceskates, frisbee, quadcopter, necklace, zebra, slinky, whale, and guitar. Currently, dave is assigned slinky, zoe is assigned whale, carol is assigned guitar, michelle is assigned zebra, alice is assigned necklace, heidi is assigned frisbee, xena is assigned iceskates, and vic is assigned quadcopter.", "question": "Which of the following actions can eventually be applied? A. exchange necklace of zoe with whale of zoe. B. exchange guitar of xena with slinky of alice. C. exchange guitar of dave with zebra of dave. D. exchange necklace of xena with zebra of xena.", "choices": {"label": ["A", "B", "C", "D"], "text": ["exchange necklace of zoe with whale of zoe", "exchange guitar of xena with slinky of alice", "exchange guitar of dave with zebra of dave", "exchange necklace of xena with zebra of xena"]}, "query": "Which action is reachable from this state?", "answer": "B"}
{"id": 8457124189181021587, "group": "reachable_action_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: xena, quentin, bob, frank, vic, and liam. There are 6 items/roles: knead, sander, wrench, ratchet, pliers, and nibbler. Currently, quentin is assigned sander, frank is assigned nibbler, vic is assigned knead, liam is assigned pliers, bob is assigned ratchet, and xena is assigned wrench.", "question": "Which of the following actions can eventually be applied? A. trade sander of quentin for knead of quentin. B. trade knead of vic for wrench of vic. C. trade sander of bob for ratchet of vic. D. trade sander of frank for wrench of frank.", "choices": {"label": ["A", "B", "C", "D"], "text": ["trade sander of quentin for knead of quentin", "trade knead of vic for wrench of vic", "trade sander of bob for ratchet of vic", "trade sander of frank for wrench of frank"]}, "query": "Which action is reachable from this state?", "answer": "C"}
