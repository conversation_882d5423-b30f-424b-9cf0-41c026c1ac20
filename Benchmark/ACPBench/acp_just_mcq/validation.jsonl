{"id": -9031846171017339656, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 5 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2, c1, c3, and c0 are at l0; c4 is at l1. The goal is to reach a state where the following facts hold: Car c4 is at location l0, Car c0 is at location l1, Car c2 is at location l1, Car c1 is at location l0, and Car c3 is at location l0.", "question": "Given the plan: \"board car c3 at location l0, unload the car c3 from the ferry to location l0, board car c0 at location l0, travel by sea from location l0 to location l1, unload the car c0 from the ferry to location l1, board car c4 at location l1, unload the car c4 from the ferry to location l1, travel by sea from location l1 to location l0, board car c2 at location l0, travel by sea from location l0 to location l1, unload the car c2 from the ferry to location l1, board car c4 at location l1, travel by sea from location l1 to location l0, unload the car c4 from the ferry to location l0\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. travel by sea from location l1 to location l0 and board car c2 at location l0. B. board car c3 at location l0 and unload the car c3 from the ferry to location l0. C. travel by sea from location l0 to location l1 and unload the car c0 from the ferry to location l1. D. board car c2 at location l0 and travel by sea from location l0 to location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel by sea from location l1 to location l0 and board car c2 at location l0", "board car c3 at location l0 and unload the car c3 from the ferry to location l0", "travel by sea from location l0 to location l1 and unload the car c0 from the ferry to location l1", "board car c2 at location l0 and travel by sea from location l0 to location l1"]}, "query": "Given the plan: \"board car c3 at location l0, unload the car c3 from the ferry to location l0, board car c0 at location l0, travel by sea from location l0 to location l1, unload the car c0 from the ferry to location l1, board car c4 at location l1, unload the car c4 from the ferry to location l1, travel by sea from location l1 to location l0, board car c2 at location l0, travel by sea from location l0 to location l1, unload the car c2 from the ferry to location l1, board car c4 at location l1, travel by sea from location l1 to location l0, unload the car c4 from the ferry to location l0\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": -6499695625485002841, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 2 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0 and c1 are at l1. The goal is to reach a state where the following facts hold: Car c1 is at location l2 and Car c0 is at location l0.", "question": "Given the plan: \"board car c1 at location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l1, travel by sea from location l1 to location l2, debark the car c1 to location l2 from the ferry, travel by sea from location l2 to location l1, board car c0 at location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l0, debark the car c0 to location l0 from the ferry\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. travel by sea from location l2 to location l1 and travel by sea from location l1 to location l2. B. travel by sea from location l2 to location l1 and board car c0 at location l1. C. board car c0 at location l1 and travel by sea from location l1 to location l2. D. travel by sea from location l2 to location l0 and debark the car c0 to location l0 from the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel by sea from location l2 to location l1 and travel by sea from location l1 to location l2", "travel by sea from location l2 to location l1 and board car c0 at location l1", "board car c0 at location l1 and travel by sea from location l1 to location l2", "travel by sea from location l2 to location l0 and debark the car c0 to location l0 from the ferry"]}, "query": "Given the plan: \"board car c1 at location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l1, travel by sea from location l1 to location l2, debark the car c1 to location l2 from the ferry, travel by sea from location l2 to location l1, board car c0 at location l1, travel by sea from location l1 to location l2, travel by sea from location l2 to location l0, debark the car c0 to location l0 from the ferry\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": 3903123391386162053, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 2 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c1 and c0 are at l0. The goal is to reach a state where the following facts hold: Car c0 is at location l1 and Car c1 is at location l1.", "question": "Given the plan: \"board the car c0 at the location l0, travel by sea from location l0 to location l1, unload the car c0 from the ferry to location l1, travel by sea from location l1 to location l0, board the car c1 at the location l0, travel by sea from location l0 to location l1, unload the car c1 from the ferry to location l1, board the car c0 at the location l1, unload the car c0 from the ferry to location l1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. board the car c0 at the location l0 and travel by sea from location l0 to location l1. B. unload the car c1 from the ferry to location l1 and board the car c0 at the location l1. C. travel by sea from location l0 to location l1 and unload the car c1 from the ferry to location l1. D. board the car c0 at the location l1 and unload the car c0 from the ferry to location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["board the car c0 at the location l0 and travel by sea from location l0 to location l1", "unload the car c1 from the ferry to location l1 and board the car c0 at the location l1", "travel by sea from location l0 to location l1 and unload the car c1 from the ferry to location l1", "board the car c0 at the location l1 and unload the car c0 from the ferry to location l1"]}, "query": "Given the plan: \"board the car c0 at the location l0, travel by sea from location l0 to location l1, unload the car c0 from the ferry to location l1, travel by sea from location l1 to location l0, board the car c1 at the location l0, travel by sea from location l0 to location l1, unload the car c1 from the ferry to location l1, board the car c0 at the location l1, unload the car c0 from the ferry to location l1\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": 3456808734159078218, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2, c8, c1, c9, c3, c0, and c6 are at l0; c7, c4, and c5 are at l1. The goal is to reach a state where the following facts hold: Car c9 is at location l1, Car c3 is at location l1, Car c4 is at location l0, Car c5 is at location l0, Car c2 is at location l1, Car c1 is at location l0, Car c7 is at location l0, Car c8 is at location l0, Car c0 is at location l0, and Car c6 is at location l0.", "question": "Given the plan: \"load the car c2 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c2 from the ferry to location l1, load the car c2 at location l1 on to the ferry, debark the car c2 from the ferry to location l1, load the car c2 at location l1 on to the ferry, debark the car c2 from the ferry to location l1, load the car c4 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c4 from the ferry to location l0, load the car c3 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c3 from the ferry to location l1, load the car c5 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c5 from the ferry to location l0, load the car c9 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c9 from the ferry to location l1, load the car c7 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c7 from the ferry to location l0\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. load the car c2 at location l1 on to the ferry and debark the car c2 from the ferry to location l1. B. debark the car c4 from the ferry to location l0 and load the car c3 at location l0 on to the ferry. C. load the car c2 at location l0 on to the ferry and sail from location l0 to location l1. D. load the car c5 at location l1 on to the ferry and sail from location l1 to location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load the car c2 at location l1 on to the ferry and debark the car c2 from the ferry to location l1", "debark the car c4 from the ferry to location l0 and load the car c3 at location l0 on to the ferry", "load the car c2 at location l0 on to the ferry and sail from location l0 to location l1", "load the car c5 at location l1 on to the ferry and sail from location l1 to location l0"]}, "query": "Given the plan: \"load the car c2 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c2 from the ferry to location l1, load the car c2 at location l1 on to the ferry, debark the car c2 from the ferry to location l1, load the car c2 at location l1 on to the ferry, debark the car c2 from the ferry to location l1, load the car c4 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c4 from the ferry to location l0, load the car c3 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c3 from the ferry to location l1, load the car c5 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c5 from the ferry to location l0, load the car c9 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c9 from the ferry to location l1, load the car c7 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c7 from the ferry to location l0\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": 4153259526725623630, "group": "action_justification_mcq", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 2 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0 and c1 are at l1. The goal is to reach a state where the following facts hold: Car c1 is at location l2 and Car c0 is at location l0.", "question": "Given the plan: \"board car c1 at location l1, sail from location l1 to location l2, debark the car c1 from the ferry to location l2, sail from location l2 to location l1, board car c0 at location l1, sail from location l1 to location l2, debark the car c0 from the ferry to location l2, board car c0 at location l2, sail from location l2 to location l0, debark the car c0 from the ferry to location l0, sail from location l0 to location l1\"; which of the following actions can be removed from this plan and still have a valid plan? A. board car c1 at location l1. B. debark the car c0 from the ferry to location l0. C. debark the car c1 from the ferry to location l2. D. sail from location l0 to location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["board car c1 at location l1", "debark the car c0 from the ferry to location l0", "debark the car c1 from the ferry to location l2", "sail from location l0 to location l1"]}, "query": "Given the plan: \"board car c1 at location l1, sail from location l1 to location l2, debark the car c1 from the ferry to location l2, sail from location l2 to location l1, board car c0 at location l1, sail from location l1 to location l2, debark the car c0 from the ferry to location l2, board car c0 at location l2, sail from location l2 to location l0, debark the car c0 from the ferry to location l0, sail from location l0 to location l1\"; which action can be removed from this plan?", "answer": "D"}
{"id": -8020230305126874419, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-1, and l0-2 are in c0; l1-1, l1-2, and l1-0 are in c1. Currently, p3, t1, and p0 are at l1-2, p1 is at l1-1, t0 is at l0-2, a0 is at l0-0, p2 is at l1-0. The goal is to reach a state where the following facts hold: p1 is at l1-2, p0 is at l0-2, p3 is at l0-1, and p2 is at l1-2.", "question": "Given the plan: \"place the object p0 into the truck t1 at location l1-2, place the object p3 into the truck t1 at location l1-2, navigate the truck t1 from location l1-2 in city c1 to location l1-0 in the same city, offload the object p0 from the truck t1 at location l1-0, place the object p2 into the truck t1 at location l1-0, offload the object p3 from the truck t1 at location l1-0, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, place the object p1 into the truck t1 at location l1-1, fly the airplane a0 from the airport l0-0 to the airport l1-0, load object p0 into airplane a0 at location l1-0, load object p3 into airplane a0 at location l1-0, fly the airplane a0 from the airport l1-0 to the airport l0-0, remove the object p0 from the airplane a0 and place it on the location l0-0, remove the object p3 from the airplane a0 and place it on the location l0-0, navigate the truck t0 from location l0-2 in city c0 to location l0-0 in the same city, place the object p0 into the truck t0 at location l0-0, place the object p3 into the truck t0 at location l0-0, navigate the truck t0 from location l0-0 in city c0 to location l0-1 in the same city, offload the object p3 from the truck t0 at location l0-1, navigate the truck t0 from location l0-1 in city c0 to location l0-2 in the same city, offload the object p0 from the truck t0 at location l0-2, offload the object p1 from the truck t1 at location l1-1, place the object p1 into the truck t1 at location l1-1, navigate the truck t1 from location l1-1 in city c1 to location l1-2 in the same city, offload the object p2 from the truck t1 at location l1-2, navigate the truck t1 from location l1-2 in city c1 to location l1-1 in the same city, offload the object p1 from the truck t1 at location l1-1, place the object p1 into the truck t1 at location l1-1, navigate the truck t1 from location l1-1 in city c1 to location l1-2 in the same city, offload the object p1 from the truck t1 at location l1-2\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. offload the object p1 from the truck t1 at location l1-1 and place the object p1 into the truck t1 at location l1-1. B. fly the airplane a0 from the airport l1-0 to the airport l0-0 and remove the object p0 from the airplane a0 and place it on the location l0-0. C. offload the object p3 from the truck t0 at location l0-1 and navigate the truck t0 from location l0-1 in city c0 to location l0-2 in the same city. D. offload the object p0 from the truck t0 at location l0-2 and offload the object p1 from the truck t1 at location l1-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["offload the object p1 from the truck t1 at location l1-1 and place the object p1 into the truck t1 at location l1-1", "fly the airplane a0 from the airport l1-0 to the airport l0-0 and remove the object p0 from the airplane a0 and place it on the location l0-0", "offload the object p3 from the truck t0 at location l0-1 and navigate the truck t0 from location l0-1 in city c0 to location l0-2 in the same city", "offload the object p0 from the truck t0 at location l0-2 and offload the object p1 from the truck t1 at location l1-1"]}, "query": "Given the plan: \"place the object p0 into the truck t1 at location l1-2, place the object p3 into the truck t1 at location l1-2, navigate the truck t1 from location l1-2 in city c1 to location l1-0 in the same city, offload the object p0 from the truck t1 at location l1-0, place the object p2 into the truck t1 at location l1-0, offload the object p3 from the truck t1 at location l1-0, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, place the object p1 into the truck t1 at location l1-1, fly the airplane a0 from the airport l0-0 to the airport l1-0, load object p0 into airplane a0 at location l1-0, load object p3 into airplane a0 at location l1-0, fly the airplane a0 from the airport l1-0 to the airport l0-0, remove the object p0 from the airplane a0 and place it on the location l0-0, remove the object p3 from the airplane a0 and place it on the location l0-0, navigate the truck t0 from location l0-2 in city c0 to location l0-0 in the same city, place the object p0 into the truck t0 at location l0-0, place the object p3 into the truck t0 at location l0-0, navigate the truck t0 from location l0-0 in city c0 to location l0-1 in the same city, offload the object p3 from the truck t0 at location l0-1, navigate the truck t0 from location l0-1 in city c0 to location l0-2 in the same city, offload the object p0 from the truck t0 at location l0-2, offload the object p1 from the truck t1 at location l1-1, place the object p1 into the truck t1 at location l1-1, navigate the truck t1 from location l1-1 in city c1 to location l1-2 in the same city, offload the object p2 from the truck t1 at location l1-2, navigate the truck t1 from location l1-2 in city c1 to location l1-1 in the same city, offload the object p1 from the truck t1 at location l1-1, place the object p1 into the truck t1 at location l1-1, navigate the truck t1 from location l1-1 in city c1 to location l1-2 in the same city, offload the object p1 from the truck t1 at location l1-2\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": 3987370885247159070, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-1 and l0-0 are in c0. Currently, p0 and t1 are at l1-1, p3, p1, and p2 are at l1-0, t0 is at l0-1, a0 is at l0-0. The goal is to reach a state where the following facts hold: p3 is at l0-1, p0 is at l0-0, p1 is at l1-0, and p2 is at l1-0.", "question": "Given the plan: \"drive truck t0 from location l0-1 in city c0 to location l0-0 in the same city, fly the airplane a0 from airport l0-0 to airport l1-0, load the object p3 from location l1-0 into the airplane a0, load the object p0 from location l1-1 into the truck t1, drive truck t1 from location l1-1 in city c1 to location l1-0 in the same city, unload the object p0 from the truck t1 at location l1-0, load the object p0 from location l1-0 into the airplane a0, fly the airplane a0 from airport l1-0 to airport l0-0, remove the object p0 from the airplane a0 and place it on the location l0-0, remove the object p3 from the airplane a0 and place it on the location l0-0, load the object p3 from location l0-0 into the truck t0, drive truck t0 from location l0-0 in city c0 to location l0-1 in the same city, unload the object p3 from the truck t0 at location l0-1, fly the airplane a0 from airport l0-0 to airport l1-0\"; which of the following actions can be removed from this plan and still have a valid plan? A. fly the airplane a0 from airport l0-0 to airport l1-0. B. fly the airplane a0 from airport l1-0 to airport l0-0. C. remove the object p0 from the airplane a0 and place it on the location l0-0. D. load the object p3 from location l0-0 into the truck t0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["fly the airplane a0 from airport l0-0 to airport l1-0", "fly the airplane a0 from airport l1-0 to airport l0-0", "remove the object p0 from the airplane a0 and place it on the location l0-0", "load the object p3 from location l0-0 into the truck t0"]}, "query": "Given the plan: \"drive truck t0 from location l0-1 in city c0 to location l0-0 in the same city, fly the airplane a0 from airport l0-0 to airport l1-0, load the object p3 from location l1-0 into the airplane a0, load the object p0 from location l1-1 into the truck t1, drive truck t1 from location l1-1 in city c1 to location l1-0 in the same city, unload the object p0 from the truck t1 at location l1-0, load the object p0 from location l1-0 into the airplane a0, fly the airplane a0 from airport l1-0 to airport l0-0, remove the object p0 from the airplane a0 and place it on the location l0-0, remove the object p3 from the airplane a0 and place it on the location l0-0, load the object p3 from location l0-0 into the truck t0, drive truck t0 from location l0-0 in city c0 to location l0-1 in the same city, unload the object p3 from the truck t0 at location l0-1, fly the airplane a0 from airport l0-0 to airport l1-0\"; which action can be removed from this plan?", "answer": "A"}
{"id": 5692758605880000032, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-1, and l0-2 are in c0; l1-1, l1-2, and l1-0 are in c1. Currently, p0 and a0 are at l0-0, t1 is at l1-2, p1 is at l1-1, p2 is at l0-1, p3 and t0 are at l0-2. The goal is to reach a state where the following facts hold: p1 is at l1-1, p2 is at l1-0, p3 is at l1-2, and p0 is at l1-1.", "question": "Given the plan: \"navigate the truck t1 which is in location l1-2 in city c1 to another location l1-0 in the same city, place the object p3 into the truck t0 at location l0-2, navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city, place the object p2 into the truck t0 at location l0-1, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city, unload object p3 from truck t0 at location l0-0, unload object p2 from truck t0 at location l0-0, place the object p2 onto the airplane a0 at location l0-0, place the object p0 into the truck t0 at location l0-0, unload object p0 from truck t0 at location l0-0, place the object p0 onto the airplane a0 at location l0-0, place the object p3 onto the airplane a0 at location l0-0, fly the airplane a0 from the airport l0-0 to the airport l1-0, offload the object p0 from the airplane a0 at location l1-0, offload the object p3 from the airplane a0 at location l1-0, place the object p0 into the truck t1 at location l1-0, offload the object p2 from the airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-1 in the same city, unload object p0 from truck t1 at location l1-1, navigate the truck t1 which is in location l1-1 in city c1 to another location l1-2 in the same city, unload object p3 from truck t1 at location l1-2\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. place the object p3 onto the airplane a0 at location l0-0 and fly the airplane a0 from the airport l0-0 to the airport l1-0. B. place the object p3 into the truck t0 at location l0-2 and navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city. C. place the object p0 into the truck t0 at location l0-0 and unload object p0 from truck t0 at location l0-0. D. navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city and place the object p2 into the truck t0 at location l0-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object p3 onto the airplane a0 at location l0-0 and fly the airplane a0 from the airport l0-0 to the airport l1-0", "place the object p3 into the truck t0 at location l0-2 and navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city", "place the object p0 into the truck t0 at location l0-0 and unload object p0 from truck t0 at location l0-0", "navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city and place the object p2 into the truck t0 at location l0-1"]}, "query": "Given the plan: \"navigate the truck t1 which is in location l1-2 in city c1 to another location l1-0 in the same city, place the object p3 into the truck t0 at location l0-2, navigate the truck t0 which is in location l0-2 in city c0 to another location l0-1 in the same city, place the object p2 into the truck t0 at location l0-1, navigate the truck t0 which is in location l0-1 in city c0 to another location l0-0 in the same city, unload object p3 from truck t0 at location l0-0, unload object p2 from truck t0 at location l0-0, place the object p2 onto the airplane a0 at location l0-0, place the object p0 into the truck t0 at location l0-0, unload object p0 from truck t0 at location l0-0, place the object p0 onto the airplane a0 at location l0-0, place the object p3 onto the airplane a0 at location l0-0, fly the airplane a0 from the airport l0-0 to the airport l1-0, offload the object p0 from the airplane a0 at location l1-0, offload the object p3 from the airplane a0 at location l1-0, place the object p0 into the truck t1 at location l1-0, offload the object p2 from the airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-1 in the same city, unload object p0 from truck t1 at location l1-1, navigate the truck t1 which is in location l1-1 in city c1 to another location l1-2 in the same city, unload object p3 from truck t1 at location l1-2\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": -8083289540892591022, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l1-0 and l1-1 are in c1; l0-1 and l0-0 are in c0. Currently, p0, p3, and t0 are at l0-0, p2 is at l1-1, a0 and t1 are at l1-0, p1 is at l0-1. The goal is to reach a state where the following facts hold: p1 is at l0-1, p0 is at l0-1, p3 is at l0-0, and p2 is at l0-0.", "question": "Given the plan: \"drive the truck t1 in city c1 from location l1-0 to location l1-1, load object p2 into truck t1 at location l1-1, drive the truck t1 in city c1 from location l1-1 to location l1-0, remove the object p2 from the truck t1 and place it on the location l1-0, load object p2 into airplane a0 at location l1-0, unload object p2 from airplane a0 at location l1-0, load object p2 into airplane a0 at location l1-0, load object p0 into truck t0 at location l0-0, operate the airplane a0 from airport l1-0 to airport l0-0, unload object p2 from airplane a0 at location l0-0, load object p2 into truck t0 at location l0-0, remove the object p2 from the truck t0 and place it on the location l0-0, drive the truck t0 in city c0 from location l0-0 to location l0-1, remove the object p0 from the truck t0 and place it on the location l0-1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. load object p2 into airplane a0 at location l1-0 and unload object p2 from airplane a0 at location l1-0. B. unload object p2 from airplane a0 at location l0-0 and load object p2 into truck t0 at location l0-0. C. drive the truck t1 in city c1 from location l1-0 to location l1-1 and load object p2 into truck t1 at location l1-1. D. load object p0 into truck t0 at location l0-0 and operate the airplane a0 from airport l1-0 to airport l0-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load object p2 into airplane a0 at location l1-0 and unload object p2 from airplane a0 at location l1-0", "unload object p2 from airplane a0 at location l0-0 and load object p2 into truck t0 at location l0-0", "drive the truck t1 in city c1 from location l1-0 to location l1-1 and load object p2 into truck t1 at location l1-1", "load object p0 into truck t0 at location l0-0 and operate the airplane a0 from airport l1-0 to airport l0-0"]}, "query": "Given the plan: \"drive the truck t1 in city c1 from location l1-0 to location l1-1, load object p2 into truck t1 at location l1-1, drive the truck t1 in city c1 from location l1-1 to location l1-0, remove the object p2 from the truck t1 and place it on the location l1-0, load object p2 into airplane a0 at location l1-0, unload object p2 from airplane a0 at location l1-0, load object p2 into airplane a0 at location l1-0, load object p0 into truck t0 at location l0-0, operate the airplane a0 from airport l1-0 to airport l0-0, unload object p2 from airplane a0 at location l0-0, load object p2 into truck t0 at location l0-0, remove the object p2 from the truck t0 and place it on the location l0-0, drive the truck t0 in city c0 from location l0-0 to location l0-1, remove the object p0 from the truck t0 and place it on the location l0-1\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": -1594068980366314944, "group": "action_justification_mcq", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-0, l0-1, and l0-2 are in c0; l1-1, l1-2, and l1-0 are in c1. Currently, p3, t1, and p0 are at l1-2, p1 is at l1-1, t0 is at l0-2, a0 is at l0-0, p2 is at l1-0. The goal is to reach a state where the following facts hold: p1 is at l1-2, p0 is at l0-2, p3 is at l0-1, and p2 is at l1-2.", "question": "Given the plan: \"load the object p3 from location l1-2 into the truck t1, offload the object p3 from the truck t1 at location l1-2, drive the truck t0 in city c0 from location l0-2 to location l0-0, fly the airplane a0 from airport l0-0 to airport l1-0, load the object p3 from location l1-2 into the truck t1, load the object p0 from location l1-2 into the truck t1, drive the truck t1 in city c1 from location l1-2 to location l1-1, load the object p1 from location l1-1 into the truck t1, drive the truck t1 in city c1 from location l1-1 to location l1-0, load the object p2 from location l1-0 into the truck t1, offload the object p3 from the truck t1 at location l1-0, load the object p3 from location l1-0 into the airplane a0, offload the object p0 from the truck t1 at location l1-0, load the object p0 from location l1-0 into the airplane a0, fly the airplane a0 from airport l1-0 to airport l0-0, remove the object p3 from the airplane a0 and place it on the location l0-0, remove the object p0 from the airplane a0 and place it on the location l0-0, load the object p0 from location l0-0 into the truck t0, load the object p3 from location l0-0 into the truck t0, drive the truck t0 in city c0 from location l0-0 to location l0-2, offload the object p0 from the truck t0 at location l0-2, drive the truck t0 in city c0 from location l0-2 to location l0-1, offload the object p3 from the truck t0 at location l0-1, drive the truck t1 in city c1 from location l1-0 to location l1-2, offload the object p1 from the truck t1 at location l1-2, offload the object p2 from the truck t1 at location l1-2\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. load the object p0 from location l0-0 into the truck t0 and load the object p3 from location l0-0 into the truck t0. B. load the object p3 from location l1-2 into the truck t1 and offload the object p3 from the truck t1 at location l1-2. C. drive the truck t0 in city c0 from location l0-0 to location l0-2 and offload the object p0 from the truck t0 at location l0-2. D. load the object p3 from location l1-2 into the truck t1 and load the object p0 from location l1-2 into the truck t1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["load the object p0 from location l0-0 into the truck t0 and load the object p3 from location l0-0 into the truck t0", "load the object p3 from location l1-2 into the truck t1 and offload the object p3 from the truck t1 at location l1-2", "drive the truck t0 in city c0 from location l0-0 to location l0-2 and offload the object p0 from the truck t0 at location l0-2", "load the object p3 from location l1-2 into the truck t1 and load the object p0 from location l1-2 into the truck t1"]}, "query": "Given the plan: \"load the object p3 from location l1-2 into the truck t1, offload the object p3 from the truck t1 at location l1-2, drive the truck t0 in city c0 from location l0-2 to location l0-0, fly the airplane a0 from airport l0-0 to airport l1-0, load the object p3 from location l1-2 into the truck t1, load the object p0 from location l1-2 into the truck t1, drive the truck t1 in city c1 from location l1-2 to location l1-1, load the object p1 from location l1-1 into the truck t1, drive the truck t1 in city c1 from location l1-1 to location l1-0, load the object p2 from location l1-0 into the truck t1, offload the object p3 from the truck t1 at location l1-0, load the object p3 from location l1-0 into the airplane a0, offload the object p0 from the truck t1 at location l1-0, load the object p0 from location l1-0 into the airplane a0, fly the airplane a0 from airport l1-0 to airport l0-0, remove the object p3 from the airplane a0 and place it on the location l0-0, remove the object p0 from the airplane a0 and place it on the location l0-0, load the object p0 from location l0-0 into the truck t0, load the object p3 from location l0-0 into the truck t0, drive the truck t0 in city c0 from location l0-0 to location l0-2, offload the object p0 from the truck t0 at location l0-2, drive the truck t0 in city c0 from location l0-2 to location l0-1, offload the object p3 from the truck t0 at location l0-1, drive the truck t1 in city c1 from location l1-0 to location l1-2, offload the object p1 from the truck t1 at location l1-2, offload the object p2 from the truck t1 at location l1-2\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": -3440279738805333027, "group": "action_justification_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) is stacked on top of another block: block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is on top of block block_2 and The block block_3 is currently situated above the block block_1.", "question": "Given the plan: \"unstack object block_3 from object block_2, put down object block_3, pick up the object block_2 from the table, put down object block_2, pick up the object block_1 from the table, stack the object block_1 on top of the object block_2, unstack object block_1 from object block_2, stack the object block_1 on top of the object block_2, unstack object block_1 from object block_2, stack the object block_1 on top of the object block_2, pick up the object block_3 from the table, stack the object block_3 on top of the object block_1, unstack object block_3 from object block_1, stack the object block_3 on top of the object block_1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. pick up the object block_1 from the table and stack the object block_1 on top of the object block_2. B. put down object block_3 and pick up the object block_2 from the table. C. unstack object block_3 from object block_1 and stack the object block_3 on top of the object block_1. D. put down object block_2 and pick up the object block_1 from the table.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pick up the object block_1 from the table and stack the object block_1 on top of the object block_2", "put down object block_3 and pick up the object block_2 from the table", "unstack object block_3 from object block_1 and stack the object block_3 on top of the object block_1", "put down object block_2 and pick up the object block_1 from the table"]}, "query": "Given the plan: \"unstack object block_3 from object block_2, put down object block_3, pick up the object block_2 from the table, put down object block_2, pick up the object block_1 from the table, stack the object block_1 on top of the object block_2, unstack object block_1 from object block_2, stack the object block_1 on top of the object block_2, unstack object block_1 from object block_2, stack the object block_1 on top of the object block_2, pick up the object block_3 from the table, stack the object block_3 on top of the object block_1, unstack object block_3 from object block_1, stack the object block_3 on top of the object block_1\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": 4864640348017390256, "group": "action_justification_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) is stacked on top of another block: block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated above the block block_2 and The block block_3 is currently situated above the block block_1.", "question": "Given the plan: \"unstack object block_3 from object block_2, put down object block_3, pick up the object block_2 from the table, put down object block_2, pick up the object block_1 from the table, stack object block_1 on top of object block_2, pick up the object block_3 from the table, stack object block_3 on top of object block_1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. put down object block_3 and pick up the object block_2 from the table. B. pick up the object block_2 from the table and put down object block_2. C. pick up the object block_3 from the table and stack object block_3 on top of object block_1. D. put down object block_2 and pick up the object block_1 from the table.", "choices": {"label": ["A", "B", "C", "D"], "text": ["put down object block_3 and pick up the object block_2 from the table", "pick up the object block_2 from the table and put down object block_2", "pick up the object block_3 from the table and stack object block_3 on top of object block_1", "put down object block_2 and pick up the object block_1 from the table"]}, "query": "Given the plan: \"unstack object block_3 from object block_2, put down object block_3, pick up the object block_2 from the table, put down object block_2, pick up the object block_1 from the table, stack object block_1 on top of object block_2, pick up the object block_3 from the table, stack object block_3 on top of object block_1\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": -1570036856712894049, "group": "action_justification_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_3, and block_1. The goal is to reach a state where the following facts hold: The block block_2 is on top of block block_1.", "question": "Given the plan: \"remove block_1 from table, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, remove block_2 from table, place the object block_2 on top of the object block_1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. place the object block_1 on top of the object block_3 and remove block_2 from table. B. remove block_2 from table and place the object block_2 on top of the object block_1. C. remove block_1 from table and place the object block_1 on top of the object block_3. D. unstack the object block_1 from the object block_3 and place the object block_1 on top of the object block_3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object block_1 on top of the object block_3 and remove block_2 from table", "remove block_2 from table and place the object block_2 on top of the object block_1", "remove block_1 from table and place the object block_1 on top of the object block_3", "unstack the object block_1 from the object block_3 and place the object block_1 on top of the object block_3"]}, "query": "Given the plan: \"remove block_1 from table, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, unstack the object block_1 from the object block_3, place the object block_1 on top of the object block_3, remove block_2 from table, place the object block_2 on top of the object block_1\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -3174529567812974026, "group": "action_justification_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_3 and block_1. The following block(s) is stacked on top of another block: block_2 is on block_1. The goal is to reach a state where the following facts hold: The block block_1 is currently situated above the block block_3 and The block block_1 is currently situated under the block block_2.", "question": "Given the plan: \"unstack object block_2 from object block_1, place the object block_2 on the table, collect the object block_1 from the table, place the object block_1 on top of the object block_3, unstack object block_1 from object block_3, place the object block_1 on the table, collect the object block_3 from the table, place the object block_3 on the table, collect the object block_1 from the table, place the object block_1 on top of the object block_3, collect the object block_2 from the table, place the object block_2 on top of the object block_1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. place the object block_1 on top of the object block_3 and unstack object block_1 from object block_3. B. collect the object block_1 from the table and place the object block_1 on top of the object block_3. C. unstack object block_1 from object block_3 and place the object block_1 on the table. D. place the object block_1 on top of the object block_3 and collect the object block_2 from the table.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object block_1 on top of the object block_3 and unstack object block_1 from object block_3", "collect the object block_1 from the table and place the object block_1 on top of the object block_3", "unstack object block_1 from object block_3 and place the object block_1 on the table", "place the object block_1 on top of the object block_3 and collect the object block_2 from the table"]}, "query": "Given the plan: \"unstack object block_2 from object block_1, place the object block_2 on the table, collect the object block_1 from the table, place the object block_1 on top of the object block_3, unstack object block_1 from object block_3, place the object block_1 on the table, collect the object block_3 from the table, place the object block_3 on the table, collect the object block_1 from the table, place the object block_1 on top of the object block_3, collect the object block_2 from the table, place the object block_2 on top of the object block_1\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": 8978557238679760031, "group": "action_justification_mcq", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_3 and block_1. The following block(s) is stacked on top of another block: block_2 is on block_1. The goal is to reach a state where the following facts hold: The block block_1 is on top of block block_3 and The block block_2 is on top of block block_1.", "question": "Given the plan: \"remove the object block_2 from on top of the object block_1, place the object block_2 on the table, collect the object block_2 from the table, place the object block_2 on the table, collect the object block_1 from the table, place the object block_1 on the table, collect the object block_1 from the table, place the object block_1 on the table, collect the object block_1 from the table, stack the object block_1 on top of the object block_3, collect the object block_2 from the table, stack the object block_2 on top of the object block_1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. place the object block_2 on the table and collect the object block_1 from the table. B. stack the object block_1 on top of the object block_3 and collect the object block_2 from the table. C. collect the object block_1 from the table and place the object block_1 on the table. D. collect the object block_2 from the table and stack the object block_2 on top of the object block_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object block_2 on the table and collect the object block_1 from the table", "stack the object block_1 on top of the object block_3 and collect the object block_2 from the table", "collect the object block_1 from the table and place the object block_1 on the table", "collect the object block_2 from the table and stack the object block_2 on top of the object block_1"]}, "query": "Given the plan: \"remove the object block_2 from on top of the object block_1, place the object block_2 on the table, collect the object block_2 from the table, place the object block_2 on the table, collect the object block_1 from the table, place the object block_1 on the table, collect the object block_1 from the table, place the object block_1 on the table, collect the object block_1 from the table, stack the object block_1 on top of the object block_3, collect the object block_2 from the table, stack the object block_2 on top of the object block_1\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": -3225800921868345154, "group": "action_justification_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f3-4f has shape0 shaped lock. Key key0-0 is at position f1-2f. Key key0-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-4f location and Key key0-0 is at f1-0f location.", "question": "Given the plan: \"transition from the current position f0-4f to the next position f0-3f, transition from the current position f0-3f to the next position f1-3f, transition from the current position f1-3f to the next position f1-2f, acquire the key key0-0 from the place f1-2f, transition from the current position f1-2f to the next position f1-1f, transition from the current position f1-1f to the next position f1-0f, place the key key0-0 at the current position place f1-0f, transition from the current position f1-0f to the next position f2-0f, transition from the current position f2-0f to the next position f3-0f, acquire the key key0-1 from the place f3-0f, transition from the current position f3-0f to the next position f4-0f, transition from the current position f4-0f to the next position f4-1f, transition from the current position f4-1f to the next position f4-2f, transition from the current position f4-2f to the next position f4-3f, transition from the current position f4-3f to the next position f4-4f, transition from the current position f4-4f to the next position f4-3f, transition from the current position f4-3f to the next position f4-4f, place the key key0-1 at the current position place f4-4f\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. transition from the current position f1-0f to the next position f2-0f and transition from the current position f2-0f to the next position f3-0f. B. transition from the current position f0-3f to the next position f1-3f and transition from the current position f1-3f to the next position f1-2f. C. transition from the current position f4-4f to the next position f4-3f and transition from the current position f4-3f to the next position f4-4f. D. transition from the current position f4-1f to the next position f4-2f and transition from the current position f4-2f to the next position f4-3f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transition from the current position f1-0f to the next position f2-0f and transition from the current position f2-0f to the next position f3-0f", "transition from the current position f0-3f to the next position f1-3f and transition from the current position f1-3f to the next position f1-2f", "transition from the current position f4-4f to the next position f4-3f and transition from the current position f4-3f to the next position f4-4f", "transition from the current position f4-1f to the next position f4-2f and transition from the current position f4-2f to the next position f4-3f"]}, "query": "Given the plan: \"transition from the current position f0-4f to the next position f0-3f, transition from the current position f0-3f to the next position f1-3f, transition from the current position f1-3f to the next position f1-2f, acquire the key key0-0 from the place f1-2f, transition from the current position f1-2f to the next position f1-1f, transition from the current position f1-1f to the next position f1-0f, place the key key0-0 at the current position place f1-0f, transition from the current position f1-0f to the next position f2-0f, transition from the current position f2-0f to the next position f3-0f, acquire the key key0-1 from the place f3-0f, transition from the current position f3-0f to the next position f4-0f, transition from the current position f4-0f to the next position f4-1f, transition from the current position f4-1f to the next position f4-2f, transition from the current position f4-2f to the next position f4-3f, transition from the current position f4-3f to the next position f4-4f, transition from the current position f4-4f to the next position f4-3f, transition from the current position f4-3f to the next position f4-4f, place the key key0-1 at the current position place f4-4f\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": 1934541770948300499, "group": "action_justification_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f3-4f has shape0 shaped lock. Key key0-0 is at position f1-2f. Key key0-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-4f location and Key key0-0 is at f1-0f location.", "question": "Given the plan: \"travel from the current position f0-4f to the next position f0-3f, travel from the current position f0-3f to the next position f1-3f, travel from the current position f1-3f to the next position f1-2f, acquire the key key0-0 from the place f1-2f, travel from the current position f1-2f to the next position f1-1f, travel from the current position f1-1f to the next position f1-0f, place the key key0-0 at the current position place f1-0f, travel from the current position f1-0f to the next position f2-0f, travel from the current position f2-0f to the next position f3-0f, acquire the key key0-1 from the place f3-0f, place the key key0-1 at the current position place f3-0f, acquire the key key0-1 from the place f3-0f, travel from the current position f3-0f to the next position f3-1f, travel from the current position f3-1f to the next position f3-2f, travel from the current position f3-2f to the next position f3-3f, travel from the current position f3-3f to the next position f4-3f, travel from the current position f4-3f to the next position f4-4f, place the key key0-1 at the current position place f4-4f\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. travel from the current position f4-3f to the next position f4-4f and place the key key0-1 at the current position place f4-4f. B. place the key key0-0 at the current position place f1-0f and travel from the current position f1-0f to the next position f2-0f. C. place the key key0-1 at the current position place f3-0f and acquire the key key0-1 from the place f3-0f. D. travel from the current position f3-0f to the next position f3-1f and travel from the current position f3-1f to the next position f3-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel from the current position f4-3f to the next position f4-4f and place the key key0-1 at the current position place f4-4f", "place the key key0-0 at the current position place f1-0f and travel from the current position f1-0f to the next position f2-0f", "place the key key0-1 at the current position place f3-0f and acquire the key key0-1 from the place f3-0f", "travel from the current position f3-0f to the next position f3-1f and travel from the current position f3-1f to the next position f3-2f"]}, "query": "Given the plan: \"travel from the current position f0-4f to the next position f0-3f, travel from the current position f0-3f to the next position f1-3f, travel from the current position f1-3f to the next position f1-2f, acquire the key key0-0 from the place f1-2f, travel from the current position f1-2f to the next position f1-1f, travel from the current position f1-1f to the next position f1-0f, place the key key0-0 at the current position place f1-0f, travel from the current position f1-0f to the next position f2-0f, travel from the current position f2-0f to the next position f3-0f, acquire the key key0-1 from the place f3-0f, place the key key0-1 at the current position place f3-0f, acquire the key key0-1 from the place f3-0f, travel from the current position f3-0f to the next position f3-1f, travel from the current position f3-1f to the next position f3-2f, travel from the current position f3-2f to the next position f3-3f, travel from the current position f3-3f to the next position f4-3f, travel from the current position f4-3f to the next position f4-4f, place the key key0-1 at the current position place f4-4f\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": 4483009876154455395, "group": "action_justification_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-1f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f0-0f has shape0 shaped lock. Key key0-0 is at position f0-2f. Key key0-1 is at position f4-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location.", "question": "Given the plan: \"move from place f3-1f to place f3-0f, move from place f3-0f to place f4-0f, retrieve the key key0-1 from its current position f4-0f, move from place f4-0f to place f3-0f, move from place f3-0f to place f4-0f, move from place f4-0f to place f3-0f, put the key key0-1 at the current position place f3-0f, move from place f3-0f to place f2-0f, move from place f2-0f to place f1-0f, move from place f1-0f to place f1-1f, move from place f1-1f to place f0-1f, move from place f0-1f to place f0-2f, retrieve the key key0-0 from its current position f0-2f, use the key key0-0 of shape shape0 to unlock the place f0-3f from the current position f0-2f, move from place f0-2f to place f0-3f, move from place f0-3f to place f0-4f, put the key key0-0 at the current position place f0-4f\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. move from place f0-2f to place f0-3f and move from place f0-3f to place f0-4f. B. move from place f3-0f to place f2-0f and move from place f2-0f to place f1-0f. C. move from place f1-1f to place f0-1f and move from place f0-1f to place f0-2f. D. move from place f3-0f to place f4-0f and move from place f4-0f to place f3-0f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move from place f0-2f to place f0-3f and move from place f0-3f to place f0-4f", "move from place f3-0f to place f2-0f and move from place f2-0f to place f1-0f", "move from place f1-1f to place f0-1f and move from place f0-1f to place f0-2f", "move from place f3-0f to place f4-0f and move from place f4-0f to place f3-0f"]}, "query": "Given the plan: \"move from place f3-1f to place f3-0f, move from place f3-0f to place f4-0f, retrieve the key key0-1 from its current position f4-0f, move from place f4-0f to place f3-0f, move from place f3-0f to place f4-0f, move from place f4-0f to place f3-0f, put the key key0-1 at the current position place f3-0f, move from place f3-0f to place f2-0f, move from place f2-0f to place f1-0f, move from place f1-0f to place f1-1f, move from place f1-1f to place f0-1f, move from place f0-1f to place f0-2f, retrieve the key key0-0 from its current position f0-2f, use the key key0-0 of shape shape0 to unlock the place f0-3f from the current position f0-2f, move from place f0-2f to place f0-3f, move from place f0-3f to place f0-4f, put the key key0-0 at the current position place f0-4f\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": 6182042285945903547, "group": "action_justification_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-1f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock. Key key0-1 is at position f0-1f. Key key0-0 is at position f4-4f. The goal is to reach a state where the following facts hold: Key key0-0 is at f2-4f location and Key key0-1 is at f4-2f location.", "question": "Given the plan: \"travel from the current position f2-1f to the next position f1-1f, travel from the current position f1-1f to the next position f0-1f, pick up key key0-1 from place f0-1f, travel from the current position f0-1f to the next position f0-2f, travel from the current position f0-2f to the next position f1-2f, travel from the current position f1-2f to the next position f2-2f, place the key key0-1 at the current position place f2-2f, pick up key key0-1 from place f2-2f, travel from the current position f2-2f to the next position f3-2f, travel from the current position f3-2f to the next position f4-2f, place the key key0-1 at the current position place f4-2f, travel from the current position f4-2f to the next position f4-3f, travel from the current position f4-3f to the next position f4-4f, pick up key key0-0 from place f4-4f, travel from the current position f4-4f to the next position f3-4f, travel from the current position f3-4f to the next position f2-4f, place the key key0-0 at the current position place f2-4f\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. travel from the current position f2-2f to the next position f3-2f and travel from the current position f3-2f to the next position f4-2f. B. pick up key key0-0 from place f4-4f and travel from the current position f4-4f to the next position f3-4f. C. travel from the current position f3-2f to the next position f4-2f and place the key key0-1 at the current position place f4-2f. D. place the key key0-1 at the current position place f2-2f and pick up key key0-1 from place f2-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel from the current position f2-2f to the next position f3-2f and travel from the current position f3-2f to the next position f4-2f", "pick up key key0-0 from place f4-4f and travel from the current position f4-4f to the next position f3-4f", "travel from the current position f3-2f to the next position f4-2f and place the key key0-1 at the current position place f4-2f", "place the key key0-1 at the current position place f2-2f and pick up key key0-1 from place f2-2f"]}, "query": "Given the plan: \"travel from the current position f2-1f to the next position f1-1f, travel from the current position f1-1f to the next position f0-1f, pick up key key0-1 from place f0-1f, travel from the current position f0-1f to the next position f0-2f, travel from the current position f0-2f to the next position f1-2f, travel from the current position f1-2f to the next position f2-2f, place the key key0-1 at the current position place f2-2f, pick up key key0-1 from place f2-2f, travel from the current position f2-2f to the next position f3-2f, travel from the current position f3-2f to the next position f4-2f, place the key key0-1 at the current position place f4-2f, travel from the current position f4-2f to the next position f4-3f, travel from the current position f4-3f to the next position f4-4f, pick up key key0-0 from place f4-4f, travel from the current position f4-4f to the next position f3-4f, travel from the current position f3-4f to the next position f2-4f, place the key key0-0 at the current position place f2-4f\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -6378087522500349929, "group": "action_justification_mcq", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f3-4f has shape0 shaped lock. Key key0-0 is at position f1-2f. Key key0-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-4f location and Key key0-0 is at f1-0f location.", "question": "Given the plan: \"move from f0-4f to f0-3f, move from f0-3f to f1-3f, move from f1-3f to f1-2f, pick up key key0-0 from place f1-2f, move from f1-2f to f1-1f, move from f1-1f to f1-0f, place the key key0-0 at the current position f1-0f, move from f1-0f to f2-0f, move from f2-0f to f3-0f, pick up key key0-1 from place f3-0f, move from f3-0f to f4-0f, move from f4-0f to f4-1f, move from f4-1f to f4-2f, move from f4-2f to f3-2f, move from f3-2f to f4-2f, move from f4-2f to f4-3f, move from f4-3f to f4-4f, place the key key0-1 at the current position f4-4f\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. move from f1-1f to f1-0f and place the key key0-0 at the current position f1-0f. B. move from f1-3f to f1-2f and pick up key key0-0 from place f1-2f. C. move from f0-4f to f0-3f and move from f0-3f to f1-3f. D. move from f4-2f to f3-2f and move from f3-2f to f4-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move from f1-1f to f1-0f and place the key key0-0 at the current position f1-0f", "move from f1-3f to f1-2f and pick up key key0-0 from place f1-2f", "move from f0-4f to f0-3f and move from f0-3f to f1-3f", "move from f4-2f to f3-2f and move from f3-2f to f4-2f"]}, "query": "Given the plan: \"move from f0-4f to f0-3f, move from f0-3f to f1-3f, move from f1-3f to f1-2f, pick up key key0-0 from place f1-2f, move from f1-2f to f1-1f, move from f1-1f to f1-0f, place the key key0-0 at the current position f1-0f, move from f1-0f to f2-0f, move from f2-0f to f3-0f, pick up key key0-1 from place f3-0f, move from f3-0f to f4-0f, move from f4-0f to f4-1f, move from f4-1f to f4-2f, move from f4-2f to f3-2f, move from f3-2f to f4-2f, move from f4-2f to f4-3f, move from f4-3f to f4-4f, place the key key0-1 at the current position f4-4f\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": -6291797480617531591, "group": "action_justification_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 16 tiles and 2 robots. The tiles locations are: tile_3 is to the right of tile_2, tile_14 is to the right of tile_13, tile_15 is to the right of tile_14, tile_2 is to the right of tile_1, tile_16 is to the right of tile_15, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_10 is to the right of tile_9, tile_4 is to the right of tile_3, and tile_7 is to the right of tile_6. Further, tile_6 is down from tile_10, tile_1 is down from tile_5, tile_3 is down from tile_7, tile_5 is down from tile_9, tile_11 is down from tile_15, tile_12 is down from tile_16, tile_4 is down from tile_8, tile_7 is down from tile_11, tile_2 is down from tile_6, tile_10 is down from tile_14, tile_8 is down from tile_12, and tile_9 is down from tile_13 Currently, robot robot1 is at tile_8 and holding color white and robot robot2 is at tile_14 and holding color black; tile_15, tile_7, tile_11, tile_3, tile_9, tile_12, tile_4, tile_5, tile_10, tile_2, tile_13, tile_16, tile_6, and tile_1 are clear. The goal is to reach a state where the following facts hold: Tile tile_15 is painted in white color, Tile tile_9 is painted in black color, Tile tile_16 is painted in black color, Tile tile_10 is painted in white color, Tile tile_11 is painted in black color, Tile tile_14 is painted in black color, Tile tile_12 is painted in white color, Tile tile_13 is painted in white color, Tile tile_7 is painted in white color, Tile tile_6 is painted in black color, Tile tile_8 is painted in black color, and Tile tile_5 is painted in white color.", "question": "Given the plan: \"move robot robot1 down from tile tile_8 to tile tile_4, move robot robot2 down from tile tile_14 to tile tile_10, use robot robot2 to paint the tile tile_14 above the tile tile_10 with the color black, navigate robot robot1 from tile tile_4 to tile tile_3 to its left, modify the color of the robot robot2 from black to white, navigate robot robot2 from tile tile_10 to tile tile_9 to its left, use robot robot2 to paint the tile tile_13 above the tile tile_9 with the color white, move robot robot2 down from tile tile_9 to tile tile_5, move robot robot2 down from tile tile_5 to tile tile_1, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot1 from tile tile_3 to tile tile_7 upwards, move the robot robot1 from tile tile_7 to tile tile_11 upwards, use robot robot1 to paint the tile tile_15 above the tile tile_11 with the color white, navigate robot robot1 from tile tile_11 to tile tile_12 to the right, modify the color of the robot robot2 from white to black, modify the color of the robot robot1 from white to black, use robot robot1 to paint the tile tile_16 above the tile tile_12 with the color black, modify the color of the robot robot1 from black to white, move robot robot1 down from tile tile_12 to tile tile_8, use robot robot1 to paint the tile tile_12 above the tile tile_8 with the color white, modify the color of the robot robot1 from white to black, navigate robot robot2 from tile tile_2 to tile tile_1 to its left, modify the color of the robot robot2 from black to white, move robot robot1 down from tile tile_8 to tile tile_4, use robot robot1 to paint the tile tile_8 above the tile tile_4 with the color black, navigate robot robot1 from tile tile_4 to tile tile_3 to its left, navigate robot robot1 from tile tile_3 to tile tile_2 to its left, move the robot robot1 from tile tile_2 to tile tile_6 upwards, navigate robot robot1 from tile tile_6 to tile tile_5 to its left, use robot robot1 to paint the tile tile_9 above the tile tile_5 with the color black, navigate robot robot1 from tile tile_5 to tile tile_6 to the right, modify the color of the robot robot1 from black to white, use robot robot1 to paint the tile tile_10 above the tile tile_6 with the color white, modify the color of the robot robot1 from white to black, move robot robot1 down from tile tile_6 to tile tile_2, use robot robot2 to paint the tile tile_5 above the tile tile_1 with the color white, use robot robot1 to paint the tile tile_6 above the tile tile_2 with the color black, navigate robot robot1 from tile tile_2 to tile tile_3 to the right, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot1 from tile tile_3 to tile tile_7 upwards, use robot robot1 to paint the tile tile_11 above the tile tile_7 with the color black, modify the color of the robot robot1 from black to white, move robot robot1 down from tile tile_7 to tile tile_3, navigate robot robot2 from tile tile_2 to tile tile_1 to its left, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, use robot robot1 to paint the tile tile_7 above the tile tile_3 with the color white, navigate robot robot2 from tile tile_2 to tile tile_1 to its left\"; which of the following actions can be removed from this plan and still have a valid plan? A. use robot robot2 to paint the tile tile_5 above the tile tile_1 with the color white. B. navigate robot robot2 from tile tile_2 to tile tile_1 to its left. C. move the robot robot1 from tile tile_2 to tile tile_6 upwards. D. move robot robot1 down from tile tile_7 to tile tile_3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use robot robot2 to paint the tile tile_5 above the tile tile_1 with the color white", "navigate robot robot2 from tile tile_2 to tile tile_1 to its left", "move the robot robot1 from tile tile_2 to tile tile_6 upwards", "move robot robot1 down from tile tile_7 to tile tile_3"]}, "query": "Given the plan: \"move robot robot1 down from tile tile_8 to tile tile_4, move robot robot2 down from tile tile_14 to tile tile_10, use robot robot2 to paint the tile tile_14 above the tile tile_10 with the color black, navigate robot robot1 from tile tile_4 to tile tile_3 to its left, modify the color of the robot robot2 from black to white, navigate robot robot2 from tile tile_10 to tile tile_9 to its left, use robot robot2 to paint the tile tile_13 above the tile tile_9 with the color white, move robot robot2 down from tile tile_9 to tile tile_5, move robot robot2 down from tile tile_5 to tile tile_1, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot1 from tile tile_3 to tile tile_7 upwards, move the robot robot1 from tile tile_7 to tile tile_11 upwards, use robot robot1 to paint the tile tile_15 above the tile tile_11 with the color white, navigate robot robot1 from tile tile_11 to tile tile_12 to the right, modify the color of the robot robot2 from white to black, modify the color of the robot robot1 from white to black, use robot robot1 to paint the tile tile_16 above the tile tile_12 with the color black, modify the color of the robot robot1 from black to white, move robot robot1 down from tile tile_12 to tile tile_8, use robot robot1 to paint the tile tile_12 above the tile tile_8 with the color white, modify the color of the robot robot1 from white to black, navigate robot robot2 from tile tile_2 to tile tile_1 to its left, modify the color of the robot robot2 from black to white, move robot robot1 down from tile tile_8 to tile tile_4, use robot robot1 to paint the tile tile_8 above the tile tile_4 with the color black, navigate robot robot1 from tile tile_4 to tile tile_3 to its left, navigate robot robot1 from tile tile_3 to tile tile_2 to its left, move the robot robot1 from tile tile_2 to tile tile_6 upwards, navigate robot robot1 from tile tile_6 to tile tile_5 to its left, use robot robot1 to paint the tile tile_9 above the tile tile_5 with the color black, navigate robot robot1 from tile tile_5 to tile tile_6 to the right, modify the color of the robot robot1 from black to white, use robot robot1 to paint the tile tile_10 above the tile tile_6 with the color white, modify the color of the robot robot1 from white to black, move robot robot1 down from tile tile_6 to tile tile_2, use robot robot2 to paint the tile tile_5 above the tile tile_1 with the color white, use robot robot1 to paint the tile tile_6 above the tile tile_2 with the color black, navigate robot robot1 from tile tile_2 to tile tile_3 to the right, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot1 from tile tile_3 to tile tile_7 upwards, use robot robot1 to paint the tile tile_11 above the tile tile_7 with the color black, modify the color of the robot robot1 from black to white, move robot robot1 down from tile tile_7 to tile tile_3, navigate robot robot2 from tile tile_2 to tile tile_1 to its left, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, use robot robot1 to paint the tile tile_7 above the tile tile_3 with the color white, navigate robot robot2 from tile tile_2 to tile tile_1 to its left\"; which action can be removed from this plan?", "answer": "B"}
{"id": 1916930533628482241, "group": "action_justification_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_3 is to the right of tile_2, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, and tile_5 is to the right of tile_4. Further, tile_3 is down from tile_6, tile_1 is down from tile_4, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_7 is down from tile_10, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_9 is down from tile_12, and tile_5 is down from tile_8 Currently, robot robot2 is at tile_11 and holding color black and robot robot1 is at tile_6 and holding color white; tile_7, tile_3, tile_9, tile_12, tile_4, tile_5, tile_10, tile_2, tile_8, and tile_1 are clear. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_4 is painted in white color, Tile tile_10 is painted in white color, Tile tile_11 is painted in black color, Tile tile_5 is painted in black color, Tile tile_12 is painted in white color, Tile tile_6 is painted in white color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"modify the color of the robot robot2 from black to white, move robot robot1 down from tile tile_6 to tile tile_3, move robot robot2 from tile tile_11 to the left tile tile tile_10, move robot robot2 down from tile tile_10 to tile tile_7, apply color white to tile tile_10 above tile tile_7 using robot robot2, move the robot robot2 from the tile tile_7 to the tile tile_8 which is to the right of the tile tile_7, move the robot robot2 from the tile tile_8 to the tile tile_9 which is to the right of the tile tile_8, apply color white to tile tile_12 above tile tile_9 using robot robot2, move robot robot1 from tile tile_3 to the left tile tile tile_2, move robot robot1 from tile tile_2 to the left tile tile tile_1, modify the color of the robot robot2 from white to black, move robot robot2 from tile tile_9 to the left tile tile tile_8, apply color black to tile tile_11 above tile tile_8 using robot robot2, modify the color of the robot robot1 from white to black, move robot robot2 down from tile tile_8 to tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, apply color black to tile tile_7 above tile tile_4 using robot robot2, move the robot robot2 from the tile tile_4 to the tile tile_5 which is to the right of the tile tile_4, modify the color of the robot robot1 from black to white, apply color white to tile tile_4 above tile tile_1 using robot robot1, move the robot robot1 from the tile tile_1 to the tile tile_2 which is to the right of the tile tile_1, modify the color of the robot robot2 from black to white, apply color white to tile tile_8 above tile tile_5 using robot robot2, modify the color of the robot robot2 from white to black, move the robot robot1 from the tile tile_2 to the tile tile_3 which is to the right of the tile tile_2, move the robot robot2 from the tile tile_5 to the tile tile_6 which is to the right of the tile tile_5, apply color black to tile tile_9 above tile tile_6 using robot robot2, move robot robot2 from tile tile_6 to the left tile tile tile_5, move robot robot2 down from tile tile_5 to tile tile_2, apply color white to tile tile_6 above tile tile_3 using robot robot1, apply color black to tile tile_5 above tile tile_2 using robot robot2, move robot robot2 from tile tile_2 to the left tile tile tile_1\"; which of the following actions can be removed from this plan and still have a valid plan? A. move the robot robot2 from the tile tile_4 to the tile tile_5 which is to the right of the tile tile_4. B. move the robot robot2 from the tile tile_7 to the tile tile_8 which is to the right of the tile tile_7. C. modify the color of the robot robot2 from white to black. D. move robot robot2 from tile tile_2 to the left tile tile tile_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move the robot robot2 from the tile tile_4 to the tile tile_5 which is to the right of the tile tile_4", "move the robot robot2 from the tile tile_7 to the tile tile_8 which is to the right of the tile tile_7", "modify the color of the robot robot2 from white to black", "move robot robot2 from tile tile_2 to the left tile tile tile_1"]}, "query": "Given the plan: \"modify the color of the robot robot2 from black to white, move robot robot1 down from tile tile_6 to tile tile_3, move robot robot2 from tile tile_11 to the left tile tile tile_10, move robot robot2 down from tile tile_10 to tile tile_7, apply color white to tile tile_10 above tile tile_7 using robot robot2, move the robot robot2 from the tile tile_7 to the tile tile_8 which is to the right of the tile tile_7, move the robot robot2 from the tile tile_8 to the tile tile_9 which is to the right of the tile tile_8, apply color white to tile tile_12 above tile tile_9 using robot robot2, move robot robot1 from tile tile_3 to the left tile tile tile_2, move robot robot1 from tile tile_2 to the left tile tile tile_1, modify the color of the robot robot2 from white to black, move robot robot2 from tile tile_9 to the left tile tile tile_8, apply color black to tile tile_11 above tile tile_8 using robot robot2, modify the color of the robot robot1 from white to black, move robot robot2 down from tile tile_8 to tile tile_5, move robot robot2 from tile tile_5 to the left tile tile tile_4, apply color black to tile tile_7 above tile tile_4 using robot robot2, move the robot robot2 from the tile tile_4 to the tile tile_5 which is to the right of the tile tile_4, modify the color of the robot robot1 from black to white, apply color white to tile tile_4 above tile tile_1 using robot robot1, move the robot robot1 from the tile tile_1 to the tile tile_2 which is to the right of the tile tile_1, modify the color of the robot robot2 from black to white, apply color white to tile tile_8 above tile tile_5 using robot robot2, modify the color of the robot robot2 from white to black, move the robot robot1 from the tile tile_2 to the tile tile_3 which is to the right of the tile tile_2, move the robot robot2 from the tile tile_5 to the tile tile_6 which is to the right of the tile tile_5, apply color black to tile tile_9 above tile tile_6 using robot robot2, move robot robot2 from tile tile_6 to the left tile tile tile_5, move robot robot2 down from tile tile_5 to tile tile_2, apply color white to tile tile_6 above tile tile_3 using robot robot1, apply color black to tile tile_5 above tile tile_2 using robot robot2, move robot robot2 from tile tile_2 to the left tile tile tile_1\"; which action can be removed from this plan?", "answer": "D"}
{"id": 1187297714147458009, "group": "action_justification_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_3 is to the right of tile_2, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, and tile_5 is to the right of tile_4. Further, tile_3 is down from tile_6, tile_1 is down from tile_4, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_7 is down from tile_10, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_9 is down from tile_12, and tile_5 is down from tile_8 Currently, robot robot2 is at tile_11 and holding color black and robot robot1 is at tile_6 and holding color white; tile_7, tile_3, tile_9, tile_12, tile_4, tile_5, tile_10, tile_2, tile_8, and tile_1 are clear. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_4 is painted in white color, Tile tile_10 is painted in white color, Tile tile_11 is painted in black color, Tile tile_5 is painted in black color, Tile tile_12 is painted in white color, Tile tile_6 is painted in white color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"change the color of robot robot2 from color black to color white, move the robot robot1 from the tile tile_6 to the tile tile_3 going downwards, move the robot robot2 from the tile tile_11 to the tile on its left tile_10, move the robot robot2 from the tile tile_10 to the tile tile_7 going downwards, apply color white to tile tile_10 above tile tile_7 using robot robot2, move robot robot2 from tile tile_7 to the right tile tile tile_8, move robot robot2 from tile tile_8 to the right tile tile tile_9, apply color white to tile tile_12 above tile tile_9 using robot robot2, move the robot robot1 from the tile tile_3 to the tile on its left tile_2, move the robot robot1 from the tile tile_2 to the tile on its left tile_1, change the color of robot robot2 from color white to color black, move the robot robot2 from the tile tile_9 to the tile on its left tile_8, apply color black to tile tile_11 above tile tile_8 using robot robot2, change the color of robot robot1 from color white to color black, move the robot robot2 from the tile tile_8 to the tile tile_5 going downwards, move robot robot2 from tile tile_5 to the right tile tile tile_6, apply color black to tile tile_9 above tile tile_6 using robot robot2, change the color of robot robot2 from color black to color white, change the color of robot robot1 from color black to color white, move the robot robot2 from the tile tile_6 to the tile on its left tile_5, apply color white to tile tile_8 above tile tile_5 using robot robot2, move the robot robot2 from the tile tile_5 to the tile tile_2 going downwards, move robot robot2 from tile tile_2 to the right tile tile tile_3, apply color white to tile tile_6 above tile tile_3 using robot robot2, change the color of robot robot2 from color white to color black, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, apply color black to tile tile_5 above tile tile_2 using robot robot2, change the color of robot robot1 from color white to color black, move the robot robot1 from tile tile_1 to tile tile_4 upwards, change the color of robot robot2 from color black to color white, apply color black to tile tile_7 above tile tile_4 using robot robot1, move the robot robot1 from the tile tile_4 to the tile tile_1 going downwards, change the color of robot robot1 from color black to color white, apply color white to tile tile_4 above tile tile_1 using robot robot1, change the color of robot robot2 from color white to color black\"; which of the following actions can be removed from this plan and still have a valid plan? A. change the color of robot robot2 from color white to color black. B. move robot robot2 from tile tile_8 to the right tile tile tile_9. C. apply color white to tile tile_10 above tile tile_7 using robot robot2. D. change the color of robot robot1 from color black to color white.", "choices": {"label": ["A", "B", "C", "D"], "text": ["change the color of robot robot2 from color white to color black", "move robot robot2 from tile tile_8 to the right tile tile tile_9", "apply color white to tile tile_10 above tile tile_7 using robot robot2", "change the color of robot robot1 from color black to color white"]}, "query": "Given the plan: \"change the color of robot robot2 from color black to color white, move the robot robot1 from the tile tile_6 to the tile tile_3 going downwards, move the robot robot2 from the tile tile_11 to the tile on its left tile_10, move the robot robot2 from the tile tile_10 to the tile tile_7 going downwards, apply color white to tile tile_10 above tile tile_7 using robot robot2, move robot robot2 from tile tile_7 to the right tile tile tile_8, move robot robot2 from tile tile_8 to the right tile tile tile_9, apply color white to tile tile_12 above tile tile_9 using robot robot2, move the robot robot1 from the tile tile_3 to the tile on its left tile_2, move the robot robot1 from the tile tile_2 to the tile on its left tile_1, change the color of robot robot2 from color white to color black, move the robot robot2 from the tile tile_9 to the tile on its left tile_8, apply color black to tile tile_11 above tile tile_8 using robot robot2, change the color of robot robot1 from color white to color black, move the robot robot2 from the tile tile_8 to the tile tile_5 going downwards, move robot robot2 from tile tile_5 to the right tile tile tile_6, apply color black to tile tile_9 above tile tile_6 using robot robot2, change the color of robot robot2 from color black to color white, change the color of robot robot1 from color black to color white, move the robot robot2 from the tile tile_6 to the tile on its left tile_5, apply color white to tile tile_8 above tile tile_5 using robot robot2, move the robot robot2 from the tile tile_5 to the tile tile_2 going downwards, move robot robot2 from tile tile_2 to the right tile tile tile_3, apply color white to tile tile_6 above tile tile_3 using robot robot2, change the color of robot robot2 from color white to color black, move the robot robot2 from the tile tile_3 to the tile on its left tile_2, apply color black to tile tile_5 above tile tile_2 using robot robot2, change the color of robot robot1 from color white to color black, move the robot robot1 from tile tile_1 to tile tile_4 upwards, change the color of robot robot2 from color black to color white, apply color black to tile tile_7 above tile tile_4 using robot robot1, move the robot robot1 from the tile tile_4 to the tile tile_1 going downwards, change the color of robot robot1 from color black to color white, apply color white to tile tile_4 above tile tile_1 using robot robot1, change the color of robot robot2 from color white to color black\"; which action can be removed from this plan?", "answer": "A"}
{"id": 1262866801893464951, "group": "action_justification_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_3 is to the right of tile_2, tile_6 is to the right of tile_5, tile_2 is to the right of tile_1, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_8 is to the right of tile_7, tile_9 is to the right of tile_8, and tile_5 is to the right of tile_4. Further, tile_3 is down from tile_6, tile_1 is down from tile_4, tile_2 is down from tile_5, tile_6 is down from tile_9, tile_7 is down from tile_10, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_9 is down from tile_12, and tile_5 is down from tile_8 Currently, robot robot2 is at tile_11 and holding color black and robot robot1 is at tile_6 and holding color white; tile_7, tile_3, tile_9, tile_12, tile_4, tile_5, tile_10, tile_2, tile_8, and tile_1 are clear. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_9 is painted in black color, Tile tile_4 is painted in white color, Tile tile_10 is painted in white color, Tile tile_11 is painted in black color, Tile tile_5 is painted in black color, Tile tile_12 is painted in white color, Tile tile_6 is painted in white color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"move the robot robot2 from the tile tile_11 to the tile on its left tile_10, move robot robot2 from tile tile_10 to the right tile tile tile_11, move the robot robot1 from the tile tile_6 to the tile on its left tile_5, move the robot robot2 from the tile tile_11 to the tile tile_8 going downwards, use robot robot2 to paint the tile tile_11 above the tile tile_8 with the color black, move the robot robot2 from the tile tile_8 to the tile on its left tile_7, use robot robot1 to paint the tile tile_8 above the tile tile_5 with the color white, move the robot robot1 from the tile tile_5 to the tile tile_2 going downwards, change the color of robot robot2 from color black to color white, use robot robot2 to paint the tile tile_10 above the tile tile_7 with the color white, move the robot robot2 from the tile tile_7 to the tile tile_4 going downwards, move the robot robot2 from the tile tile_4 to the tile tile_1 going downwards, change the color of robot robot1 from color white to color black, use robot robot1 to paint the tile tile_5 above the tile tile_2 with the color black, change the color of robot robot2 from color white to color black, move robot robot1 from tile tile_2 to the right tile tile tile_3, move robot robot2 up from tile tile_1 to tile tile_4, use robot robot2 to paint the tile tile_7 above the tile tile_4 with the color black, change the color of robot robot2 from color black to color white, move the robot robot2 from the tile tile_4 to the tile tile_1 going downwards, use robot robot2 to paint the tile tile_4 above the tile tile_1 with the color white, change the color of robot robot1 from color black to color white, move robot robot1 up from tile tile_3 to tile tile_6, move robot robot1 up from tile tile_6 to tile tile_9, use robot robot1 to paint the tile tile_12 above the tile tile_9 with the color white, change the color of robot robot1 from color white to color black, move the robot robot1 from the tile tile_9 to the tile tile_6 going downwards, use robot robot1 to paint the tile tile_9 above the tile tile_6 with the color black, change the color of robot robot1 from color black to color white, move the robot robot1 from the tile tile_6 to the tile tile_3 going downwards, use robot robot1 to paint the tile tile_6 above the tile tile_3 with the color white\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. move the robot robot1 from the tile tile_9 to the tile tile_6 going downwards and use robot robot1 to paint the tile tile_9 above the tile tile_6 with the color black. B. use robot robot2 to paint the tile tile_10 above the tile tile_7 with the color white and move the robot robot2 from the tile tile_7 to the tile tile_4 going downwards. C. use robot robot1 to paint the tile tile_12 above the tile tile_9 with the color white and change the color of robot robot1 from color white to color black. D. move the robot robot2 from the tile tile_11 to the tile on its left tile_10 and move robot robot2 from tile tile_10 to the right tile tile tile_11.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move the robot robot1 from the tile tile_9 to the tile tile_6 going downwards and use robot robot1 to paint the tile tile_9 above the tile tile_6 with the color black", "use robot robot2 to paint the tile tile_10 above the tile tile_7 with the color white and move the robot robot2 from the tile tile_7 to the tile tile_4 going downwards", "use robot robot1 to paint the tile tile_12 above the tile tile_9 with the color white and change the color of robot robot1 from color white to color black", "move the robot robot2 from the tile tile_11 to the tile on its left tile_10 and move robot robot2 from tile tile_10 to the right tile tile tile_11"]}, "query": "Given the plan: \"move the robot robot2 from the tile tile_11 to the tile on its left tile_10, move robot robot2 from tile tile_10 to the right tile tile tile_11, move the robot robot1 from the tile tile_6 to the tile on its left tile_5, move the robot robot2 from the tile tile_11 to the tile tile_8 going downwards, use robot robot2 to paint the tile tile_11 above the tile tile_8 with the color black, move the robot robot2 from the tile tile_8 to the tile on its left tile_7, use robot robot1 to paint the tile tile_8 above the tile tile_5 with the color white, move the robot robot1 from the tile tile_5 to the tile tile_2 going downwards, change the color of robot robot2 from color black to color white, use robot robot2 to paint the tile tile_10 above the tile tile_7 with the color white, move the robot robot2 from the tile tile_7 to the tile tile_4 going downwards, move the robot robot2 from the tile tile_4 to the tile tile_1 going downwards, change the color of robot robot1 from color white to color black, use robot robot1 to paint the tile tile_5 above the tile tile_2 with the color black, change the color of robot robot2 from color white to color black, move robot robot1 from tile tile_2 to the right tile tile tile_3, move robot robot2 up from tile tile_1 to tile tile_4, use robot robot2 to paint the tile tile_7 above the tile tile_4 with the color black, change the color of robot robot2 from color black to color white, move the robot robot2 from the tile tile_4 to the tile tile_1 going downwards, use robot robot2 to paint the tile tile_4 above the tile tile_1 with the color white, change the color of robot robot1 from color black to color white, move robot robot1 up from tile tile_3 to tile tile_6, move robot robot1 up from tile tile_6 to tile tile_9, use robot robot1 to paint the tile tile_12 above the tile tile_9 with the color white, change the color of robot robot1 from color white to color black, move the robot robot1 from the tile tile_9 to the tile tile_6 going downwards, use robot robot1 to paint the tile tile_9 above the tile tile_6 with the color black, change the color of robot robot1 from color black to color white, move the robot robot1 from the tile tile_6 to the tile tile_3 going downwards, use robot robot1 to paint the tile tile_6 above the tile tile_3 with the color white\"; which pair of consecutive actions can be removed from this plan?", "answer": "D"}
{"id": 6627093291695246124, "group": "action_justification_mcq", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 16 tiles and 2 robots. The tiles locations are: tile_3 is to the right of tile_2, tile_14 is to the right of tile_13, tile_15 is to the right of tile_14, tile_2 is to the right of tile_1, tile_16 is to the right of tile_15, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_10 is to the right of tile_9, tile_4 is to the right of tile_3, and tile_7 is to the right of tile_6. Further, tile_6 is down from tile_10, tile_1 is down from tile_5, tile_3 is down from tile_7, tile_5 is down from tile_9, tile_11 is down from tile_15, tile_12 is down from tile_16, tile_4 is down from tile_8, tile_7 is down from tile_11, tile_2 is down from tile_6, tile_10 is down from tile_14, tile_8 is down from tile_12, and tile_9 is down from tile_13 Currently, robot robot1 is at tile_8 and holding color white and robot robot2 is at tile_14 and holding color black; tile_15, tile_7, tile_11, tile_3, tile_9, tile_12, tile_4, tile_5, tile_10, tile_2, tile_13, tile_16, tile_6, and tile_1 are clear. The goal is to reach a state where the following facts hold: Tile tile_15 is painted in white color, Tile tile_9 is painted in black color, Tile tile_16 is painted in black color, Tile tile_10 is painted in white color, Tile tile_11 is painted in black color, Tile tile_14 is painted in black color, Tile tile_12 is painted in white color, Tile tile_13 is painted in white color, Tile tile_7 is painted in white color, Tile tile_6 is painted in black color, Tile tile_8 is painted in black color, and Tile tile_5 is painted in white color.", "question": "Given the plan: \"move the robot robot1 from the tile tile_8 to the tile tile_4 going downwards, move the robot robot2 from the tile tile_14 to the tile tile_10 going downwards, paint the tile tile_14 above the tile tile_10 with color black using the robot robot2, move robot robot1 from tile tile_4 to the left tile tile tile_3, alter the color of the robot robot2 from color black to color white, move robot robot2 from tile tile_10 to the left tile tile tile_9, paint the tile tile_13 above the tile tile_9 with color white using the robot robot2, move the robot robot2 from the tile tile_9 to the tile tile_5 going downwards, move the robot robot2 from the tile tile_5 to the tile tile_1 going downwards, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot1 up from tile tile_3 to tile tile_7, move the robot robot1 up from tile tile_7 to tile tile_11, paint the tile tile_15 above the tile tile_11 with color white using the robot robot1, move the robot robot1 from tile tile_11 to the right tile tile_12, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot1 from color white to color black, paint the tile tile_16 above the tile tile_12 with color black using the robot robot1, alter the color of the robot robot1 from color black to color white, move the robot robot1 from the tile tile_12 to the tile tile_8 going downwards, paint the tile tile_12 above the tile tile_8 with color white using the robot robot1, alter the color of the robot robot1 from color white to color black, move robot robot2 from tile tile_2 to the left tile tile tile_1, alter the color of the robot robot2 from color black to color white, move the robot robot1 from the tile tile_8 to the tile tile_4 going downwards, paint the tile tile_8 above the tile tile_4 with color black using the robot robot1, move robot robot1 from tile tile_4 to the left tile tile tile_3, move robot robot1 from tile tile_3 to the left tile tile tile_2, move the robot robot1 up from tile tile_2 to tile tile_6, move robot robot1 from tile tile_6 to the left tile tile tile_5, paint the tile tile_9 above the tile tile_5 with color black using the robot robot1, move the robot robot1 from tile tile_5 to the right tile tile_6, paint the tile tile_5 above the tile tile_1 with color white using the robot robot2, move the robot robot1 from the tile tile_6 to the tile tile_2 going downwards, move the robot robot1 from tile tile_2 to the right tile tile_3, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 up from tile tile_2 to tile tile_6, paint the tile tile_10 above the tile tile_6 with color white using the robot robot2, move the robot robot2 from the tile tile_6 to the tile tile_2 going downwards, alter the color of the robot robot2 from color white to color black, paint the tile tile_6 above the tile tile_2 with color black using the robot robot2, alter the color of the robot robot2 from color black to color white, move the robot robot1 up from tile tile_3 to tile tile_7, paint the tile tile_11 above the tile tile_7 with color black using the robot robot1, alter the color of the robot robot1 from color black to color white, move the robot robot1 from the tile tile_7 to the tile tile_3 going downwards, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, paint the tile tile_7 above the tile tile_3 with color white using the robot robot1, move robot robot2 from tile tile_2 to the left tile tile tile_1\"; which of the following actions can be removed from this plan and still have a valid plan? A. move the robot robot1 from tile tile_2 to the right tile tile_3. B. alter the color of the robot robot2 from color black to color white. C. alter the color of the robot robot2 from color white to color black. D. move robot robot2 from tile tile_2 to the left tile tile tile_1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move the robot robot1 from tile tile_2 to the right tile tile_3", "alter the color of the robot robot2 from color black to color white", "alter the color of the robot robot2 from color white to color black", "move robot robot2 from tile tile_2 to the left tile tile tile_1"]}, "query": "Given the plan: \"move the robot robot1 from the tile tile_8 to the tile tile_4 going downwards, move the robot robot2 from the tile tile_14 to the tile tile_10 going downwards, paint the tile tile_14 above the tile tile_10 with color black using the robot robot2, move robot robot1 from tile tile_4 to the left tile tile tile_3, alter the color of the robot robot2 from color black to color white, move robot robot2 from tile tile_10 to the left tile tile tile_9, paint the tile tile_13 above the tile tile_9 with color white using the robot robot2, move the robot robot2 from the tile tile_9 to the tile tile_5 going downwards, move the robot robot2 from the tile tile_5 to the tile tile_1 going downwards, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot1 up from tile tile_3 to tile tile_7, move the robot robot1 up from tile tile_7 to tile tile_11, paint the tile tile_15 above the tile tile_11 with color white using the robot robot1, move the robot robot1 from tile tile_11 to the right tile tile_12, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot1 from color white to color black, paint the tile tile_16 above the tile tile_12 with color black using the robot robot1, alter the color of the robot robot1 from color black to color white, move the robot robot1 from the tile tile_12 to the tile tile_8 going downwards, paint the tile tile_12 above the tile tile_8 with color white using the robot robot1, alter the color of the robot robot1 from color white to color black, move robot robot2 from tile tile_2 to the left tile tile tile_1, alter the color of the robot robot2 from color black to color white, move the robot robot1 from the tile tile_8 to the tile tile_4 going downwards, paint the tile tile_8 above the tile tile_4 with color black using the robot robot1, move robot robot1 from tile tile_4 to the left tile tile tile_3, move robot robot1 from tile tile_3 to the left tile tile tile_2, move the robot robot1 up from tile tile_2 to tile tile_6, move robot robot1 from tile tile_6 to the left tile tile tile_5, paint the tile tile_9 above the tile tile_5 with color black using the robot robot1, move the robot robot1 from tile tile_5 to the right tile tile_6, paint the tile tile_5 above the tile tile_1 with color white using the robot robot2, move the robot robot1 from the tile tile_6 to the tile tile_2 going downwards, move the robot robot1 from tile tile_2 to the right tile tile_3, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 up from tile tile_2 to tile tile_6, paint the tile tile_10 above the tile tile_6 with color white using the robot robot2, move the robot robot2 from the tile tile_6 to the tile tile_2 going downwards, alter the color of the robot robot2 from color white to color black, paint the tile tile_6 above the tile tile_2 with color black using the robot robot2, alter the color of the robot robot2 from color black to color white, move the robot robot1 up from tile tile_3 to tile tile_7, paint the tile tile_11 above the tile tile_7 with color black using the robot robot1, alter the color of the robot robot1 from color black to color white, move the robot robot1 from the tile tile_7 to the tile tile_3 going downwards, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move robot robot2 from tile tile_2 to the left tile tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, paint the tile tile_7 above the tile tile_3 with color white using the robot robot1, move robot robot2 from tile tile_2 to the left tile tile tile_1\"; which action can be removed from this plan?", "answer": "D"}
{"id": 3984237593774441233, "group": "action_justification_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball2 is at room1, ball1 and ball3 are at room2, ball4 is at room3. The goal is to reach a state where the following facts hold: Ball ball3 is in room room2, Ball ball1 is in room room1, Ball ball2 is in room room1, and Ball ball4 is at room1 location.", "question": "Given the plan: \"move the robot robot1 from room room2 to room room1, move the robot robot1 from room room1 to room room2, move the robot robot1 from room room2 to room room3, use the robot robot1 equipped with right1 gripper to retrieve the object ball4 from room room3, move the robot robot1 from room room3 to room room2, use the right1 gripper of robot robot1 to drop the object ball4 in room room2, use the robot robot1 equipped with left1 gripper to retrieve the object ball4 from room room2, use the robot robot1 equipped with right1 gripper to retrieve the object ball1 from room room2, move the robot robot1 from room room2 to room room1, use the right1 gripper of robot robot1 to drop the object ball1 in room room1, use the left1 gripper of robot robot1 to drop the object ball4 in room room1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. use the robot robot1 equipped with left1 gripper to retrieve the object ball4 from room room2 and use the robot robot1 equipped with right1 gripper to retrieve the object ball1 from room room2. B. use the right1 gripper of robot robot1 to drop the object ball1 in room room1 and use the left1 gripper of robot robot1 to drop the object ball4 in room room1. C. move the robot robot1 from room room2 to room room1 and move the robot robot1 from room room1 to room room2. D. use the right1 gripper of robot robot1 to drop the object ball4 in room room2 and use the robot robot1 equipped with left1 gripper to retrieve the object ball4 from room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the robot robot1 equipped with left1 gripper to retrieve the object ball4 from room room2 and use the robot robot1 equipped with right1 gripper to retrieve the object ball1 from room room2", "use the right1 gripper of robot robot1 to drop the object ball1 in room room1 and use the left1 gripper of robot robot1 to drop the object ball4 in room room1", "move the robot robot1 from room room2 to room room1 and move the robot robot1 from room room1 to room room2", "use the right1 gripper of robot robot1 to drop the object ball4 in room room2 and use the robot robot1 equipped with left1 gripper to retrieve the object ball4 from room room2"]}, "query": "Given the plan: \"move the robot robot1 from room room2 to room room1, move the robot robot1 from room room1 to room room2, move the robot robot1 from room room2 to room room3, use the robot robot1 equipped with right1 gripper to retrieve the object ball4 from room room3, move the robot robot1 from room room3 to room room2, use the right1 gripper of robot robot1 to drop the object ball4 in room room2, use the robot robot1 equipped with left1 gripper to retrieve the object ball4 from room room2, use the robot robot1 equipped with right1 gripper to retrieve the object ball1 from room room2, move the robot robot1 from room room2 to room room1, use the right1 gripper of robot robot1 to drop the object ball1 in room room1, use the left1 gripper of robot robot1 to drop the object ball4 in room room1\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": -1211718777830902104, "group": "action_justification_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball2 and ball1 are at room1, ball4 and ball3 are at room2. The goal is to reach a state where the following facts hold: Ball ball3 is in room room1, Ball ball1 is in room room1, Ball ball2 is in room room1, and Ball ball4 is in room room1.", "question": "Given the plan: \"move robot robot1 from room room1 to room room2, grasp the object ball4 from room room2 with the right1 gripper of robot robot1, grasp the object ball3 from room room2 with the left1 gripper of robot robot1, move robot robot1 from room room2 to room room1, drop object ball4 in room room1 using right1 gripper of robot robot1, grasp the object ball4 from room room1 with the right1 gripper of robot robot1, drop object ball4 in room room1 using right1 gripper of robot robot1, drop object ball3 in room room1 using left1 gripper of robot robot1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. grasp the object ball4 from room room1 with the right1 gripper of robot robot1 and drop object ball4 in room room1 using right1 gripper of robot robot1. B. drop object ball4 in room room1 using right1 gripper of robot robot1 and drop object ball3 in room room1 using left1 gripper of robot robot1. C. grasp the object ball3 from room room2 with the left1 gripper of robot robot1 and move robot robot1 from room room2 to room room1. D. move robot robot1 from room room2 to room room1 and drop object ball4 in room room1 using right1 gripper of robot robot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["grasp the object ball4 from room room1 with the right1 gripper of robot robot1 and drop object ball4 in room room1 using right1 gripper of robot robot1", "drop object ball4 in room room1 using right1 gripper of robot robot1 and drop object ball3 in room room1 using left1 gripper of robot robot1", "grasp the object ball3 from room room2 with the left1 gripper of robot robot1 and move robot robot1 from room room2 to room room1", "move robot robot1 from room room2 to room room1 and drop object ball4 in room room1 using right1 gripper of robot robot1"]}, "query": "Given the plan: \"move robot robot1 from room room1 to room room2, grasp the object ball4 from room room2 with the right1 gripper of robot robot1, grasp the object ball3 from room room2 with the left1 gripper of robot robot1, move robot robot1 from room room2 to room room1, drop object ball4 in room room1 using right1 gripper of robot robot1, grasp the object ball4 from room room1 with the right1 gripper of robot robot1, drop object ball4 in room room1 using right1 gripper of robot robot1, drop object ball3 in room room1 using left1 gripper of robot robot1\"; which pair of consecutive actions can be removed from this plan?", "answer": "A"}
{"id": 7072413652510895689, "group": "action_justification_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball2 is at room1, ball1 and ball3 are at room2, ball4 is at room3. The goal is to reach a state where the following facts hold: Ball ball3 is in room room2, Ball ball1 is in room room1, Ball ball2 is in room room1, and Ball ball4 is in room room1.", "question": "Given the plan: \"grasp the object ball1 from room room2 with the left1 gripper of robot robot1, move the robot robot1 from room room2 to room room3, grasp the object ball4 from room room3 with the right1 gripper of robot robot1, place the object ball4 in the room room3 using the robot robot1 with right1 gripper, grasp the object ball4 from room room3 with the right1 gripper of robot robot1, move the robot robot1 from room room3 to room room1, place the object ball4 in the room room1 using the robot robot1 with right1 gripper, place the object ball1 in the room room1 using the robot robot1 with left1 gripper, move the robot robot1 from room room1 to room room2\"; which of the following actions can be removed from this plan and still have a valid plan? A. place the object ball4 in the room room3 using the robot robot1 with right1 gripper. B. move the robot robot1 from room room3 to room room1. C. place the object ball4 in the room room1 using the robot robot1 with right1 gripper. D. move the robot robot1 from room room1 to room room2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["place the object ball4 in the room room3 using the robot robot1 with right1 gripper", "move the robot robot1 from room room3 to room room1", "place the object ball4 in the room room1 using the robot robot1 with right1 gripper", "move the robot robot1 from room room1 to room room2"]}, "query": "Given the plan: \"grasp the object ball1 from room room2 with the left1 gripper of robot robot1, move the robot robot1 from room room2 to room room3, grasp the object ball4 from room room3 with the right1 gripper of robot robot1, place the object ball4 in the room room3 using the robot robot1 with right1 gripper, grasp the object ball4 from room room3 with the right1 gripper of robot robot1, move the robot robot1 from room room3 to room room1, place the object ball4 in the room room1 using the robot robot1 with right1 gripper, place the object ball1 in the room room1 using the robot robot1 with left1 gripper, move the robot robot1 from room room1 to room room2\"; which action can be removed from this plan?", "answer": "D"}
{"id": -5234060494111284908, "group": "action_justification_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball2 and ball1 are at room1, ball4 and ball3 are at room2. The goal is to reach a state where the following facts hold: Ball ball3 is in room room1, Ball ball1 is in room room1, Ball ball2 is in room room1, and Ball ball4 is in room room1.", "question": "Given the plan: \"transfer the robot robot1 from the room room1 to the room room2, grasp the object ball3 from room room2 with the left1 gripper of robot robot1, grasp the object ball4 from room room2 with the right1 gripper of robot robot1, transfer the robot robot1 from the room room2 to the room room1, use the left1 gripper of robot robot1 to drop the object ball3 in room room1, grasp the object ball3 from room room1 with the left1 gripper of robot robot1, use the left1 gripper of robot robot1 to drop the object ball3 in room room1, use the right1 gripper of robot robot1 to drop the object ball4 in room room1\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. use the left1 gripper of robot robot1 to drop the object ball3 in room room1 and use the right1 gripper of robot robot1 to drop the object ball4 in room room1. B. grasp the object ball4 from room room2 with the right1 gripper of robot robot1 and transfer the robot robot1 from the room room2 to the room room1. C. use the left1 gripper of robot robot1 to drop the object ball3 in room room1 and grasp the object ball3 from room room1 with the left1 gripper of robot robot1. D. grasp the object ball3 from room room2 with the left1 gripper of robot robot1 and grasp the object ball4 from room room2 with the right1 gripper of robot robot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["use the left1 gripper of robot robot1 to drop the object ball3 in room room1 and use the right1 gripper of robot robot1 to drop the object ball4 in room room1", "grasp the object ball4 from room room2 with the right1 gripper of robot robot1 and transfer the robot robot1 from the room room2 to the room room1", "use the left1 gripper of robot robot1 to drop the object ball3 in room room1 and grasp the object ball3 from room room1 with the left1 gripper of robot robot1", "grasp the object ball3 from room room2 with the left1 gripper of robot robot1 and grasp the object ball4 from room room2 with the right1 gripper of robot robot1"]}, "query": "Given the plan: \"transfer the robot robot1 from the room room1 to the room room2, grasp the object ball3 from room room2 with the left1 gripper of robot robot1, grasp the object ball4 from room room2 with the right1 gripper of robot robot1, transfer the robot robot1 from the room room2 to the room room1, use the left1 gripper of robot robot1 to drop the object ball3 in room room1, grasp the object ball3 from room room1 with the left1 gripper of robot robot1, use the left1 gripper of robot robot1 to drop the object ball3 in room room1, use the right1 gripper of robot robot1 to drop the object ball4 in room room1\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": -5876148848814339212, "group": "action_justification_mcq", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball2 is at room1, ball1 and ball3 are at room2, ball4 is at room3. The goal is to reach a state where the following facts hold: Ball ball3 is in room room2, Ball ball1 is in room room1, Ball ball2 is at room1 location, and Ball ball4 is at room1 location.", "question": "Given the plan: \"pick up the object ball1 with the robot robot1 using the right1 gripper from the room room2, transfer the robot robot1 from the room room2 to the room room3, drop the object ball1 in room room3 using robot robot1 with the right1 gripper, pick up the object ball1 with the robot robot1 using the left1 gripper from the room room3, pick up the object ball4 with the robot robot1 using the right1 gripper from the room room3, transfer the robot robot1 from the room room3 to the room room1, drop the object ball4 in room room1 using robot robot1 with the right1 gripper, drop the object ball1 in room room1 using robot robot1 with the left1 gripper, transfer the robot robot1 from the room room1 to the room room2\"; which of the following actions can be removed from this plan and still have a valid plan? A. drop the object ball1 in room room3 using robot robot1 with the right1 gripper. B. transfer the robot robot1 from the room room2 to the room room3. C. transfer the robot robot1 from the room room1 to the room room2. D. pick up the object ball4 with the robot robot1 using the right1 gripper from the room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["drop the object ball1 in room room3 using robot robot1 with the right1 gripper", "transfer the robot robot1 from the room room2 to the room room3", "transfer the robot robot1 from the room room1 to the room room2", "pick up the object ball4 with the robot robot1 using the right1 gripper from the room room3"]}, "query": "Given the plan: \"pick up the object ball1 with the robot robot1 using the right1 gripper from the room room2, transfer the robot robot1 from the room room2 to the room room3, drop the object ball1 in room room3 using robot robot1 with the right1 gripper, pick up the object ball1 with the robot robot1 using the left1 gripper from the room room3, pick up the object ball4 with the robot robot1 using the right1 gripper from the room room3, transfer the robot robot1 from the room room3 to the room room1, drop the object ball4 in room room1 using robot robot1 with the right1 gripper, drop the object ball1 in room room1 using robot robot1 with the left1 gripper, transfer the robot robot1 from the room room1 to the room room2\"; which action can be removed from this plan?", "answer": "C"}
{"id": 2066499650596870774, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera1 can be calibrated on objective1. Camera camera0 can be calibrated on objective2. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint2 and waypoint0. Soil can be sampled at the following location(s): waypoint0 and waypoint2. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, Soil data was communicated from waypoint waypoint2;, Soil data was communicated from waypoint waypoint0;, Image objective0 was communicated in mode low_res, and Rock data was communicated from waypoint waypoint2;.", "question": "Given the plan: \"collect a rock sample from waypoint waypoint2 using rover rover1 and store it in store store1, transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2, sample the soil at waypoint waypoint2 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint2, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on rover rover0 for the objective objective1 at the waypoint waypoint1, take a picture of the objective objective1 in mode high_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on rover rover0 for the objective objective1 at the waypoint waypoint1, take a picture of the objective objective1 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on rover rover0 for the objective objective1 at the waypoint waypoint1, take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, drop store store0 of rover rover0, sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, drop store store0 of rover rover0, collect a rock sample from waypoint waypoint0 using rover rover0 and store it in store store0, transmit the rock data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0\"; which of the following actions can be removed from this plan and still have a valid plan? A. transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0. B. navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2. C. sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0. D. take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0", "navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2", "sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0", "take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1"]}, "query": "Given the plan: \"collect a rock sample from waypoint waypoint2 using rover rover1 and store it in store store1, transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2, sample the soil at waypoint waypoint2 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint2, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on rover rover0 for the objective objective1 at the waypoint waypoint1, take a picture of the objective objective1 in mode high_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on rover rover0 for the objective objective1 at the waypoint waypoint1, take a picture of the objective objective1 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on rover rover0 for the objective objective1 at the waypoint waypoint1, take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint1, navigate rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate rover rover0 from waypoint waypoint2 to waypoint waypoint0, drop store store0 of rover rover0, sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, drop store store0 of rover rover0, collect a rock sample from waypoint waypoint0 using rover rover0 and store it in store store0, transmit the rock data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0\"; which action can be removed from this plan?", "answer": "A"}
{"id": 5696217647687082383, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera1 supports high_res. Camera camera2 supports high_res. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint2 and waypoint0. Soil can be sampled at the following location(s): waypoint1 and waypoint0. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, Image objective0 was communicated in mode high_res, Soil data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint1;, and Rock data was communicated from waypoint waypoint2;.", "question": "Given the plan: \"sample the soil at waypoint waypoint1 using the rover rover1, then store it in the storage unit store1, navigate with rover rover0 to waypoint waypoint0 from waypoint waypoint1, sample the rock at waypoint waypoint0 with rover rover0 and store it in store store0, transmit the rock data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0, calibrate camera camera2 on rover rover0 for objective objective1 at waypoint waypoint0, take an image of objective objective1 in mode high_res using camera camera2 on rover rover0 from waypoint waypoint0, communicate the image data of target objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, navigate with rover rover1 to waypoint waypoint0 from waypoint waypoint1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1, navigate with rover rover0 to waypoint waypoint1 from waypoint waypoint0, calibrate camera camera2 on rover rover0 for objective objective1 at waypoint waypoint1, navigate with rover rover0 to waypoint waypoint2 from waypoint waypoint1, take an image of objective objective0 in mode high_res using camera camera2 on rover rover0 from waypoint waypoint2, communicate the image data of target objective0 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, if storey is full, drop store store0 of rover rover0, sample the rock at waypoint waypoint2 with rover rover0 and store it in store store0, transmit the rock data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2, if storey is full, drop store store1 of rover rover1, sample the soil at waypoint waypoint0 using the rover rover1, then store it in the storage unit store1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1\"; which of the following actions can be removed from this plan and still have a valid plan? A. communicate the image data of target objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1. B. transmit the rock data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2. C. communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1. D. sample the soil at waypoint waypoint0 using the rover rover1, then store it in the storage unit store1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate the image data of target objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1", "transmit the rock data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2", "communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1", "sample the soil at waypoint waypoint0 using the rover rover1, then store it in the storage unit store1"]}, "query": "Given the plan: \"sample the soil at waypoint waypoint1 using the rover rover1, then store it in the storage unit store1, navigate with rover rover0 to waypoint waypoint0 from waypoint waypoint1, sample the rock at waypoint waypoint0 with rover rover0 and store it in store store0, transmit the rock data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0, calibrate camera camera2 on rover rover0 for objective objective1 at waypoint waypoint0, take an image of objective objective1 in mode high_res using camera camera2 on rover rover0 from waypoint waypoint0, communicate the image data of target objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, navigate with rover rover1 to waypoint waypoint0 from waypoint waypoint1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1, navigate with rover rover0 to waypoint waypoint1 from waypoint waypoint0, calibrate camera camera2 on rover rover0 for objective objective1 at waypoint waypoint1, navigate with rover rover0 to waypoint waypoint2 from waypoint waypoint1, take an image of objective objective0 in mode high_res using camera camera2 on rover rover0 from waypoint waypoint2, communicate the image data of target objective0 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, if storey is full, drop store store0 of rover rover0, sample the rock at waypoint waypoint2 with rover rover0 and store it in store store0, transmit the rock data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2, if storey is full, drop store store1 of rover rover1, sample the soil at waypoint waypoint0 using the rover rover1, then store it in the storage unit store1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1\"; which action can be removed from this plan?", "answer": "C"}
{"id": -7050458051006358400, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective2. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera1 supports high_res. Camera camera2 supports high_res. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Image objective1 was communicated in mode high_res.", "question": "Given the plan: \"guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1, guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2, calibrate the camera camera0 on rover rover1 for the objective objective0 at the waypoint waypoint2, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint1, guide the rover rover1 from waypoint waypoint1 to waypoint waypoint0, sample soil at waypoint waypoint0 with rover rover1 and store in the store store1, communicate image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, guide the rover rover0 from waypoint waypoint1 to waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera1 on the rover rover1 at waypoint waypoint0, sample the rock at waypoint waypoint0 with rover rover0 and store it in store store0, communicate the rock data from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0, communicate image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, transmit soil data from rover rover1 located at waypoint waypoint0 to lander general located at waypoint waypoint1 using soil analysis of waypoint waypoint0\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. communicate image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1 and transmit soil data from rover rover1 located at waypoint waypoint0 to lander general located at waypoint waypoint1 using soil analysis of waypoint waypoint0. B. capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint1 and guide the rover rover1 from waypoint waypoint1 to waypoint waypoint0. C. guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2 and guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1. D. calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2 and calibrate the camera camera0 on rover rover1 for the objective objective0 at the waypoint waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["communicate image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1 and transmit soil data from rover rover1 located at waypoint waypoint0 to lander general located at waypoint waypoint1 using soil analysis of waypoint waypoint0", "capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint1 and guide the rover rover1 from waypoint waypoint1 to waypoint waypoint0", "guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2 and guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1", "calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2 and calibrate the camera camera0 on rover rover1 for the objective objective0 at the waypoint waypoint2"]}, "query": "Given the plan: \"guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1, guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2, calibrate the camera camera1 on rover rover1 for the objective objective0 at the waypoint waypoint2, calibrate the camera camera0 on rover rover1 for the objective objective0 at the waypoint waypoint2, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint1, guide the rover rover1 from waypoint waypoint1 to waypoint waypoint0, sample soil at waypoint waypoint0 with rover rover1 and store in the store store1, communicate image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, guide the rover rover0 from waypoint waypoint1 to waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera1 on the rover rover1 at waypoint waypoint0, sample the rock at waypoint waypoint0 with rover rover0 and store it in store store0, communicate the rock data from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0, communicate image data of objective objective1 in mode high_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, transmit soil data from rover rover1 located at waypoint waypoint0 to lander general located at waypoint waypoint1 using soil analysis of waypoint waypoint0\"; which pair of consecutive actions can be removed from this plan?", "answer": "C"}
{"id": -1911038771589290469, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera1 can be calibrated on objective1. Camera camera0 can be calibrated on objective2. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint2 and waypoint0. Soil can be sampled at the following location(s): waypoint0 and waypoint2. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, Soil data was communicated from waypoint waypoint2;, Soil data was communicated from waypoint waypoint0;, Image objective0 was communicated in mode low_res, and Rock data was communicated from waypoint waypoint2;.", "question": "Given the plan: \"sample the rock at waypoint waypoint2 with rover rover1 and store it in store store1, communicate the rock data from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint1 via waypoint waypoint2, sample the soil at waypoint waypoint2 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint2, navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint1, capture an image of objective objective1 in mode high_res using the camera camera1 on the rover rover0 from waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint1, capture an image of objective objective1 in mode low_res using the camera camera1 on the rover rover0 from waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint1, capture an image of objective objective0 in mode low_res using the camera camera1 on the rover rover0 from waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint0, empty the store store0 from rover rover0, sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, empty the store store0 from rover rover0, sample the rock at waypoint waypoint0 with rover rover0 and store it in store store0, communicate the rock data from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0, navigate with rover rover0 from waypoint waypoint0 to waypoint waypoint2\"; which of the following actions can be removed from this plan and still have a valid plan? A. capture an image of objective objective1 in mode high_res using the camera camera1 on the rover rover0 from waypoint waypoint1. B. empty the store store0 from rover rover0. C. navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint0. D. navigate with rover rover0 from waypoint waypoint0 to waypoint waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["capture an image of objective objective1 in mode high_res using the camera camera1 on the rover rover0 from waypoint waypoint1", "empty the store store0 from rover rover0", "navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint0", "navigate with rover rover0 from waypoint waypoint0 to waypoint waypoint2"]}, "query": "Given the plan: \"sample the rock at waypoint waypoint2 with rover rover1 and store it in store store1, communicate the rock data from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint1 via waypoint waypoint2, sample the soil at waypoint waypoint2 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint2, navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint1, capture an image of objective objective1 in mode high_res using the camera camera1 on the rover rover0 from waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint1, capture an image of objective objective1 in mode low_res using the camera camera1 on the rover rover0 from waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at waypoint waypoint1, capture an image of objective objective0 in mode low_res using the camera camera1 on the rover rover0 from waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint0, empty the store store0 from rover rover0, sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, empty the store store0 from rover rover0, sample the rock at waypoint waypoint0 with rover rover0 and store it in store store0, communicate the rock data from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0, navigate with rover rover0 from waypoint waypoint0 to waypoint waypoint2\"; which action can be removed from this plan?", "answer": "D"}
{"id": -480819846947890286, "group": "action_justification_mcq", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective2. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera1 supports high_res. Camera camera2 supports high_res. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Image objective1 was communicated in mode high_res.", "question": "Given the plan: \"navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint2, calibrate the camera camera0 on rover rover1 for the objective objective0 at the waypoint waypoint2, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1, navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint0, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint0, sample the soil at waypoint waypoint0 with rover rover1 and store it in store store1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate the image data of target objective1 in mode low_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint0, calibrate the camera camera2 on rover rover0 for the objective objective2 at the waypoint waypoint0, collect a sample from the waypoint waypoint0 using the rover rover0 and store it in the store store0, communicate the rock data from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0, communicate the image data of target objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, empty the content from store store1 of the rover rover1\"; which of the following actions can be removed from this plan and still have a valid plan? A. capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0. B. empty the content from store store1 of the rover rover1. C. navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint0. D. sample the soil at waypoint waypoint0 with rover rover1 and store it in store store1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0", "empty the content from store store1 of the rover rover1", "navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint0", "sample the soil at waypoint waypoint0 with rover rover1 and store it in store store1"]}, "query": "Given the plan: \"navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint2, calibrate the camera camera0 on rover rover1 for the objective objective0 at the waypoint waypoint2, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1, navigate with rover rover1 from waypoint waypoint1 to waypoint waypoint0, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint0, sample the soil at waypoint waypoint0 with rover rover1 and store it in store store1, communicate soil data from rover rover1 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate the image data of target objective1 in mode low_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint0, calibrate the camera camera2 on rover rover0 for the objective objective2 at the waypoint waypoint0, collect a sample from the waypoint waypoint0 using the rover rover0 and store it in the store store0, communicate the rock data from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0, communicate the image data of target objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, empty the content from store store1 of the rover rover1\"; which action can be removed from this plan?", "answer": "B"}
{"id": 4683175702457515702, "group": "action_justification_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. Currently, the robot is in place loc-x0-y3.Place loc-x0-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x1-y1 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x0-y0 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y2 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, and Place loc-x0-y3 has been visited.", "question": "Given the plan: \"travel from loc-x0-y3 to loc-x0-y2, travel from loc-x0-y2 to loc-x0-y1, travel from loc-x0-y1 to loc-x0-y0, travel from loc-x0-y0 to loc-x1-y0, travel from loc-x1-y0 to loc-x2-y0, travel from loc-x2-y0 to loc-x3-y0, travel from loc-x3-y0 to loc-x2-y0, travel from loc-x2-y0 to loc-x2-y1, travel from loc-x2-y1 to loc-x1-y1, travel from loc-x1-y1 to loc-x1-y2, travel from loc-x1-y2 to loc-x1-y3, travel from loc-x1-y3 to loc-x2-y3, travel from loc-x2-y3 to loc-x2-y2, travel from loc-x2-y2 to loc-x3-y2, travel from loc-x3-y2 to loc-x3-y3, travel from loc-x3-y3 to loc-x2-y3\"; which of the following actions can be removed from this plan and still have a valid plan? A. travel from loc-x2-y0 to loc-x3-y0. B. travel from loc-x3-y3 to loc-x2-y3. C. travel from loc-x0-y1 to loc-x0-y0. D. travel from loc-x2-y1 to loc-x1-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel from loc-x2-y0 to loc-x3-y0", "travel from loc-x3-y3 to loc-x2-y3", "travel from loc-x0-y1 to loc-x0-y0", "travel from loc-x2-y1 to loc-x1-y1"]}, "query": "Given the plan: \"travel from loc-x0-y3 to loc-x0-y2, travel from loc-x0-y2 to loc-x0-y1, travel from loc-x0-y1 to loc-x0-y0, travel from loc-x0-y0 to loc-x1-y0, travel from loc-x1-y0 to loc-x2-y0, travel from loc-x2-y0 to loc-x3-y0, travel from loc-x3-y0 to loc-x2-y0, travel from loc-x2-y0 to loc-x2-y1, travel from loc-x2-y1 to loc-x1-y1, travel from loc-x1-y1 to loc-x1-y2, travel from loc-x1-y2 to loc-x1-y3, travel from loc-x1-y3 to loc-x2-y3, travel from loc-x2-y3 to loc-x2-y2, travel from loc-x2-y2 to loc-x3-y2, travel from loc-x3-y2 to loc-x3-y3, travel from loc-x3-y3 to loc-x2-y3\"; which action can be removed from this plan?", "answer": "B"}
{"id": 9123044859157710145, "group": "action_justification_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3, loc-x0-y0, and loc-x3-y3. Currently, the robot is in place loc-x3-y2.Place loc-x3-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x1-y1 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, and Place loc-x3-y2 has been visited.", "question": "Given the plan: \"go to loc-x3-y1 from loc-x3-y2, go to loc-x3-y0 from loc-x3-y1, go to loc-x2-y0 from loc-x3-y0, go to loc-x1-y0 from loc-x2-y0, go to loc-x1-y1 from loc-x1-y0, go to loc-x0-y1 from loc-x1-y1, go to loc-x0-y2 from loc-x0-y1, go to loc-x1-y2 from loc-x0-y2, go to loc-x1-y3 from loc-x1-y2, go to loc-x2-y3 from loc-x1-y3, go to loc-x2-y2 from loc-x2-y3, go to loc-x2-y1 from loc-x2-y2, go to loc-x2-y2 from loc-x2-y1\"; which of the following actions can be removed from this plan and still have a valid plan? A. go to loc-x2-y3 from loc-x1-y3. B. go to loc-x0-y1 from loc-x1-y1. C. go to loc-x3-y1 from loc-x3-y2. D. go to loc-x2-y2 from loc-x2-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["go to loc-x2-y3 from loc-x1-y3", "go to loc-x0-y1 from loc-x1-y1", "go to loc-x3-y1 from loc-x3-y2", "go to loc-x2-y2 from loc-x2-y1"]}, "query": "Given the plan: \"go to loc-x3-y1 from loc-x3-y2, go to loc-x3-y0 from loc-x3-y1, go to loc-x2-y0 from loc-x3-y0, go to loc-x1-y0 from loc-x2-y0, go to loc-x1-y1 from loc-x1-y0, go to loc-x0-y1 from loc-x1-y1, go to loc-x0-y2 from loc-x0-y1, go to loc-x1-y2 from loc-x0-y2, go to loc-x1-y3 from loc-x1-y2, go to loc-x2-y3 from loc-x1-y3, go to loc-x2-y2 from loc-x2-y3, go to loc-x2-y1 from loc-x2-y2, go to loc-x2-y2 from loc-x2-y1\"; which action can be removed from this plan?", "answer": "D"}
{"id": -3565744594003096499, "group": "action_justification_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3, loc-x0-y0, and loc-x3-y3. Currently, the robot is in place loc-x3-y2.Place loc-x3-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x1-y1 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, and Place loc-x3-y2 has been visited.", "question": "Given the plan: \"travel from the current position loc-x3-y2 to the next position loc-x3-y1, travel from the current position loc-x3-y1 to the next position loc-x3-y0, travel from the current position loc-x3-y0 to the next position loc-x2-y0, travel from the current position loc-x2-y0 to the next position loc-x2-y1, travel from the current position loc-x2-y1 to the next position loc-x1-y1, travel from the current position loc-x1-y1 to the next position loc-x1-y0, travel from the current position loc-x1-y0 to the next position loc-x1-y1, travel from the current position loc-x1-y1 to the next position loc-x0-y1, travel from the current position loc-x0-y1 to the next position loc-x0-y2, travel from the current position loc-x0-y2 to the next position loc-x1-y2, travel from the current position loc-x1-y2 to the next position loc-x1-y3, travel from the current position loc-x1-y3 to the next position loc-x2-y3, travel from the current position loc-x2-y3 to the next position loc-x2-y2, travel from the current position loc-x2-y2 to the next position loc-x2-y3\"; which of the following actions can be removed from this plan and still have a valid plan? A. travel from the current position loc-x3-y2 to the next position loc-x3-y1. B. travel from the current position loc-x2-y2 to the next position loc-x2-y3. C. travel from the current position loc-x1-y1 to the next position loc-x1-y0. D. travel from the current position loc-x2-y1 to the next position loc-x1-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["travel from the current position loc-x3-y2 to the next position loc-x3-y1", "travel from the current position loc-x2-y2 to the next position loc-x2-y3", "travel from the current position loc-x1-y1 to the next position loc-x1-y0", "travel from the current position loc-x2-y1 to the next position loc-x1-y1"]}, "query": "Given the plan: \"travel from the current position loc-x3-y2 to the next position loc-x3-y1, travel from the current position loc-x3-y1 to the next position loc-x3-y0, travel from the current position loc-x3-y0 to the next position loc-x2-y0, travel from the current position loc-x2-y0 to the next position loc-x2-y1, travel from the current position loc-x2-y1 to the next position loc-x1-y1, travel from the current position loc-x1-y1 to the next position loc-x1-y0, travel from the current position loc-x1-y0 to the next position loc-x1-y1, travel from the current position loc-x1-y1 to the next position loc-x0-y1, travel from the current position loc-x0-y1 to the next position loc-x0-y2, travel from the current position loc-x0-y2 to the next position loc-x1-y2, travel from the current position loc-x1-y2 to the next position loc-x1-y3, travel from the current position loc-x1-y3 to the next position loc-x2-y3, travel from the current position loc-x2-y3 to the next position loc-x2-y2, travel from the current position loc-x2-y2 to the next position loc-x2-y3\"; which action can be removed from this plan?", "answer": "B"}
{"id": -1666730962947091339, "group": "action_justification_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3, loc-x0-y0, and loc-x3-y3. Currently, the robot is in place loc-x3-y2.Place loc-x3-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x1-y1 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, and Place loc-x3-y2 has been visited.", "question": "Given the plan: \"navigate from loc-x3-y2 to loc-x2-y2, navigate from loc-x2-y2 to loc-x2-y1, navigate from loc-x2-y1 to loc-x3-y1, navigate from loc-x3-y1 to loc-x3-y0, navigate from loc-x3-y0 to loc-x2-y0, navigate from loc-x2-y0 to loc-x1-y0, navigate from loc-x1-y0 to loc-x1-y1, navigate from loc-x1-y1 to loc-x0-y1, navigate from loc-x0-y1 to loc-x0-y2, navigate from loc-x0-y2 to loc-x1-y2, navigate from loc-x1-y2 to loc-x1-y3, navigate from loc-x1-y3 to loc-x1-y2, navigate from loc-x1-y2 to loc-x1-y3, navigate from loc-x1-y3 to loc-x2-y3\"; which of the following pairs of consecutive actions can be removed from this plan and still have a valid plan? A. navigate from loc-x2-y2 to loc-x2-y1 and navigate from loc-x2-y1 to loc-x3-y1. B. navigate from loc-x1-y2 to loc-x1-y3 and navigate from loc-x1-y3 to loc-x1-y2. C. navigate from loc-x1-y0 to loc-x1-y1 and navigate from loc-x1-y1 to loc-x0-y1. D. navigate from loc-x1-y2 to loc-x1-y3 and navigate from loc-x1-y3 to loc-x2-y3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["navigate from loc-x2-y2 to loc-x2-y1 and navigate from loc-x2-y1 to loc-x3-y1", "navigate from loc-x1-y2 to loc-x1-y3 and navigate from loc-x1-y3 to loc-x1-y2", "navigate from loc-x1-y0 to loc-x1-y1 and navigate from loc-x1-y1 to loc-x0-y1", "navigate from loc-x1-y2 to loc-x1-y3 and navigate from loc-x1-y3 to loc-x2-y3"]}, "query": "Given the plan: \"navigate from loc-x3-y2 to loc-x2-y2, navigate from loc-x2-y2 to loc-x2-y1, navigate from loc-x2-y1 to loc-x3-y1, navigate from loc-x3-y1 to loc-x3-y0, navigate from loc-x3-y0 to loc-x2-y0, navigate from loc-x2-y0 to loc-x1-y0, navigate from loc-x1-y0 to loc-x1-y1, navigate from loc-x1-y1 to loc-x0-y1, navigate from loc-x0-y1 to loc-x0-y2, navigate from loc-x0-y2 to loc-x1-y2, navigate from loc-x1-y2 to loc-x1-y3, navigate from loc-x1-y3 to loc-x1-y2, navigate from loc-x1-y2 to loc-x1-y3, navigate from loc-x1-y3 to loc-x2-y3\"; which pair of consecutive actions can be removed from this plan?", "answer": "B"}
{"id": -2305385574017156916, "group": "action_justification_mcq", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3, loc-x0-y0, and loc-x3-y3. Currently, the robot is in place loc-x3-y2.Place loc-x3-y2 has been visited. The goal is to reach a state where the following facts hold: Place loc-x1-y1 has been visited, Place loc-x0-y1 has been visited, Place loc-x1-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x0-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x3-y1 has been visited, Place loc-x1-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, and Place loc-x3-y2 has been visited.", "question": "Given the plan: \"move to place loc-x2-y2 from place loc-x3-y2, move to place loc-x2-y3 from place loc-x2-y2, move to place loc-x1-y3 from place loc-x2-y3, move to place loc-x1-y2 from place loc-x1-y3, move to place loc-x0-y2 from place loc-x1-y2, move to place loc-x0-y1 from place loc-x0-y2, move to place loc-x1-y1 from place loc-x0-y1, move to place loc-x2-y1 from place loc-x1-y1, move to place loc-x3-y1 from place loc-x2-y1, move to place loc-x3-y0 from place loc-x3-y1, move to place loc-x2-y0 from place loc-x3-y0, move to place loc-x1-y0 from place loc-x2-y0, move to place loc-x1-y1 from place loc-x1-y0\"; which of the following actions can be removed from this plan and still have a valid plan? A. move to place loc-x1-y1 from place loc-x1-y0. B. move to place loc-x1-y1 from place loc-x0-y1. C. move to place loc-x3-y1 from place loc-x2-y1. D. move to place loc-x0-y1 from place loc-x0-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["move to place loc-x1-y1 from place loc-x1-y0", "move to place loc-x1-y1 from place loc-x0-y1", "move to place loc-x3-y1 from place loc-x2-y1", "move to place loc-x0-y1 from place loc-x0-y2"]}, "query": "Given the plan: \"move to place loc-x2-y2 from place loc-x3-y2, move to place loc-x2-y3 from place loc-x2-y2, move to place loc-x1-y3 from place loc-x2-y3, move to place loc-x1-y2 from place loc-x1-y3, move to place loc-x0-y2 from place loc-x1-y2, move to place loc-x0-y1 from place loc-x0-y2, move to place loc-x1-y1 from place loc-x0-y1, move to place loc-x2-y1 from place loc-x1-y1, move to place loc-x3-y1 from place loc-x2-y1, move to place loc-x3-y0 from place loc-x3-y1, move to place loc-x2-y0 from place loc-x3-y0, move to place loc-x1-y0 from place loc-x2-y0, move to place loc-x1-y1 from place loc-x1-y0\"; which action can be removed from this plan?", "answer": "A"}
