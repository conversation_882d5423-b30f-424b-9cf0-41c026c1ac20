{"id": 1268984985790259256, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0, with the car c1 on board. The cars are at locations as follows: c0 and c2 are at l3.", "question": "Is it possible to transition to a state where the following holds: The ferry is empty and The ferry is at l2 location?", "answer": "yes"}
{"id": -3826593486022472982, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c2 is at l1; c0 and c1 are at l3.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l1 location?", "answer": "yes"}
{"id": 3086831723562678790, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c9, c2, and c7 are at l0; c3, c0, c6, and c5 are at l2; c1, c4, and c8 are at l1.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l0 location and Car c0 is on board the ferry?", "answer": "yes"}
{"id": 254220531529050744, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c1 is at l1; c2 and c0 are at l4.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l4 location and The ferry is at l0 location?", "answer": "no"}
{"id": 6273095533827635039, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c28, c31, c1, c32, c24, c22, c38, c41, c49, c5, c43, c21, c40, c39, c26, c10, c4, c27, c48, c47, c46, c36, c45, c34, c42, c7, c25, c37, and c12 are at l1; c17, c33, c2, c13, c20, c11, c16, c29, c30, c3, c14, c15, c8, c6, c18, c19, c0, c23, c9, c44, and c35 are at l0.", "question": "Is it possible to transition to a state where the following holds: Car c48 is on board the ferry and The ferry is at l0 location?", "answer": "yes"}
{"id": -1387152592430020428, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l2, with the car c35 on board. The cars are at locations as follows: c17, c16, c24, c1, c10, c6, c18, and c9 are at l0; c34, c4, c42, c20, c31, c13, c7, c49, c5, c37, c12, c23, c25, and c43 are at l3; c3, c0, c21, c39, c22, and c40 are at l2; c41, c28, c47, c26, c27, c45, c36, c30, c8, c38, c29, and c14 are at l4; c19, c32, c11, c48, c33, c15, c2, c46, and c44 are at l1.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l3 location?", "answer": "yes"}
{"id": -3167712919509616391, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c17 on board. The cars are at locations as follows: c28, c31, c1, c32, c24, c22, c38, c41, c49, c5, c43, c16, c40, c39, c4, c10, c27, c48, c15, c47, c46, c36, c45, c34, c42, c7, c25, and c37 are at l1; c33, c2, c13, c20, c11, c29, c30, c3, c14, c26, c8, c6, c18, c19, c0, c23, c12, c9, c44, c35, and c21 are at l0.", "question": "Is it possible to transition to a state where the following holds: The ferry is empty and Car c17 is at location l0?", "answer": "yes"}
{"id": -3526496914030249199, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c0, c2, and c1 are at l3.", "question": "Is it possible to transition to a state where the following holds: Ferry has car l4 on board and Car c2 is at location l3?", "answer": "no"}
{"id": -8510261219970440273, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c2 and c1 are at l3; c0 is at l0.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l0 location?", "answer": "yes"}
{"id": 1461482101892106760, "group": "reachable_atom_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l4 location and it is empty. The cars are at locations as follows: c17, c16, c45, c42, c24, c1, c36, c10, c4, c18, c48, and c44 are at l0; c34, c20, c6, c31, c13, c7, c37, c12, c23, and c25 are at l3; c43, c26, c3, c35, c21, c39, c22, and c40 are at l2; c41, c28, c0, c47, c27, c9, c30, c5, c8, c46, c38, c29, and c14 are at l4; c19, c32, c49, c11, c33, c15, and c2 are at l1.", "question": "Is it possible to transition to a state where the following holds: The ferry is at l3 location and Car c5 is on board the ferry?", "answer": "yes"}
{"id": 7474610897774792912, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, t1 is at l1-2, p3 and t0 are at l0-1, a0 is at l0-0, p0, p2, and p1 are in t0.", "question": "Is it possible to transition to a state where the following holds: l0-1 is in a0?", "answer": "no"}
{"id": -2981570131346945014, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0; l3-1, l3-2, and l3-0 are in c3; l4-0, l4-1, and l4-2 are in c4; l2-0, l2-1, and l2-2 are in c2. Currently, p2 and t2 are at l2-2, t4 and a0 are at l4-0, t1 is at l1-2, t0 is at l0-2, p1 is at l3-1, t3 is at l3-0, p0 and p3 are in a0.", "question": "Is it possible to transition to a state where the following holds: p0 is at l2-2 and p0 is at l0-2?", "answer": "no"}
{"id": 8810249779840829756, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, t0 and p3 are at l0-2, t1 is at l1-1, a0 is at l0-0, p2 is in a0, p0 and p1 are in t0.", "question": "Is it possible to transition to a state where the following holds: a0 is at l1-0?", "answer": "yes"}
{"id": -4540434072416334212, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, p2 and p0 are at l0-1, a0 is at l1-0, t1 is at l1-1, t0 and p3 are at l0-0, p1 is in a0.", "question": "Is it possible to transition to a state where the following holds: l0-0 is at t0 and p2 is at l0-1?", "answer": "no"}
{"id": -7845509779827378671, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, a0 is at l1-0, t1 is at l1-2, t0 and p3 are at l0-2, p0, p1, and p2 are in t0.", "question": "Is it possible to transition to a state where the following holds: p1 is in a0 and p1 is at l1-2?", "answer": "no"}
{"id": -3007841307099444791, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-6, l1-8, l1-2, l1-1, l1-7, l1-3, l1-0, l1-9, l1-4, and l1-5 are in c1; l3-3, l3-8, l3-6, l3-2, l3-0, l3-7, l3-1, l3-4, l3-9, and l3-5 are in c3; l2-5, l2-0, l2-4, l2-3, l2-7, l2-6, l2-8, l2-9, l2-1, and l2-2 are in c2; l4-3, l4-9, l4-4, l4-6, l4-2, l4-1, l4-7, l4-0, l4-5, and l4-8 are in c4; l0-4, l0-3, l0-8, l0-6, l0-0, l0-9, l0-5, l0-2, l0-1, and l0-7 are in c0. Currently, p2 and t3 are at l3-0, a0 is at l1-0, t4 is at l4-0, t0 is at l0-2, t1 is at l1-1, p1 is at l1-8, t2 is at l2-4, p0 and p3 are in t2.", "question": "Is it possible to transition to a state where the following holds: p3 is at l2-4 and p3 is in t3?", "answer": "no"}
{"id": -333617104970359206, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p2, p3, t0, and p0 are at l0-1, t1 is at l1-2, a0 is at l0-0, p1 is in t0.", "question": "Is it possible to transition to a state where the following holds: p0 is at l0-0 and p0 is at l0-1?", "answer": "no"}
{"id": -6277981076939153131, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-1, l1-0, and l1-2 are in c1; l0-0, l0-2, and l0-1 are in c0; l2-0, l2-1, and l2-2 are in c2. Currently, a0, p4, p1, and t1 are at l1-0, t0 is at l0-2, t2 is at l2-1, p0 and p3 are in t1, p2 is in t0.", "question": "Is it possible to transition to a state where the following holds: p3 is at l1-0 and t0 is at l0-0?", "answer": "yes"}
{"id": 5759802802624466637, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-6, l1-8, l1-2, l1-1, l1-7, l1-3, l1-0, l1-9, l1-4, and l1-5 are in c1; l3-3, l3-8, l3-6, l3-2, l3-0, l3-7, l3-1, l3-4, l3-9, and l3-5 are in c3; l2-5, l2-0, l2-4, l2-3, l2-7, l2-6, l2-8, l2-9, l2-1, and l2-2 are in c2; l4-3, l4-9, l4-4, l4-6, l4-2, l4-1, l4-7, l4-0, l4-5, and l4-8 are in c4; l0-4, l0-3, l0-8, l0-6, l0-0, l0-9, l0-5, l0-2, l0-1, and l0-7 are in c0. Currently, t2, p0, a0, and p1 are at l2-0, t0 is at l0-2, t4 and p2 are at l4-8, t3 is at l3-0, t1 is at l1-0, p3 is in t2.", "question": "Is it possible to transition to a state where the following holds: t2 is at l2-7 and t4 is at l4-3?", "answer": "yes"}
{"id": 2019778659293841791, "group": "reachable_atom_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, p2 and p0 are at l0-1, a0 and t1 are at l1-0, t0 is at l0-0, p1 is in t1, p3 is in a0.", "question": "Is it possible to transition to a state where the following holds: p0 is in t1 and p0 is in a0?", "answer": "no"}
{"id": 8256941752428165977, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_19. The following block(s) are on the table: block_13, block_2, block_20, block_10, and block_6. The following block(s) are stacked on top of another block: block_3 is on block_13, block_7 is on block_10, block_1 is on block_17, block_4 is on block_6, block_14 is on block_20, block_17 is on block_12, block_8 is on block_4, block_5 is on block_1, block_12 is on block_7, block_18 is on block_5, block_11 is on block_8, block_15 is on block_9, block_16 is on block_11, and block_9 is on block_2.", "question": "Is it possible to transition to a state where the following holds: The block block_6 is currently being held by the robotic arm and The robotic arm is holding block_4?", "answer": "no"}
{"id": -1138079910160729630, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_10, block_8, and block_6. The following block(s) are stacked on top of another block: block_5 is on block_9, block_7 is on block_10, block_4 is on block_6, block_2 is on block_5, block_1 is on block_8, block_9 is on block_1, and block_3 is on block_7.", "question": "Is it possible to transition to a state where the following holds: No blocks are placed on top of block_7 and The block block_3 is currently situated above the block block_4?", "answer": "yes"}
{"id": -3782707831614811594, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_2. The following block(s) are on the table: block_4, block_3, and block_5. The following block(s) is stacked on top of another block: block_1 is on block_5.", "question": "Is it possible to transition to a state where the following holds: The block block_3 is currently situated under the block block_5 and The block block_3 is on top of block block_3?", "answer": "no"}
{"id": 2544905356212068046, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_10, block_1, block_2, and block_8. The following block(s) are stacked on top of another block: block_6 is on block_8, block_7 is on block_10, block_9 is on block_5, block_3 is on block_1, and block_5 is on block_2.", "question": "Is it possible to transition to a state where the following holds: The block block_5 is on top of block block_6 and The block block_5 is currently being held by the robotic arm?", "answer": "no"}
{"id": -3253610256607851858, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_2. The following block(s) are stacked on top of another block: block_3 is on block_2, block_4 is on block_1, and block_5 is on block_4.", "question": "Is it possible to transition to a state where the following holds: Block block_5 is located on the table and No blocks are placed on top of block_4?", "answer": "yes"}
{"id": -1214666747726733564, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_7, block_14, block_16, block_18, block_20, block_1, block_10, block_19, and block_6. The following block(s) are stacked on top of another block: block_12 is on block_5, block_3 is on block_13, block_17 is on block_12, block_11 is on block_1, block_5 is on block_14, block_13 is on block_4, block_4 is on block_11, block_2 is on block_18, block_8 is on block_7, block_15 is on block_9, and block_9 is on block_2.", "question": "Is it possible to transition to a state where the following holds: block_7 is not obstructed by any other blocks and The block block_8 is on top of block block_15?", "answer": "yes"}
{"id": -3240788156726209860, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_5. The following block(s) are on the table: block_3 and block_1. The following block(s) are stacked on top of another block: block_4 is on block_2 and block_2 is on block_3.", "question": "Is it possible to transition to a state where the following holds: Block block_5 is clear and The block block_1 is currently being held by the robotic arm?", "answer": "yes"}
{"id": 3037388191648938111, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_19. The following block(s) are on the table: block_5, block_13, block_20, block_10, and block_6. The following block(s) are stacked on top of another block: block_3 is on block_13, block_7 is on block_10, block_1 is on block_17, block_4 is on block_6, block_14 is on block_20, block_2 is on block_14, block_17 is on block_12, block_8 is on block_4, block_12 is on block_7, block_18 is on block_5, block_11 is on block_8, block_15 is on block_9, block_16 is on block_11, and block_9 is on block_2.", "question": "Is it possible to transition to a state where the following holds: No blocks are placed on top of block_19 and No blocks are placed on top of block_5?", "answer": "yes"}
{"id": 2669741931983850416, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_3, block_1, and block_5. The following block(s) are stacked on top of another block: block_2 is on block_3 and block_4 is on block_5.", "question": "Is it possible to transition to a state where the following holds: The block block_1 is currently being held by the robotic arm and The block block_3 is currently being held by the robotic arm?", "answer": "no"}
{"id": 3001871075049492593, "group": "reachable_atom_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_10, block_1, block_2, block_8, and block_6. The following block(s) are stacked on top of another block: block_4 is on block_9, block_7 is on block_10, block_5 is on block_2, block_9 is on block_5, and block_3 is on block_7.", "question": "Is it possible to transition to a state where the following holds: The block block_2 is on top of block block_3 and The robotic arm is holding block_2?", "answer": "no"}
{"id": -6561961169987719768, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-3f and is holding key0-0. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-1 is at position f2-2f.", "question": "Is it possible to transition to a state where the following holds: Robot is not holding anything and Robot is at f2-3f location?", "answer": "yes"}
{"id": 7199958739360794837, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-0 is of shape shape1, Key key1-1 is of shape shape1.  Currently, the robot is at position f0-1f and its arm is empty. All the positions are open except the following: f3-4f has shape1 shaped lock, f2-1f has shape1 shaped lock. Key key1-1 is at position f3-0f. Key key1-0 is at position f1-0f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f1-2f location?", "answer": "yes"}
{"id": 2055922311707026235, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-3f and is holding key0-0. All the positions are open except the following: f2-2f has shape0 shaped lock, f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-1 is at position f2-2f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f1-1f location?", "answer": "yes"}
{"id": 3589815530864347982, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-4f and its arm is empty. All the positions are open except the following: f2-2f has shape0 shaped lock, f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-0 is at position f1-3f. Key key0-1 is at position f2-2f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f3-3f location?", "answer": "yes"}
{"id": -8389810127872957892, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-0 is of shape shape1, Key key1-1 is of shape shape1.  Currently, the robot is at position f0-0f and its arm is empty. All the positions are open except the following: f3-4f has shape1 shaped lock, f2-1f has shape1 shaped lock. Key key1-1 is at position f3-0f. Key key1-0 is at position f0-0f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f1-0f location and Robot is holding key1-0?", "answer": "yes"}
{"id": -3280748980533762447, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-3 is of shape shape0.  Currently, the robot is at position f1-1f and its arm is empty. All the positions are open except the following: f0-2f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f1-1f. Key key0-1 is at position f4-2f. Key key0-2 is at position f3-0f. Key key0-3 is at position f0-1f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f2-2f location?", "answer": "yes"}
{"id": 2290808842321697151, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0, Key key0-3 is of shape shape0.  Currently, the robot is at position f2-2f and is holding key0-1. All the positions are open except the following: f0-3f has shape0 shaped lock. Key key0-0 is at position f0-1f. Key key0-2 is at position f4-0f. Key key0-3 is at position f0-1f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f2-0f location?", "answer": "yes"}
{"id": -7745541852822579417, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-3f and its arm is empty. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-0 is at position f1-3f. Key key0-2 is at position f2-3f. Key key0-1 is at position f2-2f.", "question": "Is it possible to transition to a state where the following holds: Robot is at key0-0 location?", "answer": "no"}
{"id": -8845809395763048442, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f4-1f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f0-1f has shape0 shaped lock. Key key0-0 is at position f3-2f. Key key0-1 is at position f4-1f.", "question": "Is it possible to transition to a state where the following holds: Robot is at f4-3f location?", "answer": "yes"}
{"id": -3833354442525456454, "group": "reachable_atom_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-3f and is holding key0-1. All the positions are open except the following: f4-2f has shape0 shaped lock, f2-0f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-0 is at position f3-3f.", "question": "Is it possible to transition to a state where the following holds: Key key0-1 is at f1-3f location and Robot is holding key0-1?", "answer": "no"}
{"id": -1048307014425651264, "group": "reachable_atom_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 9 tiles and 2 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, and tile_6 is to the right of tile_5. Further, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_1 is down from tile_4, tile_5 is down from tile_8, tile_4 is down from tile_7, and tile_2 is down from tile_5 Currently, robot robot1 is at tile_2 and holding color black and robot robot2 is at tile_1 and holding color white; tile_3 and tile_5 are clear; tile_4 is painted white, tile_6 is painted white, tile_9 is painted black, tile_8 is painted white, and tile_7 is painted black.", "question": "Is it possible to transition to a state where the following holds: tile_2 is clear and Robot robot1 is at tile_5 location?", "answer": "yes"}
{"id": -7722126026049618985, "group": "reachable_atom_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_17 is to the right of tile_16, tile_7 is to the right of tile_6, tile_19 is to the right of tile_18, tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_20 is to the right of tile_19, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_15 is to the right of tile_14, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_14 is to the right of tile_13, and tile_4 is to the right of tile_3. Further, tile_2 is down from tile_7, tile_9 is down from tile_14, tile_4 is down from tile_9, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_8 is down from tile_13, tile_11 is down from tile_16, tile_10 is down from tile_15, tile_6 is down from tile_11, tile_5 is down from tile_10, tile_1 is down from tile_6, tile_13 is down from tile_18, tile_3 is down from tile_8, tile_12 is down from tile_17, and tile_14 is down from tile_19 Currently, robot robot2 is at tile_6 and holding color black, robot robot3 is at tile_14 and holding color white, and robot robot1 is at tile_13 and holding color white; tile_2, tile_20, tile_11, tile_12, tile_15, tile_3, tile_7, tile_1, tile_18, tile_4, tile_8, tile_9, tile_19, tile_5, tile_17, and tile_10 are clear; tile_16 is painted white.", "question": "Is it possible to transition to a state where the following holds: Robot robot2 is at tile_11 location and Robot robot2 is at tile_19 location?", "answer": "no"}
{"id": -3491510099634833392, "group": "reachable_atom_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_17 is to the right of tile_16, tile_7 is to the right of tile_6, tile_19 is to the right of tile_18, tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_20 is to the right of tile_19, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_15 is to the right of tile_14, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_14 is to the right of tile_13, and tile_4 is to the right of tile_3. Further, tile_2 is down from tile_7, tile_9 is down from tile_14, tile_4 is down from tile_9, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_8 is down from tile_13, tile_11 is down from tile_16, tile_10 is down from tile_15, tile_6 is down from tile_11, tile_5 is down from tile_10, tile_1 is down from tile_6, tile_13 is down from tile_18, tile_3 is down from tile_8, tile_12 is down from tile_17, and tile_14 is down from tile_19 Currently, robot robot1 is at tile_4 and holding color white and robot robot2 is at tile_14 and holding color black; tile_2, tile_11, tile_12, tile_15, tile_6, tile_3, tile_16, tile_7, tile_1, tile_18, tile_13, tile_8, tile_9, tile_19, tile_5, tile_17, and tile_10 are clear; tile_20 is painted white.", "question": "Is it possible to transition to a state where the following holds: Robot robot2 is at tile_12 location and tile_12 is clear?", "answer": "no"}
{"id": 1229853569147381390, "group": "reachable_atom_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_17 is to the right of tile_16, tile_7 is to the right of tile_6, tile_19 is to the right of tile_18, tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_20 is to the right of tile_19, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_15 is to the right of tile_14, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_14 is to the right of tile_13, and tile_4 is to the right of tile_3. Further, tile_2 is down from tile_7, tile_9 is down from tile_14, tile_4 is down from tile_9, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_8 is down from tile_13, tile_11 is down from tile_16, tile_10 is down from tile_15, tile_6 is down from tile_11, tile_5 is down from tile_10, tile_1 is down from tile_6, tile_13 is down from tile_18, tile_3 is down from tile_8, tile_12 is down from tile_17, and tile_14 is down from tile_19 Currently, robot robot1 is at tile_3 and holding color white and robot robot2 is at tile_1 and holding color white; tile_2, tile_4, tile_8, and tile_5 are clear; tile_19 is painted black, tile_15 is painted black, tile_17 is painted black, tile_14 is painted white, tile_20 is painted white, tile_13 is painted black, tile_7 is painted black, tile_10 is painted white, tile_9 is painted black, tile_11 is painted black, tile_18 is painted white, tile_6 is painted white, tile_12 is painted white, and tile_16 is painted white.", "question": "Is it possible to transition to a state where the following holds: Robot robot2 is holding black paint and Tile tile_8 is painted in white color?", "answer": "yes"}
{"id": -1777274271145667436, "group": "reachable_atom_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_17 is to the right of tile_16, tile_7 is to the right of tile_6, tile_19 is to the right of tile_18, tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_20 is to the right of tile_19, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_15 is to the right of tile_14, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_14 is to the right of tile_13, and tile_4 is to the right of tile_3. Further, tile_2 is down from tile_7, tile_9 is down from tile_14, tile_4 is down from tile_9, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_8 is down from tile_13, tile_11 is down from tile_16, tile_10 is down from tile_15, tile_6 is down from tile_11, tile_5 is down from tile_10, tile_1 is down from tile_6, tile_13 is down from tile_18, tile_3 is down from tile_8, tile_12 is down from tile_17, and tile_14 is down from tile_19 Currently, robot robot1 is at tile_7 and holding color white and robot robot2 is at tile_5 and holding color white; tile_2, tile_14, tile_11, tile_12, tile_6, tile_3, tile_1, tile_4, tile_13, tile_8, tile_9, and tile_17 are clear; tile_19 is painted black, tile_15 is painted black, tile_20 is painted white, tile_10 is painted white, tile_18 is painted white, and tile_16 is painted white.", "question": "Is it possible to transition to a state where the following holds: tile_5 is clear and Robot robot2 is at tile_4 location?", "answer": "yes"}
{"id": 7976860471845273235, "group": "reachable_atom_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 24 tiles and 2 robots. The tiles locations are: tile_2 is to the right of tile_1, tile_14 is to the right of tile_13, tile_15 is to the right of tile_14, tile_16 is to the right of tile_15, tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_10 is to the right of tile_9, tile_21 is to the right of tile_20, tile_11 is to the right of tile_10, tile_3 is to the right of tile_2, tile_8 is to the right of tile_7, tile_23 is to the right of tile_22, tile_12 is to the right of tile_11, tile_17 is to the right of tile_16, tile_20 is to the right of tile_19, tile_18 is to the right of tile_17, tile_9 is to the right of tile_8, tile_22 is to the right of tile_21, tile_24 is to the right of tile_23, and tile_4 is to the right of tile_3. Further, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_13 is down from tile_19, tile_8 is down from tile_14, tile_11 is down from tile_17, tile_5 is down from tile_11, tile_17 is down from tile_23, tile_7 is down from tile_13, tile_3 is down from tile_9, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_9 is down from tile_15, tile_14 is down from tile_20, tile_6 is down from tile_12, tile_12 is down from tile_18, tile_4 is down from tile_10, tile_16 is down from tile_22, and tile_2 is down from tile_8 Currently, robot robot1 is at tile_4 and holding color black and robot robot2 is at tile_1 and holding color white; tile_2, tile_15, tile_6, tile_3, tile_9, and tile_5 are clear; tile_10 is painted black, tile_8 is painted black, tile_19 is painted white, tile_17 is painted black, tile_21 is painted white, tile_14 is painted white, tile_13 is painted black, tile_12 is painted black, tile_22 is painted black, tile_11 is painted white, tile_24 is painted black, tile_7 is painted white, tile_23 is painted white, tile_20 is painted black, tile_18 is painted white, and tile_16 is painted white.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is holding white paint and Robot robot1 is at tile_5 location?", "answer": "yes"}
{"id": 8144855404350642824, "group": "reachable_atom_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_17 is to the right of tile_16, tile_7 is to the right of tile_6, tile_19 is to the right of tile_18, tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_20 is to the right of tile_19, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_15 is to the right of tile_14, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_14 is to the right of tile_13, and tile_4 is to the right of tile_3. Further, tile_2 is down from tile_7, tile_9 is down from tile_14, tile_4 is down from tile_9, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_8 is down from tile_13, tile_11 is down from tile_16, tile_10 is down from tile_15, tile_6 is down from tile_11, tile_5 is down from tile_10, tile_1 is down from tile_6, tile_13 is down from tile_18, tile_3 is down from tile_8, tile_12 is down from tile_17, and tile_14 is down from tile_19 Currently, robot robot1 is at tile_4 and holding color white and robot robot2 is at tile_1 and holding color black; tile_2, tile_14, tile_11, tile_12, tile_6, tile_3, tile_7, tile_9, tile_19, and tile_5 are clear; tile_8 is painted white, tile_15 is painted black, tile_17 is painted black, tile_20 is painted white, tile_13 is painted black, tile_10 is painted white, tile_18 is painted white, and tile_16 is painted white.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is at tile_12 location and tile_12 is clear?", "answer": "no"}
{"id": 6820260920843194592, "group": "reachable_atom_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 2 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_17 is to the right of tile_16, tile_7 is to the right of tile_6, tile_19 is to the right of tile_18, tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_20 is to the right of tile_19, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_15 is to the right of tile_14, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_14 is to the right of tile_13, and tile_4 is to the right of tile_3. Further, tile_2 is down from tile_7, tile_9 is down from tile_14, tile_4 is down from tile_9, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_8 is down from tile_13, tile_11 is down from tile_16, tile_10 is down from tile_15, tile_6 is down from tile_11, tile_5 is down from tile_10, tile_1 is down from tile_6, tile_13 is down from tile_18, tile_3 is down from tile_8, tile_12 is down from tile_17, and tile_14 is down from tile_19 Currently, robot robot1 is at tile_5 and holding color white and robot robot2 is at tile_12 and holding color black; tile_2, tile_14, tile_11, tile_15, tile_6, tile_3, tile_7, tile_1, tile_4, tile_13, tile_8, tile_9, tile_19, and tile_10 are clear; tile_17 is painted black, tile_20 is painted white, tile_18 is painted white, and tile_16 is painted white.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is at tile_4 location and Tile tile_10 is painted in white color?", "answer": "yes"}
{"id": -8291467984310084867, "group": "reachable_atom_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 20 tiles and 3 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_17 is to the right of tile_16, tile_7 is to the right of tile_6, tile_19 is to the right of tile_18, tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_20 is to the right of tile_19, tile_2 is to the right of tile_1, tile_13 is to the right of tile_12, tile_15 is to the right of tile_14, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_18 is to the right of tile_17, tile_14 is to the right of tile_13, and tile_4 is to the right of tile_3. Further, tile_2 is down from tile_7, tile_9 is down from tile_14, tile_4 is down from tile_9, tile_15 is down from tile_20, tile_7 is down from tile_12, tile_8 is down from tile_13, tile_11 is down from tile_16, tile_10 is down from tile_15, tile_6 is down from tile_11, tile_5 is down from tile_10, tile_1 is down from tile_6, tile_13 is down from tile_18, tile_3 is down from tile_8, tile_12 is down from tile_17, and tile_14 is down from tile_19 Currently, robot robot1 is at tile_2 and holding color white, robot robot2 is at tile_3 and holding color white, and robot robot3 is at tile_9 and holding color white; tile_11, tile_6, tile_7, tile_1, tile_18, tile_4, tile_13, tile_8, and tile_5 are clear; tile_19 is painted black, tile_15 is painted black, tile_17 is painted black, tile_14 is painted white, tile_20 is painted white, tile_10 is painted white, tile_12 is painted white, and tile_16 is painted white.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is holding white paint and Robot robot1 is holding black paint?", "answer": "no"}
{"id": 227479060233047288, "group": "reachable_atom_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_5 is to the right of tile_4, tile_12 is to the right of tile_11, tile_2 is to the right of tile_1, tile_11 is to the right of tile_10, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_8 is to the right of tile_7, and tile_6 is to the right of tile_5. Further, tile_6 is down from tile_9, tile_3 is down from tile_6, tile_1 is down from tile_4, tile_5 is down from tile_8, tile_7 is down from tile_10, tile_9 is down from tile_12, tile_4 is down from tile_7, tile_2 is down from tile_5, and tile_8 is down from tile_11 Currently, robot robot1 is at tile_1 and holding color white and robot robot2 is at tile_2 and holding color white; tile_3 and tile_4 are clear; tile_11 is painted black, tile_6 is painted white, tile_9 is painted black, tile_8 is painted white, tile_12 is painted white, tile_5 is painted black, tile_7 is painted black, and tile_10 is painted white.", "question": "Is it possible to transition to a state where the following holds: Robot robot2 is holding white paint and Robot robot2 is holding black paint?", "answer": "no"}
{"id": -5300480923743753954, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, left gripper is free, and right gripper is carrying the ball ball3. Additionally, ball1 is at room2, ball2 is at room3, ball4 is at room5.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is carrying the ball ball4 in the left gripper and The left gripper of robot robot1 is free?", "answer": "no"}
{"id": -8351840878139143160, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball3 and ball4 are at room2, ball2 is at room1.", "question": "Is it possible to transition to a state where the following holds: Ball ball2 is at room2 location and Ball ball2 is in room room1?", "answer": "no"}
{"id": 2063787763986646885, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 10 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball4 and ball2 are at room2, ball1 is at room4, ball3 is at room10.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is at room2 location?", "answer": "yes"}
{"id": -3822123605812103040, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball7. Additionally, ball4, ball1, ball2, and ball3 are at room1, ball5 is at room3, ball6 is at room2.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is at room3 location and The right gripper of robot robot1 is free?", "answer": "yes"}
{"id": -7417836131478415900, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball3 and ball4 are at room2, ball1 and ball2 are at room1.", "question": "Is it possible to transition to a state where the following holds: Ball room2 is at room1 location and The left gripper of robot robot1 is free?", "answer": "no"}
{"id": 5297141015067430868, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball9, and right gripper is carrying the ball ball14. Additionally, ball3, ball13, ball12, and ball1 are at room2, ball4, ball5, ball2, ball15, ball6, and ball8 are at room1, ball7, ball10, and ball11 are at room3.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is carrying the ball ball1 in the right gripper and Robot robot1 is carrying the ball ball8 in the right gripper?", "answer": "no"}
{"id": 898910693672625741, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball3 is at room2, ball4 and ball2 are at room1.", "question": "Is it possible to transition to a state where the following holds: Ball left1 is in room room1?", "answer": "no"}
{"id": 9000560023870591173, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball2. Additionally, ball4 and ball1 are at room1.", "question": "Is it possible to transition to a state where the following holds: The right gripper of robot robot1 is free and Ball ball2 is in room room2?", "answer": "yes"}
{"id": -3752540588024094941, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room4, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball4. Additionally, ball1 is at room2, ball3 is at room5.", "question": "Is it possible to transition to a state where the following holds: Ball left1 is at room5 location?", "answer": "no"}
{"id": -1140742101112367148, "group": "reachable_atom_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball3, ball2, and ball1 are at room2.", "question": "Is it possible to transition to a state where the following holds: Robot robot1 is carrying the ball ball1 in the right gripper and Robot robot1 is carrying the ball ball3 in the right gripper?", "answer": "no"}
{"id": 1132511168930332617, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has its camera camera2 calibrated. Store(s) store1 and store0 are full. ", "question": "Is it possible to transition to a state where the following holds: Rover rover0 has its camera camera1 calibrated and Store(s) store0 is empty?", "answer": "yes"}
{"id": -1043496969056018846, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rover rover1 has image objective1 in mode low_res. Store(s) store0 and store1 are empty. ", "question": "Is it possible to transition to a state where the following holds: Rover rover0 is at waypoint0 and Store(s) store1 is full?", "answer": "yes"}
{"id": -1902777571294228735, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Soil data was communicated from waypoint waypoint1; Image objective0 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Rover rover0 has image objective0 in mode low_res. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Is it possible to transition to a state where the following holds: Soil can be sampled at the following location(s): waypoint0?", "answer": "no"}
{"id": 6532627485091435659, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Image objective1 was communicated in mode low_res. Image objective0 was communicated in mode low_res. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Is it possible to transition to a state where the following holds: Rocks can be sampled at the following location(s): waypoint0?", "answer": "no"}
{"id": -2130439532138217037, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Image objective1 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode colour. Rover rover0 has its camera camera1 calibrated. Store(s) store1 and store0 are full. ", "question": "Is it possible to transition to a state where the following holds: Soil data was communicated from waypoint waypoint1;?", "answer": "no"}
{"id": -5920828701192469606, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera0 and camera1 on board. Rover rover1 has camera2 on board. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Image objective1 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode colour. Store(s) store1 and store0 are full. ", "question": "Is it possible to transition to a state where the following holds: Rover rover1 has rock analyzed in waypoint waypoint0 and Rocks can be sampled at the following location(s): waypoint2?", "answer": "no"}
{"id": -1016262709697167809, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera1 supports colour and low_res. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint4 to waypoint1, waypoint0 to waypoint1, waypoint1 to waypoint4. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2, waypoint0, and waypoint4. Waypoint(s) are visible from waypoint0: waypoint2, waypoint3, waypoint4, and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint1 and waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rover rover1 has image objective0 in mode colour. Rover rover0 has image objective1 in mode low_res. Rover rover1 has its camera camera1 calibrated. Store(s) store0 and store1 are empty. ", "question": "Is it possible to transition to a state where the following holds: Rover rover1 has image objective1 in mode high_res?", "answer": "no"}
{"id": -2233591958937444481, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera2 supports colour and low_res. Camera camera0 supports low_res and colour and high_res. Camera camera1 supports colour. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint6 to waypoint0, waypoint2 to waypoint0, waypoint3 to waypoint0, waypoint5 to waypoint0, waypoint6 to waypoint1, waypoint0 to waypoint3, waypoint2 to waypoint4, waypoint1 to waypoint6, waypoint0 to waypoint5, waypoint4 to waypoint2, waypoint0 to waypoint6. Rover rover0 can traverse from waypoint5 to waypoint0, waypoint5 to waypoint2, waypoint3 to waypoint0, waypoint4 to waypoint5, waypoint5 to waypoint1, waypoint0 to waypoint3, waypoint0 to waypoint5, waypoint6 to waypoint5, waypoint1 to waypoint5, waypoint5 to waypoint4, waypoint2 to waypoint5, waypoint5 to waypoint6. Waypoint(s) are visible from waypoint3: waypoint0, waypoint6, waypoint4, waypoint1, waypoint5, and waypoint2. Waypoint(s) are visible from waypoint1: waypoint5, waypoint2, waypoint3, waypoint6, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint6, waypoint1, waypoint5, waypoint2, and waypoint3. Waypoint(s) are visible from waypoint5: waypoint6, waypoint0, waypoint1, waypoint4, waypoint2, and waypoint3. Waypoint(s) are visible from waypoint6: waypoint0, waypoint1, waypoint4, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint0: waypoint6, waypoint2, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint2: waypoint0, waypoint4, waypoint1, waypoint5, and waypoint3. Objective objective1 is visible from waypoint2, waypoint0, waypoint4, and waypoint5. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint5. Rocks can be sampled at the following location(s): waypoint4, waypoint6, and waypoint2. Soil can be sampled at the following location(s): waypoint3, waypoint4, waypoint6, and waypoint1. Rovers rover0 and rover1 are available. Image objective1 was communicated in mode colour. Image objective0 was communicated in mode high_res. Rover rover1 has rock analyzed in waypoint waypoint3. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has image objective0 in mode high_res. Rover rover1 has image objective1 in mode colour. Rover rover1 has its camera camera0 calibrated. Store(s) store1 and store0 are full. ", "question": "Is it possible to transition to a state where the following holds: Store(s) store0 is empty and Rover rover0 is at waypoint1?", "answer": "yes"}
{"id": -7828064559952906372, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera1 supports colour and low_res. Camera camera0 supports colour and low_res. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint4 to waypoint1, waypoint0 to waypoint1, waypoint1 to waypoint4. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint0 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2, waypoint0, and waypoint4. Waypoint(s) are visible from waypoint0: waypoint2, waypoint3, waypoint4, and waypoint1. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint2 and waypoint1. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Rover rover1 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Is it possible to transition to a state where the following holds: Rover rover1 has rock analyzed in waypoint waypoint1 and Rocks can be sampled at the following location(s): waypoint1?", "answer": "no"}
{"id": 7840568113505898601, "group": "reachable_atom_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera2 supports colour and low_res. Camera camera0 supports low_res and colour and high_res. Camera camera1 supports colour. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint6 to waypoint0, waypoint2 to waypoint0, waypoint3 to waypoint0, waypoint5 to waypoint0, waypoint6 to waypoint1, waypoint0 to waypoint3, waypoint2 to waypoint4, waypoint1 to waypoint6, waypoint0 to waypoint5, waypoint4 to waypoint2, waypoint0 to waypoint6. Rover rover0 can traverse from waypoint5 to waypoint0, waypoint5 to waypoint2, waypoint3 to waypoint0, waypoint4 to waypoint5, waypoint5 to waypoint1, waypoint0 to waypoint3, waypoint0 to waypoint5, waypoint6 to waypoint5, waypoint1 to waypoint5, waypoint5 to waypoint4, waypoint2 to waypoint5, waypoint5 to waypoint6. Waypoint(s) are visible from waypoint3: waypoint0, waypoint6, waypoint4, waypoint1, waypoint5, and waypoint2. Waypoint(s) are visible from waypoint1: waypoint5, waypoint2, waypoint3, waypoint6, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint6, waypoint1, waypoint5, waypoint2, and waypoint3. Waypoint(s) are visible from waypoint5: waypoint6, waypoint0, waypoint1, waypoint4, waypoint2, and waypoint3. Waypoint(s) are visible from waypoint6: waypoint0, waypoint1, waypoint4, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint0: waypoint6, waypoint2, waypoint3, and waypoint5. Waypoint(s) are visible from waypoint2: waypoint0, waypoint4, waypoint1, waypoint5, and waypoint3. Objective objective1 is visible from waypoint2, waypoint0, waypoint4, and waypoint5. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover0 is at waypoint4. Rover rover1 is at waypoint5. Rocks can be sampled at the following location(s): waypoint4, waypoint6, waypoint2, and waypoint3. Soil can be sampled at the following location(s): waypoint3, waypoint4, waypoint6, and waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint5; Image objective1 was communicated in mode high_res. Image objective0 was communicated in mode high_res. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has image objective0 in mode high_res. Rover rover1 has image objective1 in mode high_res. Rover rover0 has its camera camera2 calibrated. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Is it possible to transition to a state where the following holds: Rover rover0 has rock analyzed in waypoint waypoint3 and Rover rover1 has rock analyzed in waypoint waypoint3?", "answer": "no"}
{"id": 5848559229288385036, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y0 and loc-x3-y1. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x3-y0, loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y3, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x1-y2, loc-x2-y3, and loc-x3-y2.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x2-y0?", "answer": "yes"}
{"id": -3707823257616714862, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y0, loc-x0-y2, loc-x3-y3, and loc-x3-y1. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x2-y2, loc-x1-y1, loc-x2-y1, and loc-x1-y2.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x1-y1 and the robot is in place loc-x0-y1?", "answer": "no"}
{"id": 8667516452484671595, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y0 and loc-x3-y1. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x3-y0, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x0-y3, and loc-x0-y0.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x0-y3 and the robot is in place loc-x2-y3?", "answer": "no"}
{"id": -5824563948979246597, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y0 and loc-x3-y1. Currently, the robot is in place loc-x0-y2.The following places have been visited: loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x3-y0, loc-x3-y3, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x0-y3, loc-x1-y2, loc-x2-y3, and loc-x3-y2.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x3-y3 and the robot is in place loc-x1-y3?", "answer": "no"}
{"id": 9209347401036085916, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x1-y0, loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y3, loc-x1-y1, loc-x0-y3, loc-x1-y2, loc-x0-y0, loc-x2-y3, and loc-x3-y2.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x1-y3?", "answer": "yes"}
{"id": -5210007184100988901, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x1-y0, loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y0, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x0-y3, loc-x1-y2, loc-x0-y0, and loc-x2-y3.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x1-y0 and the robot is in place loc-x0-y1?", "answer": "no"}
{"id": 7010375140385281994, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x2-y3, loc-x1-y2, and loc-x0-y4. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x2-y2, loc-x0-y2, loc-x0-y1, loc-x1-y1, and loc-x2-y1.", "question": "Is it possible to transition to a state where the following holds: Place loc-x3-y2 has been visited?", "answer": "yes"}
{"id": -511082433697583788, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x3-y3.The following places have been visited: loc-x1-y0, loc-x3-y0, loc-x0-y2, loc-x3-y3, loc-x0-y1, loc-x3-y2, loc-x2-y0, loc-x0-y0, and loc-x3-y1.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x0-y3 and the robot is in place loc-x1-y2?", "answer": "no"}
{"id": 5705969304590475747, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x1-y0, loc-x2-y2, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y2, loc-x2-y1, loc-x0-y3, loc-x0-y0, loc-x2-y3, and loc-x3-y1.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x0-y0 and the robot is in place loc-x3-y2?", "answer": "no"}
{"id": 5625704496036238137, "group": "reachable_atom_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x0-y2.The following places have been visited: loc-x1-y0, loc-x0-y2, loc-x1-y3, loc-x0-y1, loc-x3-y0, loc-x3-y3, loc-x1-y1, loc-x2-y1, loc-x2-y0, loc-x0-y3, loc-x3-y2, loc-x0-y0, loc-x2-y3, and loc-x3-y1.", "question": "Is it possible to transition to a state where the following holds: the robot is in place loc-x2-y3 and the robot is in place loc-x0-y0?", "answer": "no"}
{"id": -2876959616016670438, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 pallets, 2 distributors, 2 trucks, 12 hoists, 10 depots, 2 crates, numbered consecutively. Currently, crate1, pallet10, pallet5, pallet1, pallet4, pallet11, pallet8, pallet0, pallet6, pallet7, pallet9, and pallet3 are clear; hoist3, hoist1, hoist7, hoist10, hoist2, hoist0, hoist5, hoist6, hoist9, hoist11, and hoist4 are available; pallet10 is at distributor0, truck1 is at depot8, hoist9 is at depot9, pallet9 is at depot9, hoist6 is at depot6, hoist10 is at distributor0, hoist8 is at depot8, pallet4 is at depot4, hoist5 is at depot5, pallet3 is at depot3, hoist7 is at depot7, hoist0 is at depot0, hoist1 is at depot1, truck0 is at depot0, pallet5 is at depot5, pallet8 is at depot8, pallet1 is at depot1, pallet6 is at depot6, hoist4 is at depot4, pallet7 is at depot7, pallet0 is at depot0, pallet2 is at depot2, pallet11 is at distributor1, hoist2 is at depot2, crate1 is at depot2, hoist11 is at distributor1, and hoist3 is at depot3; crate1 is on pallet2; hoist8 is lifting crate0.", "question": "Is it possible to transition to a state where the following holds: hoist10 is at distributor0 and hoist5 is at distributor0?", "answer": "no"}
{"id": 2209055194466017943, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 4 pallets, 2 distributors, 2 trucks, 4 hoists, 2 depots, 2 crates, numbered consecutively. Currently, crate1, pallet1, pallet2, and pallet3 are clear; hoist3, hoist2, and hoist1 are available; pallet2 is at distributor0, truck1 is at distributor0, hoist0 is at depot0, hoist1 is at depot1, truck0 is at depot0, pallet1 is at depot1, hoist3 is at distributor1, pallet0 is at depot0, crate1 is at depot0, pallet3 is at distributor1, and hoist2 is at distributor0; crate1 is on pallet0; hoist0 is lifting crate0.", "question": "Is it possible to transition to a state where the following holds: truck1 is at depot0 and truck0 is at depot1?", "answer": "yes"}
{"id": -4265684394652917076, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 pallets, 2 distributors, 2 trucks, 6 hoists, 4 depots, 3 crates, numbered consecutively. Currently, pallet5, pallet2, pallet4, pallet0, crate0, and pallet3 are clear; hoist3, hoist2, hoist0, hoist5, and hoist1 are available; hoist4 is at distributor0, pallet4 is at distributor0, crate0 is at depot1, hoist5 is at distributor1, pallet3 is at depot3, hoist0 is at depot0, hoist1 is at depot1, truck1 is at depot2, pallet1 is at depot1, truck0 is at depot2, pallet5 is at distributor1, pallet0 is at depot0, pallet2 is at depot2, hoist3 is at depot3, and hoist2 is at depot2; crate0 is on pallet1; crate1 is in truck0; hoist4 is lifting crate2.", "question": "Is it possible to transition to a state where the following holds: hoist3 is at depot0 and hoist3 is at depot3?", "answer": "no"}
{"id": -3417521372616212671, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 6 pallets, 2 distributors, 2 trucks, 6 hoists, 4 depots, 3 crates, numbered consecutively. Currently, crate2, pallet5, pallet1, pallet2, pallet0, and pallet3 are clear; hoist3, hoist2, hoist0, hoist5, and hoist1 are available; hoist4 is at distributor0, pallet4 is at distributor0, hoist5 is at distributor1, crate2 is at distributor0, pallet3 is at depot3, hoist0 is at depot0, hoist1 is at depot1, truck1 is at depot1, pallet1 is at depot1, pallet5 is at distributor1, pallet0 is at depot0, pallet2 is at depot2, hoist3 is at depot3, hoist2 is at depot2, and truck0 is at distributor0; crate2 is on pallet4; crate0 is in truck1; hoist4 is lifting crate1.", "question": "Is it possible to transition to a state where the following holds: pallet1 is at distributor0?", "answer": "no"}
{"id": 2783800139767511239, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 9 pallets, 2 distributors, 2 trucks, 9 hoists, 7 depots, 2 crates, numbered consecutively. Currently, pallet5, pallet1, pallet2, pallet4, pallet8, pallet0, pallet6, pallet7, and pallet3 are clear; hoist1, hoist8, hoist7, hoist2, hoist0, hoist5, hoist6, and hoist4 are available; hoist8 is at distributor1, pallet7 is at distributor0, pallet8 is at distributor1, hoist6 is at depot6, pallet4 is at depot4, hoist5 is at depot5, pallet3 is at depot3, hoist0 is at depot0, hoist1 is at depot1, truck1 is at depot1, pallet5 is at depot5, pallet1 is at depot1, pallet6 is at depot6, hoist4 is at depot4, pallet0 is at depot0, pallet2 is at depot2, truck0 is at depot1, hoist2 is at depot2, hoist7 is at distributor0, and hoist3 is at depot3; crate0 is in truck1; hoist3 is lifting crate1.", "question": "Is it possible to transition to a state where the following holds: pallet0 is at depot0 and hoist0 is at depot0?", "answer": "no"}
{"id": -2467461375520176010, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 pallets, 2 distributors, 2 trucks, 12 hoists, 10 depots, 2 crates, numbered consecutively. Currently, pallet10, pallet5, pallet1, pallet2, pallet4, pallet11, pallet0, pallet6, pallet7, pallet9, crate0, and pallet3 are clear; hoist3, hoist1, hoist8, hoist7, hoist10, hoist0, hoist5, hoist6, hoist9, hoist11, and hoist4 are available; pallet10 is at distributor0, hoist9 is at depot9, pallet9 is at depot9, hoist6 is at depot6, hoist10 is at distributor0, hoist8 is at depot8, pallet4 is at depot4, hoist5 is at depot5, pallet3 is at depot3, hoist7 is at depot7, hoist0 is at depot0, hoist1 is at depot1, truck1 is at depot2, truck0 is at depot0, pallet5 is at depot5, pallet8 is at depot8, pallet1 is at depot1, pallet6 is at depot6, hoist4 is at depot4, pallet7 is at depot7, pallet0 is at depot0, pallet2 is at depot2, pallet11 is at distributor1, hoist2 is at depot2, hoist11 is at distributor1, crate0 is at depot8, and hoist3 is at depot3; crate0 is on pallet8; hoist2 is lifting crate1.", "question": "Is it possible to transition to a state where the following holds: pallet9 is at depot9 and truck1 is at depot9?", "answer": "no"}
{"id": -5628398575096707880, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 4 pallets, 2 distributors, 2 trucks, 4 hoists, 2 depots, 2 crates, numbered consecutively. Currently, pallet1, pallet2, pallet0, and pallet3 are clear; hoist3, hoist2, hoist0, and hoist1 are available; pallet2 is at distributor0, truck1 is at depot0, hoist0 is at depot0, hoist1 is at depot1, pallet1 is at depot1, hoist3 is at distributor1, pallet0 is at depot0, truck0 is at distributor1, pallet3 is at distributor1, and hoist2 is at distributor0; crate0 is in truck1 and crate1 is in truck1.", "question": "Is it possible to transition to a state where the following holds: hoist3 is at depot0?", "answer": "no"}
{"id": 5187376524514580818, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 pallets, 2 distributors, 2 trucks, 12 hoists, 10 depots, 2 crates, numbered consecutively. Currently, pallet10, pallet5, pallet1, pallet2, pallet4, pallet11, pallet8, pallet0, pallet6, pallet7, pallet9, and pallet3 are clear; hoist3, hoist1, hoist8, hoist7, hoist10, hoist2, hoist0, hoist5, hoist6, hoist9, hoist11, and hoist4 are available; pallet10 is at distributor0, hoist9 is at depot9, truck1 is at distributor1, pallet9 is at depot9, hoist6 is at depot6, hoist10 is at distributor0, pallet4 is at depot4, hoist8 is at depot8, hoist5 is at depot5, pallet3 is at depot3, hoist7 is at depot7, hoist0 is at depot0, hoist1 is at depot1, truck0 is at depot0, pallet5 is at depot5, pallet8 is at depot8, pallet1 is at depot1, pallet6 is at depot6, hoist4 is at depot4, pallet7 is at depot7, pallet0 is at depot0, pallet2 is at depot2, pallet11 is at distributor1, hoist2 is at depot2, hoist11 is at distributor1, and hoist3 is at depot3; crate0 is in truck1 and crate1 is in truck1.", "question": "Is it possible to transition to a state where the following holds: hoist1 is at depot9?", "answer": "no"}
{"id": -6986907514704113462, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 12 pallets, 2 distributors, 2 trucks, 12 hoists, 10 depots, 2 crates, numbered consecutively. Currently, pallet10, pallet5, pallet1, pallet2, pallet4, pallet11, pallet8, pallet0, pallet6, pallet7, pallet9, and pallet3 are clear; hoist3, hoist1, hoist8, hoist7, hoist10, hoist0, hoist5, hoist6, hoist9, hoist11, and hoist4 are available; pallet10 is at distributor0, hoist9 is at depot9, pallet9 is at depot9, hoist6 is at depot6, hoist10 is at distributor0, pallet4 is at depot4, hoist8 is at depot8, hoist5 is at depot5, pallet3 is at depot3, hoist7 is at depot7, hoist0 is at depot0, hoist1 is at depot1, pallet5 is at depot5, pallet8 is at depot8, pallet1 is at depot1, pallet6 is at depot6, hoist4 is at depot4, truck1 is at depot4, pallet7 is at depot7, pallet0 is at depot0, pallet2 is at depot2, pallet11 is at distributor1, hoist2 is at depot2, hoist11 is at distributor1, truck0 is at distributor1, and hoist3 is at depot3; crate0 is in truck0; hoist2 is lifting crate1.", "question": "Is it possible to transition to a state where the following holds: truck0 is at depot2 and truck1 is at depot3?", "answer": "yes"}
{"id": -1901112298191899187, "group": "reachable_atom_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 5 pallets, 2 distributors, 2 trucks, 5 hoists, 3 depots, 2 crates, numbered consecutively. Currently, pallet2, pallet4, pallet0, crate0, and pallet3 are clear; hoist3, hoist2, hoist0, hoist4, and hoist1 are available; crate0 is at depot1, truck1 is at depot0, hoist0 is at depot0, hoist1 is at depot1, hoist4 is at distributor1, pallet1 is at depot1, pallet0 is at depot0, pallet2 is at depot2, truck0 is at depot1, pallet3 is at distributor0, hoist2 is at depot2, hoist3 is at distributor0, and pallet4 is at distributor1; crate0 is on pallet1; crate1 is in truck0.", "question": "Is it possible to transition to a state where the following holds: hoist1 is lifting crate1 and truck1 is at depot1?", "answer": "yes"}
{"id": 5011635154821118594, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and its arm is empty. The following locations have hard rock: f1-3f, f3-1f, f1-1f, f3-3f, and f1-2f. The following locations have soft rock: f3-2f, f2-2f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-2f location.", "question": "Is it possible to transition to a state where the following holds: The robot is at position f0-0f?", "answer": "yes"}
{"id": 2159023389531114356, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-1f and is holding a laser. The following locations have hard rock: f1-4f and f1-3f. The following locations have soft rock: f2-4f, f1-2f, f0-3f, f0-4f, f2-1f, f2-2f, f2-3f, and f0-2f. The gold is at f0-4f location.", "question": "Is it possible to transition to a state where the following holds: The robot is at position f0-4f and The robot is at position f1-4f?", "answer": "no"}
{"id": -4304504674581420587, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-2f and its arm is empty. The following locations have soft rock: f1-2f, f1-3f, f2-1f, f2-2f, and f2-3f. The gold is at f0-3f location. The laser is at f0-1f location.", "question": "Is it possible to transition to a state where the following holds: The robot is holding a laser and The robot is at position f0-1f?", "answer": "yes"}
{"id": 5939611851157161387, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-2f and its arm is empty. The following locations have hard rock: f2-2f, f2-1f, and f0-3f. The following locations have soft rock: f2-3f and f1-3f. The gold is at f1-3f location. The laser is at f1-1f location.", "question": "Is it possible to transition to a state where the following holds: The robot is at position f0-1f?", "answer": "yes"}
{"id": 2256183000316335299, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-1f and is holding a laser. The following locations have hard rock: f2-2f and f0-3f. The following locations have soft rock: f1-3f, f2-3f, and f0-2f. The gold is at f1-3f location.", "question": "Is it possible to transition to a state where the following holds: The robot is at position f0-1f?", "answer": "yes"}
{"id": -5876511576232440146, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-1f and its arm is empty. The following locations have hard rock: f1-3f, f3-1f, f3-3f, and f1-2f. The following locations have soft rock: f3-2f, f2-2f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f1-0f location.", "question": "Is it possible to transition to a state where the following holds: The gold is at f0-2f location?", "answer": "no"}
{"id": 488906920822912350, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding gold. The following locations have hard rock: f1-3f, f1-1f, and f1-4f. The following locations have soft rock: f2-2f, f2-4f, f1-2f, f2-1f, and f2-3f. The gold is at f0-4f location. The laser is at f0-0f location.", "question": "Is it possible to transition to a state where the following holds: The robot is at position f0-2f?", "answer": "yes"}
{"id": -4917035875754716528, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-3f, f1-1f, and f1-4f. The following locations have soft rock: f2-2f, f2-4f, f1-2f, f2-1f, f0-4f, and f2-3f. The gold is at f0-4f location. The laser is at f0-1f location.", "question": "Is it possible to transition to a state where the following holds: The robot is holding a bomb and The robot is at position f0-1f?", "answer": "yes"}
{"id": 2255197774336346575, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding a bomb. The following locations have hard rock: f1-3f, f3-1f, f3-3f, and f1-2f. The following locations have soft rock: f3-2f, f2-2f, f0-3f, f2-1f, and f2-3f. The gold is at f0-3f location. The laser is at f0-1f location.", "question": "Is it possible to transition to a state where the following holds: The robot is at position f0-2f?", "answer": "yes"}
{"id": -2451579030057291609, "group": "reachable_atom_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and is holding a laser. The following locations have hard rock: f1-4f, f1-3f, and f1-1f. The following locations have soft rock: f2-4f, f1-2f, f0-4f, f2-1f, f2-2f, and f2-3f. The gold is at f0-4f location.", "question": "Is it possible to transition to a state where the following holds: The laser is at f0-2f location and The robot's arm is empty?", "answer": "yes"}
{"id": 3105364738225347439, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation2, phenomenon5, star3, groundstation4, star6, groundstation0, star1. There are 3 image mode(s): image2, image1, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite6 has following instruments onboard: instrument15, instrument16, instrument17. Satellite satellite5 has following instruments onboard: instrument13, instrument14, instrument12. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Satellite satellite3 is pointing to star6. Satellite satellite6 is pointing to star1. Satellite satellite5 is pointing to phenomenon5. Power is available on the following satellite(s): satellite0, satellite1, satellite6, satellite5, satellite3. Following instruments are powered on: instrument11, instrument6. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite3 is pointing to groundstation4?", "answer": "yes"}
{"id": 7507623453296779685, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): star4, planet5, star2, groundstation1, groundstation0, planet6, groundstation3. There are 3 image mode(s): image1, thermograph0, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite4 has following instruments onboard: instrument7, instrument6. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite6 has following instruments onboard: instrument10, instrument9, instrument11. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite5 has following instruments onboard: instrument8. Instrument instrument12 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument6 supports image of mode thermograph0 and its calibration target is groundstation0. Instrument instrument4 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument2 supports image of mode image1 and its calibration target is star2. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument8 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite1 is pointing to groundstation0. Satellite satellite5 is pointing to groundstation3. Satellite satellite8 is pointing to planet6. Satellite satellite7 is pointing to star2. Satellite satellite2 is pointing to planet6. Satellite satellite4 is pointing to planet6. Satellite satellite6 is pointing to groundstation1. Satellite satellite3 is pointing to groundstation1. Satellite satellite0 is pointing to groundstation1. Satellite satellite9 is pointing to star4. Power is available on the following satellite(s): satellite1, satellite2, satellite9, satellite3, satellite8, satellite5, satellite0, satellite4, satellite7. Following instruments are powered on: instrument10. Following instruments are calibrated: instrument10. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite9 is pointing to star4 and Satellite satellite9 is pointing to planet6?", "answer": "no"}
{"id": -58859649782086694, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): star0, phenomenon6, planet5, star2, groundstation1, groundstation4, groundstation3. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite1 is pointing to groundstation1. Satellite satellite2 is pointing to star2. Satellite satellite0 is pointing to groundstation4. Power is available on the following satellite(s): satellite0, satellite2, satellite1. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite2 is pointing to groundstation1 and Satellite satellite2 is pointing to planet5?", "answer": "no"}
{"id": -109716664035700185, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): star4, planet5, star2, groundstation1, groundstation0, planet6, groundstation3. There are 3 image mode(s): image1, thermograph0, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite3 has following instruments onboard: instrument5, instrument4. Satellite satellite4 has following instruments onboard: instrument7, instrument6. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite6 has following instruments onboard: instrument10, instrument9, instrument11. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite5 has following instruments onboard: instrument8. Instrument instrument12 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument6 supports image of mode thermograph0 and its calibration target is groundstation0. Instrument instrument4 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument2 supports image of mode image1 and its calibration target is star2. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument8 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4.  Currently, Satellite satellite3 is pointing to star4. Satellite satellite5 is pointing to groundstation3. Satellite satellite7 is pointing to star2. Satellite satellite2 is pointing to planet6. Satellite satellite4 is pointing to planet6. Satellite satellite1 is pointing to star2. Satellite satellite6 is pointing to star4. Satellite satellite8 is pointing to groundstation1. Satellite satellite0 is pointing to groundstation1. Satellite satellite9 is pointing to star4. Power is available on the following satellite(s): satellite1, satellite2, satellite9, satellite8, satellite5, satellite0, satellite4, satellite7. Following instruments are powered on: instrument5, instrument10. Following instruments are calibrated: instrument10. A thermograph0 mode image of target planet5 is available. A infrared2 mode image of target planet6 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite7 is pointing to groundstation3 and Satellite satellite7 is pointing to planet6?", "answer": "no"}
{"id": 4376933373471435824, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation2, phenomenon5, star3, groundstation4, star6, groundstation0, star1. There are 3 image mode(s): image2, image1, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite6 has following instruments onboard: instrument15, instrument16, instrument17. Satellite satellite5 has following instruments onboard: instrument13, instrument14, instrument12. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Satellite satellite2 is pointing to groundstation2. Satellite satellite3 is pointing to star6. Satellite satellite5 is pointing to groundstation0. Satellite satellite6 is pointing to groundstation0. Power is available on the following satellite(s): satellite0, satellite1, satellite2, satellite4, satellite5, satellite3. Following instruments are powered on: instrument16. Following instruments are calibrated: instrument16. A image1 mode image of target star6 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite0 is pointing to groundstation0 and Satellite satellite1 is pointing to groundstation0?", "answer": "yes"}
{"id": -8179275404747455390, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There is 1 satellite(s), numbered consecutively. There are 7 possible target object(s): star4, planet5, star2, groundstation1, groundstation0, planet6, groundstation3. There are 3 image mode(s): image1, thermograph0, infrared2. There are 1 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0. Instrument instrument0 supports image of mode infrared2 and its calibration target is star4.  Currently, Satellite satellite0 is pointing to groundstation0. Power is available on the following satellite(s): satellite0. Following instruments are calibrated: instrument0. A thermograph0 mode image of target planet5 is available. A infrared2 mode image of target planet5 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite0 is pointing to star4?", "answer": "yes"}
{"id": 7188686075881517182, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation2, phenomenon5, star3, groundstation4, star6, groundstation0, star1. There are 3 image mode(s): image2, image1, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite6 has following instruments onboard: instrument15, instrument16, instrument17. Satellite satellite5 has following instruments onboard: instrument13, instrument14, instrument12. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to star6. Satellite satellite4 is pointing to star1. Satellite satellite3 is pointing to star6. Satellite satellite6 is pointing to groundstation0. Satellite satellite5 is pointing to phenomenon5. Power is available on the following satellite(s): satellite0, satellite4, satellite6, satellite5, satellite3. Following instruments are powered on: instrument6, instrument4. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. A image1 mode image of target star6 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite0 is pointing to star3 and Satellite satellite0 is pointing to groundstation4?", "answer": "no"}
{"id": 38115520750114517, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation2, phenomenon5, star3, groundstation4, star6, groundstation0, star1. There are 3 image mode(s): image2, image1, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite6 has following instruments onboard: instrument15, instrument16, instrument17. Satellite satellite5 has following instruments onboard: instrument13, instrument14, instrument12. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to star6. Satellite satellite4 is pointing to star1. Satellite satellite3 is pointing to star6. Satellite satellite5 is pointing to groundstation0. Satellite satellite6 is pointing to groundstation0. Power is available on the following satellite(s): satellite0, satellite1, satellite6, satellite4, satellite5, satellite3. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. A image1 mode image of target star6 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite0 is pointing to groundstation0 and Satellite satellite0 is pointing to star1?", "answer": "no"}
{"id": -7853614269930853508, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation2, phenomenon5, star3, groundstation4, star6, groundstation0, star1. There are 3 image mode(s): image2, image1, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite6 has following instruments onboard: instrument15, instrument16, instrument17. Satellite satellite5 has following instruments onboard: instrument13, instrument14, instrument12. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Satellite satellite3 is pointing to star6. Satellite satellite5 is pointing to star1. Satellite satellite6 is pointing to groundstation0. Power is available on the following satellite(s): satellite0, satellite1, satellite4, satellite6, satellite5, satellite3. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. A image0 mode image of target phenomenon5 is available. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite3 is pointing to star1?", "answer": "yes"}
{"id": -7241477041063095125, "group": "reachable_atom_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation2, phenomenon5, star3, groundstation4, star6, groundstation0, star1. There are 3 image mode(s): image2, image1, image0. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument4, instrument3, instrument5. Satellite satellite0 has following instruments onboard: instrument2, instrument0, instrument1. Satellite satellite6 has following instruments onboard: instrument15, instrument16, instrument17. Satellite satellite5 has following instruments onboard: instrument13, instrument14, instrument12. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite4 has following instruments onboard: instrument9, instrument10, instrument11. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument6 supports image of mode image2 and its calibration target is groundstation2. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument2 supports image of mode image0 and its calibration target is star1. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument16 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument11 supports image of mode image2 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument8 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite1 is pointing to phenomenon5. Satellite satellite4 is pointing to star1. Satellite satellite2 is pointing to groundstation2. Satellite satellite5 is pointing to groundstation0. Satellite satellite3 is pointing to star3. Satellite satellite6 is pointing to groundstation0. Power is available on the following satellite(s): satellite0, satellite1, satellite4, satellite6, satellite5, satellite3. Following instruments are powered on: instrument6. ", "question": "Is it possible to transition to a state where the following holds: Satellite satellite3 is pointing to groundstation2 and Satellite satellite5 is pointing to phenomenon5?", "answer": "yes"}
{"id": 4724179342319617331, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: alice, xena, heidi, dave, ted, bob, and kevin. There are 7 items/roles: quince, leek, ulluco, valerian, parsnip, mushroom, and yam. Currently, heidi is assigned yam, kevin is assigned mushroom, bob is assigned parsnip, dave is assigned ulluco, alice is assigned quince, xena is assigned valerian, and ted is assigned leek.", "question": "Is it possible to transition to a state where the following holds: heidi is assigned valerian and bob is assigned valerian?", "answer": "no"}
{"id": 6591384788862151733, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, xena, heidi, carol, dave, zoe, michelle, and vic. There are 8 items/roles: zebra, necklace, guitar, frisbee, whale, slinky, iceskates, and quadcopter. Currently, carol is assigned frisbee, xena is assigned whale, michelle is assigned slinky, vic is assigned necklace, dave is assigned iceskates, zoe is assigned quadcopter, heidi is assigned guitar, and alice is assigned zebra.", "question": "Is it possible to transition to a state where the following holds: xena is assigned zebra and zoe is assigned zebra?", "answer": "no"}
{"id": -2981782850390496734, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: alice, xena, heidi, dave, ted, bob, and kevin. There are 7 items/roles: quince, leek, ulluco, valerian, parsnip, mushroom, and yam. Currently, heidi is assigned quince, kevin is assigned mushroom, bob is assigned parsnip, dave is assigned ulluco, alice is assigned yam, xena is assigned valerian, and ted is assigned leek.", "question": "Is it possible to transition to a state where the following holds: heidi is assigned mushroom and kevin is assigned ulluco?", "answer": "yes"}
{"id": 6637722070391320395, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: alice, xena, heidi, dave, ted, bob, and kevin. There are 7 items/roles: quince, leek, ulluco, valerian, parsnip, mushroom, and yam. Currently, heidi is assigned valerian, bob is assigned parsnip, ted is assigned quince, kevin is assigned leek, alice is assigned mushroom, xena is assigned ulluco, and dave is assigned yam.", "question": "Is it possible to transition to a state where the following holds: heidi is assigned quince and alice is assigned quince?", "answer": "no"}
{"id": -2656658457923006555, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: quentin, frank, xena, liam, bob, and vic. There are 6 items/roles: sander, ratchet, wrench, pliers, knead, and nibbler. Currently, frank is assigned ratchet, vic is assigned sander, liam is assigned knead, bob is assigned wrench, xena is assigned nibbler, and quentin is assigned pliers.", "question": "Is it possible to transition to a state where the following holds: xena is assigned wrench and frank is assigned wrench?", "answer": "no"}
{"id": 4312873891786312159, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: quentin, frank, xena, liam, bob, and vic. There are 6 items/roles: sander, ratchet, wrench, pliers, knead, and nibbler. Currently, vic is assigned nibbler, frank is assigned ratchet, liam is assigned wrench, xena is assigned pliers, quentin is assigned knead, and bob is assigned sander.", "question": "Is it possible to transition to a state where the following holds: liam is assigned ratchet and quentin is assigned wrench?", "answer": "yes"}
{"id": -699973422265378932, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: alice, xena, heidi, dave, ted, bob, and kevin. There are 7 items/roles: quince, leek, ulluco, valerian, parsnip, mushroom, and yam. Currently, heidi is assigned quince, kevin is assigned mushroom, xena is assigned parsnip, dave is assigned leek, ted is assigned ulluco, alice is assigned yam, and bob is assigned valerian.", "question": "Is it possible to transition to a state where the following holds: dave is assigned parsnip and xena is assigned mushroom?", "answer": "yes"}
{"id": 1764250917309631630, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: quentin, frank, xena, liam, bob, and vic. There are 6 items/roles: sander, ratchet, wrench, pliers, knead, and nibbler. Currently, frank is assigned sander, bob is assigned ratchet, vic is assigned knead, xena is assigned wrench, liam is assigned nibbler, and quentin is assigned pliers.", "question": "Is it possible to transition to a state where the following holds: frank is assigned wrench and quentin is assigned ratchet?", "answer": "yes"}
{"id": 1624672500270803855, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, xena, heidi, carol, dave, zoe, michelle, and vic. There are 8 items/roles: zebra, necklace, guitar, frisbee, whale, slinky, iceskates, and quadcopter. Currently, dave is assigned whale, michelle is assigned iceskates, alice is assigned slinky, vic is assigned necklace, heidi is assigned quadcopter, xena is assigned frisbee, zoe is assigned zebra, and carol is assigned guitar.", "question": "Is it possible to transition to a state where the following holds: xena is assigned guitar and xena is assigned zebra?", "answer": "no"}
{"id": 7343057538436520425, "group": "reachable_atom_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: quentin, frank, xena, liam, bob, and vic. There are 6 items/roles: sander, ratchet, wrench, pliers, knead, and nibbler. Currently, frank is assigned pliers, vic is assigned nibbler, quentin is assigned ratchet, bob is assigned wrench, xena is assigned knead, and liam is assigned sander.", "question": "Is it possible to transition to a state where the following holds: bob is assigned pliers and liam is assigned pliers?", "answer": "no"}
{"id": -2019104209955033347, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. stoveburner4 and stoveburner2 are at location21. drawer1 is at location20. microwave1 is at location24. cabinet5 is at location13. countertop3 is at location3. countertop2 and coffeemachine1 are at location11. garbagecan1 is at location8. drawer3 is at location27. countertop1 is at location2. cabinet3 is at location16. cabinet6 is at location4. cabinet1 is at location19. shelf2 is at location29. shelf1 is at location17. fridge1 is at location10. shelf3 is at location26. cabinet4 is at location14. sinkbasin1 is at location6. drawer2 is at location15. cabinet2 is at location22. toaster1 is at location9.  Currently, the objects are at locations as follows. pan1 and spoon1 are at location11. glassbottle3, potato3, and apple2 are at location8. pot1 is at location5. tomato1, potato1, egg2, mug1, plate2, bowl1, and egg1 are at location10. dishsponge1 and fork1 are at location27. papertowelroll1, cellphone3, spoon2, glassbottle1, butterknife1, knife2, bread1, plate3, creditcard1, spatula1, mug2, and houseplant1 are at location3. window1 is at location30. plate1, peppershaker1, lettuce1, knife1, lettuce2, cellphone1, cellphone2, and apple1 are at location2. stoveknob1 is at location18. lightswitch1 is at location25. window2 is at location28. chair2 is at location1. potato2, spatula2, glassbottle2, spatula3, and sink1 are at location6. peppershaker3 is at location16. statue1 and soapbottle2 are at location26. bowl2 is at location29. saltshaker1 is at location15. chair1 is at location23. dishsponge2 and vase1 are at location13. peppershaker2 is at location20. soapbottle1 is at location4. cup1 is at location24. stoveknob4 is at location7. vase2 is at location17. stoveknob2 and stoveknob3 are at location12. agent agent1 is at location location3. The objects are in/on receptacle as follows. lettuce1, lettuce2, knife1, plate1, cellphone2, cellphone1, apple1, and peppershaker1 are on countertop1. mug2, butterknife1, creditcard1, cellphone3, spoon2, glassbottle1, houseplant1, bread1, spatula1, plate3, knife2, and papertowelroll1 are on countertop3. mug1, potato1, tomato1, egg1, plate2, egg2, and bowl1 are in fridge1. saltshaker1 is in drawer2. dishsponge2 and vase1 are in cabinet5. peppershaker2 is in drawer1. pan1 is on stoveburner2. spatula2, spatula3, glassbottle2, and potato2 are in sinkbasin1. pan1 and spoon1 are on countertop2. soapbottle2 and statue1 are on shelf3. pot1 is on stoveburner3. glassbottle3, apple2, and potato3 are in garbagecan1. pan1 is on stoveburner4. vase2 is on shelf1. soapbottle1 is in cabinet6. fork1 and dishsponge1 are in drawer3. peppershaker3 is in cabinet3. cup1 is in microwave1. pot1 is on stoveburner1. bowl2 is on shelf2. cellphone2 is on plate1. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. ", "question": "Is it possible to transition to a state where the following holds: It has been validated that an object of type papertowelrolltype is examined under an object of type plungertype?", "answer": "no"}
{"id": -3858097941701578166, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. stoveburner4 and stoveburner2 are at location21. drawer1 is at location20. microwave1 is at location24. cabinet5 is at location13. countertop3 is at location3. countertop2 and coffeemachine1 are at location11. garbagecan1 is at location8. drawer3 is at location27. countertop1 is at location2. cabinet3 is at location16. cabinet6 is at location4. cabinet1 is at location19. shelf2 is at location29. shelf1 is at location17. fridge1 is at location10. shelf3 is at location26. cabinet4 is at location14. sinkbasin1 is at location6. drawer2 is at location15. cabinet2 is at location22. toaster1 is at location9.  Currently, the objects are at locations as follows. pan1 and spoon1 are at location11. glassbottle3, potato3, and apple2 are at location8. pot1 is at location5. tomato1, potato1, egg2, mug1, plate2, bowl1, and egg1 are at location10. dishsponge1 and fork1 are at location27. papertowelroll1, cellphone3, spoon2, glassbottle1, butterknife1, knife2, bread1, plate3, creditcard1, spatula1, mug2, and houseplant1 are at location3. window1 is at location30. plate1, peppershaker1, lettuce1, knife1, lettuce2, cellphone1, cellphone2, and apple1 are at location2. stoveknob1 is at location18. lightswitch1 is at location25. window2 is at location28. chair2 is at location1. potato2, spatula2, glassbottle2, spatula3, and sink1 are at location6. peppershaker3 is at location16. statue1 and soapbottle2 are at location26. bowl2 is at location29. saltshaker1 is at location15. chair1 is at location23. dishsponge2 and vase1 are at location13. peppershaker2 is at location20. soapbottle1 is at location4. cup1 is at location24. stoveknob4 is at location7. vase2 is at location17. stoveknob2 and stoveknob3 are at location12. agent agent1 is at location location15. The objects are in/on receptacle as follows. lettuce1, lettuce2, knife1, plate1, cellphone2, cellphone1, apple1, and peppershaker1 are on countertop1. mug2, butterknife1, creditcard1, cellphone3, spoon2, glassbottle1, houseplant1, bread1, spatula1, plate3, knife2, and papertowelroll1 are on countertop3. mug1, potato1, tomato1, egg1, plate2, egg2, and bowl1 are in fridge1. saltshaker1 is in drawer2. dishsponge2 and vase1 are in cabinet5. peppershaker2 is in drawer1. pan1 is on stoveburner2. spatula2, spatula3, glassbottle2, and potato2 are in sinkbasin1. pan1 and spoon1 are on countertop2. soapbottle2 and statue1 are on shelf3. pot1 is on stoveburner3. glassbottle3, apple2, and potato3 are in garbagecan1. pan1 is on stoveburner4. vase2 is on shelf1. soapbottle1 is in cabinet6. fork1 and dishsponge1 are in drawer3. peppershaker3 is in cabinet3. cup1 is in microwave1. pot1 is on stoveburner1. bowl2 is on shelf2. cellphone2 is on plate1. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. ", "question": "Is it possible to transition to a state where the following holds: agent agent1 is at location location19 and It has been validated that an object of type bowltype is in a receptacle of type fridgetype?", "answer": "yes"}
{"id": -2316437304382535862, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 16 object types: 1 candle, 2 cloths, 3 faucets, 2 handtowels, 1 lightswitch, 1 mirror, 1 plunger, 1 scrubbrush, 1 showerdoor, 1 showerglass, 2 sinks, 2 soapbars, 2 soapbottles, 3 spraybottles, 2 toiletpapers, 1 towel, 8 receptacle types: 4 cabinets, 1 countertop, 1 garbagecan, 2 handtowelholders, 2 sinkbasins, 1 toiletpaperhanger, 1 toilet, 1 towelholder, and 19 locations all numbered consecutively.  The receptacles are at locations as follows. cabinet2 is at location11. toilet1 is at location7. cabinet3 is at location8. garbagecan1 is at location2. countertop1 is at location3. cabinet4 is at location15. sinkbasin2 is at location6. handtowelholder1 is at location18. handtowelholder2 is at location17. towelholder1 is at location5. sinkbasin1 is at location16. cabinet1 is at location4. toiletpaperhanger1 is at location10.  Currently, the objects are at locations as follows. spraybottle1 and soapbottle2 are at location4. toiletpaper1 and soapbar1 are at location7. plunger1 and scrubbrush1 are at location10. soapbottle1 and spraybottle2 are at location15. spraybottle3 is at location2. mirror1 is at location9. cloth2 is at location11. sink2 is at location14. handtowel1 is at location18. lightswitch1 is at location13. cloth1 and candle1 are at location3. sink1 is at location12. towel1 and showerglass1 are at location5. showerdoor1 is at location1. toiletpaper2 is at location8. handtowel2 is at location17. agent agent1 is at location location18. The objects are in/on receptacle as follows. toiletpaper1 and soapbar1 are in toilet1. spraybottle1 and soapbottle2 are in cabinet1. toiletpaper2 is in cabinet3. candle1 and cloth1 are on countertop1. spraybottle3 is in garbagecan1. soapbottle1 and spraybottle2 are in cabinet4. handtowel2 is on handtowelholder2. handtowel1 is on handtowelholder1. cloth2 is in cabinet2. towel1 is on towelholder1. cabinet2, cabinet1, cabinet4, and cabinet3 are closed. soapbar2 is clean. It has been validated that an object of type toiletpapertype is in a receptacle of type toilettype. agent1 is holding object soapbar2. ", "question": "Is it possible to transition to a state where the following holds: Nothing has been validated?", "answer": "no"}
{"id": -5684355342343410798, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. drawer3 is at location17. drawer2 is at location18. shelf2 is at location25. desk2 is at location10. shelf5 is at location22. shelf3 is at location11. garbagecan1 is at location2. drawer6 is at location1. desk1 is at location3. drawer1 is at location21. safe1 is at location6. drawer5 and drawer4 are at location12. shelf1 is at location20. bed1 is at location13. laundryhamper1 is at location8. shelf6 is at location24. shelf4 is at location23.  Currently, the objects are at locations as follows. pillow1, book1, pillow2, laptop2, and laptop1 are at location13. laundryhamperlid1 is at location8. keychain1 and keychain2 are at location6. mug2, cellphone2, cd3, pen1, and pencil3 are at location10. window1 is at location5. blinds1 is at location16. desklamp1, alarmclock3, and bowl2 are at location23. lightswitch1 is at location14. creditcard1 and pencil2 are at location22. cd1, bowl1, alarmclock1, pencil1, and mug1 are at location3. chair2 is at location26. blinds2 is at location15. baseballbat1 is at location9. cd2 is at location2. chair1 is at location21. cellphone3 is at location12. mirror1 is at location19. window2 is at location4. alarmclock2 is at location11. bowl3 is at location24. basketball1 is at location7. agent agent1 is at location location13. The objects are in/on receptacle as follows. pencil1, mug1, alarmclock1, bowl1, and cd1 are on desk1. laptop2, laptop1, book1, pillow2, and pillow1 are in bed1. cd2 is in garbagecan1. creditcard1 and pencil2 are on shelf5. desklamp1, alarmclock3, cd3, pen1, pencil3, mug2, cellphone2, and bowl2 are on desk2. alarmclock3, desklamp1, and bowl2 are on shelf4. cellphone3 is in drawer5. keychain1 and keychain2 are in safe1. bowl3 is on shelf6. alarmclock2 is on shelf3. drawer1, drawer3, safe1, and drawer6 are closed. desklamp1 is off. Nothing has been validated. agent1 is holding object cellphone1. ", "question": "Is it possible to transition to a state where the following holds: It has been validated that an object of type pillowtype is in a receptacle of type bedtype?", "answer": "yes"}
{"id": -2381222796914070965, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. stoveburner4 and stoveburner2 are at location21. drawer1 is at location20. microwave1 is at location24. cabinet5 is at location13. countertop3 is at location3. countertop2 and coffeemachine1 are at location11. garbagecan1 is at location8. drawer3 is at location27. countertop1 is at location2. cabinet3 is at location16. cabinet6 is at location4. cabinet1 is at location19. shelf2 is at location29. shelf1 is at location17. fridge1 is at location10. shelf3 is at location26. cabinet4 is at location14. sinkbasin1 is at location6. drawer2 is at location15. cabinet2 is at location22. toaster1 is at location9.  Currently, the objects are at locations as follows. pan1 and spoon1 are at location11. glassbottle3, potato3, and apple2 are at location8. pot1 is at location5. tomato1, potato1, egg2, mug1, plate2, bowl1, and egg1 are at location10. dishsponge1 and fork1 are at location27. papertowelroll1, cellphone3, spoon2, glassbottle1, butterknife1, knife2, bread1, plate3, creditcard1, spatula1, and houseplant1 are at location3. window1 is at location30. plate1, peppershaker1, lettuce1, knife1, lettuce2, cellphone1, cellphone2, and apple1 are at location2. stoveknob1 is at location18. lightswitch1 is at location25. window2 is at location28. chair2 is at location1. potato2, spatula2, glassbottle2, spatula3, and sink1 are at location6. peppershaker3 is at location16. statue1 and soapbottle2 are at location26. bowl2 is at location29. saltshaker1 is at location15. chair1 is at location23. dishsponge2 and vase1 are at location13. peppershaker2 is at location20. soapbottle1 is at location4. cup1 is at location24. stoveknob4 is at location7. vase2 is at location17. stoveknob2 and stoveknob3 are at location12. agent agent1 is at location location5. The objects are in/on receptacle as follows. lettuce1, lettuce2, knife1, plate1, cellphone2, cellphone1, apple1, and peppershaker1 are on countertop1. mug1, potato1, tomato1, egg1, plate2, egg2, and bowl1 are in fridge1. saltshaker1 is in drawer2. butterknife1, creditcard1, cellphone3, spoon2, glassbottle1, houseplant1, bread1, spatula1, plate3, knife2, and papertowelroll1 are on countertop3. dishsponge2 and vase1 are in cabinet5. peppershaker2 is in drawer1. pan1 is on stoveburner2. spatula2, spatula3, glassbottle2, and potato2 are in sinkbasin1. pan1 and spoon1 are on countertop2. soapbottle2 and statue1 are on shelf3. pot1 is on stoveburner3. glassbottle3, apple2, and potato3 are in garbagecan1. pan1 is on stoveburner4. vase2 is on shelf1. soapbottle1 is in cabinet6. fork1 and dishsponge1 are in drawer3. peppershaker3 is in cabinet3. cup1 is in microwave1. pot1 is on stoveburner1. bowl2 is on shelf2. cellphone2 is on plate1. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. mug1 is cool. It has been validated that an object of type dishspongetype is in a receptacle of type drawertype. agent1 is holding object mug2. ", "question": "Is it possible to transition to a state where the following holds: houseplant1 is at location3 and coffeetabletype is off?", "answer": "no"}
{"id": -6242863931191345145, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. stoveburner4 and stoveburner2 are at location21. drawer1 is at location20. microwave1 is at location24. cabinet5 is at location13. countertop3 is at location3. countertop2 and coffeemachine1 are at location11. garbagecan1 is at location8. drawer3 is at location27. countertop1 is at location2. cabinet3 is at location16. cabinet6 is at location4. cabinet1 is at location19. shelf2 is at location29. shelf1 is at location17. fridge1 is at location10. shelf3 is at location26. cabinet4 is at location14. sinkbasin1 is at location6. drawer2 is at location15. cabinet2 is at location22. toaster1 is at location9.  Currently, the objects are at locations as follows. pan1 and spoon1 are at location11. glassbottle3, potato3, and apple2 are at location8. pot1 is at location5. tomato1, potato1, egg2, mug1, plate2, bowl1, and egg1 are at location10. dishsponge1 and fork1 are at location27. papertowelroll1, cellphone3, spoon2, glassbottle1, butterknife1, knife2, bread1, plate3, creditcard1, spatula1, mug2, and houseplant1 are at location3. window1 is at location30. plate1, peppershaker1, lettuce1, knife1, lettuce2, cellphone1, cellphone2, and apple1 are at location2. stoveknob1 is at location18. lightswitch1 is at location25. window2 is at location28. chair2 is at location1. potato2, spatula2, glassbottle2, spatula3, and sink1 are at location6. peppershaker3 is at location16. statue1 and soapbottle2 are at location26. bowl2 is at location29. saltshaker1 is at location15. chair1 is at location23. dishsponge2 and vase1 are at location13. peppershaker2 is at location20. soapbottle1 is at location4. cup1 is at location24. stoveknob4 is at location7. vase2 is at location17. stoveknob2 and stoveknob3 are at location12. agent agent1 is at location location16. The objects are in/on receptacle as follows. lettuce1, lettuce2, knife1, plate1, cellphone2, cellphone1, apple1, and peppershaker1 are on countertop1. mug2, butterknife1, creditcard1, cellphone3, spoon2, glassbottle1, houseplant1, bread1, spatula1, plate3, knife2, and papertowelroll1 are on countertop3. mug1, potato1, tomato1, egg1, plate2, egg2, and bowl1 are in fridge1. saltshaker1 is in drawer2. dishsponge2 and vase1 are in cabinet5. peppershaker2 is in drawer1. pan1 is on stoveburner2. spatula2, spatula3, glassbottle2, and potato2 are in sinkbasin1. pan1 and spoon1 are on countertop2. soapbottle2 and statue1 are on shelf3. pot1 is on stoveburner3. glassbottle3, apple2, and potato3 are in garbagecan1. pan1 is on stoveburner4. vase2 is on shelf1. soapbottle1 is in cabinet6. fork1 and dishsponge1 are in drawer3. peppershaker3 is in cabinet3. cup1 is in microwave1. pot1 is on stoveburner1. bowl2 is on shelf2. cellphone2 is on plate1. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. mug1 is cool. Nothing has been validated. agent1's hands are empty. ", "question": "Is it possible to transition to a state where the following holds: houseplant1 is on countertop3 and It has been validated that an object of type pottype is in a receptacle of type laundryhampertype?", "answer": "no"}
{"id": *******************, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. stoveburner4 and stoveburner2 are at location21. drawer1 is at location20. microwave1 is at location24. cabinet5 is at location13. countertop3 is at location3. countertop2 and coffeemachine1 are at location11. garbagecan1 is at location8. drawer3 is at location27. countertop1 is at location2. cabinet3 is at location16. cabinet6 is at location4. cabinet1 is at location19. shelf2 is at location29. shelf1 is at location17. fridge1 is at location10. shelf3 is at location26. cabinet4 is at location14. sinkbasin1 is at location6. drawer2 is at location15. cabinet2 is at location22. toaster1 is at location9.  Currently, the objects are at locations as follows. pan1, cellphone1, and peppershaker1 are at location11. bowl1 and glassbottle2 are at location14. spoon1, creditcard1, bread1, lettuce2, statue1, mug1, cellphone3, and creditcard2 are at location2. pot1 is at location5. tomato1, soapbottle1, saltshaker2, butterknife1, glassbottle3, lettuce3, knife1, fork2, spoon3, and houseplant1 are at location3. dishsponge1 is at location15. window1 is at location30. spoon2, fork1, spatula2, and sink1 are at location6. plate1 is at location4. potato1, apple1, potato2, lettuce1, cup1, and cup2 are at location10. stoveknob1 is at location18. lightswitch1 is at location25. window2 is at location28. chair2 is at location1. papertowelroll1 and vase2 are at location29. egg1 and apple2 are at location8. vase1 is at location17. saltshaker3 is at location26. chair1 is at location23. peppershaker2 and cellphone2 are at location20. spatula1 is at location27. stoveknob4 is at location7. glassbottle1 is at location19. saltshaker1 is at location16. stoveknob2 and stoveknob3 are at location12. agent agent1 is at location location8. The objects are in/on receptacle as follows. glassbottle2 and bowl1 are in cabinet4. lettuce3, soapbottle1, butterknife1, saltshaker2, tomato1, houseplant1, fork2, spoon3, glassbottle3, and knife1 are on countertop3. papertowelroll1 and vase2 are on shelf2. spatula1 is in drawer3. peppershaker1, pan1, and cellphone1 are on countertop2. plate1 is in cabinet6. potato1, potato2, cup1, lettuce1, apple1, and cup2 are in fridge1. creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, statue1, and spoon1 are on countertop1. saltshaker3 is on shelf3. peppershaker2 and cellphone2 are in drawer1. pan1 is on stoveburner2. spatula2, spoon2, and fork1 are in sinkbasin1. pot1 is on stoveburner3. apple2 and egg1 are in garbagecan1. vase1 is on shelf1. pan1 is on stoveburner4. dishsponge1 is in drawer2. glassbottle1 is in cabinet1. pot1 is on stoveburner1. saltshaker1 is in cabinet3. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. Nothing has been validated. agent1 is holding object egg2. ", "question": "Is it possible to transition to a state where the following holds: agent agent1 is at location location5?", "answer": "yes"}
{"id": -3664968030174976659, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner1 and stoveburner3 are at location5. stoveburner4 and stoveburner2 are at location21. drawer1 is at location20. microwave1 is at location24. cabinet5 is at location13. countertop3 is at location3. countertop2 and coffeemachine1 are at location11. garbagecan1 is at location8. drawer3 is at location27. countertop1 is at location2. cabinet3 is at location16. cabinet6 is at location4. cabinet1 is at location19. shelf2 is at location29. shelf1 is at location17. fridge1 is at location10. shelf3 is at location26. cabinet4 is at location14. sinkbasin1 is at location6. drawer2 is at location15. cabinet2 is at location22. toaster1 is at location9.  Currently, the objects are at locations as follows. pan1, cellphone1, and peppershaker1 are at location11. bowl1 and glassbottle2 are at location14. spoon1, creditcard1, bread1, lettuce2, statue1, mug1, cellphone3, and creditcard2 are at location2. pot1 is at location5. tomato1, soapbottle1, saltshaker2, butterknife1, glassbottle3, lettuce3, knife1, fork2, spoon3, and houseplant1 are at location3. dishsponge1 is at location15. window1 is at location30. spoon2, fork1, spatula2, and sink1 are at location6. plate1 is at location4. potato1, apple1, potato2, lettuce1, cup1, and cup2 are at location10. stoveknob1 is at location18. lightswitch1 is at location25. window2 is at location28. chair2 is at location1. papertowelroll1 and vase2 are at location29. egg2 and apple2 are at location8. vase1 is at location17. saltshaker3 is at location26. chair1 is at location23. peppershaker2 and cellphone2 are at location20. spatula1 is at location27. stoveknob4 is at location7. glassbottle1 is at location19. saltshaker1 is at location16. stoveknob2 and stoveknob3 are at location12. agent agent1 is at location location8. The objects are in/on receptacle as follows. glassbottle2 and bowl1 are in cabinet4. lettuce3, soapbottle1, butterknife1, saltshaker2, tomato1, houseplant1, fork2, spoon3, glassbottle3, and knife1 are on countertop3. papertowelroll1 and vase2 are on shelf2. spatula1 is in drawer3. peppershaker1, pan1, and cellphone1 are on countertop2. plate1 is in cabinet6. potato1, potato2, cup1, lettuce1, apple1, and cup2 are in fridge1. creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, statue1, and spoon1 are on countertop1. saltshaker3 is on shelf3. peppershaker2 and cellphone2 are in drawer1. pan1 is on stoveburner2. spatula2, spoon2, and fork1 are in sinkbasin1. pot1 is on stoveburner3. apple2 and egg2 are in garbagecan1. vase1 is on shelf1. pan1 is on stoveburner4. dishsponge1 is in drawer2. glassbottle1 is in cabinet1. pot1 is on stoveburner1. saltshaker1 is in cabinet3. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. egg1 is hot. It has been validated that an object of type soapbottletype is in a receptacle of type countertoptype. agent1 is holding object egg1. ", "question": "Is it possible to transition to a state where the following holds: Nothing has been validated?", "answer": "no"}
{"id": 8455360622799739225, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. drawer3 is at location17. drawer2 is at location18. shelf2 is at location25. desk2 is at location10. shelf5 is at location22. shelf3 is at location11. garbagecan1 is at location2. drawer6 is at location1. desk1 is at location3. drawer1 is at location21. safe1 is at location6. drawer5 and drawer4 are at location12. shelf1 is at location20. bed1 is at location13. laundryhamper1 is at location8. shelf6 is at location24. shelf4 is at location23.  Currently, the objects are at locations as follows. pillow1, cellphone1, book1, pillow2, laptop2, and laptop1 are at location13. laundryhamperlid1 is at location8. keychain1 and keychain2 are at location6. mug2, cellphone2, pen1, cd3, and pencil3 are at location10. window1 is at location5. blinds1 is at location16. desklamp1, alarmclock3, and bowl2 are at location23. lightswitch1 is at location14. creditcard1 and pencil2 are at location22. cd1, bowl1, alarmclock1, pencil1, and mug1 are at location3. chair2 is at location26. blinds2 is at location15. baseballbat1 is at location9. cd2 is at location2. chair1 is at location21. cellphone3 is at location12. mirror1 is at location19. window2 is at location4. alarmclock2 is at location11. bowl3 is at location24. basketball1 is at location7. agent agent1 is at location location2. The objects are in/on receptacle as follows. pencil1, mug1, alarmclock1, bowl1, and cd1 are on desk1. laptop2, laptop1, book1, pillow2, cellphone1, and pillow1 are in bed1. cd2 is in garbagecan1. creditcard1 and pencil2 are on shelf5. desklamp1, alarmclock3, cd3, pen1, pencil3, mug2, cellphone2, and bowl2 are on desk2. alarmclock3, desklamp1, and bowl2 are on shelf4. cellphone3 is in drawer5. keychain1 and keychain2 are in safe1. bowl3 is on shelf6. alarmclock2 is on shelf3. drawer1, drawer3, safe1, and drawer6 are closed. desklamp1 is off. Nothing has been validated. agent1's hands are empty. ", "question": "Is it possible to transition to a state where the following holds: agent agent1 is at location location19 and pillow2 is at location13?", "answer": "no"}
{"id": -582553878352808518, "group": "reachable_atom_bool", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. toaster1 is at location10. cabinet3 is at location17. countertop1 is at location3. garbagecan1 is at location9. microwave1 is at location25. stoveburner1 and stoveburner3 are at location6. cabinet6 is at location5. sinkbasin1 is at location7. countertop2 and coffeemachine1 are at location12. drawer1 is at location21. cabinet5 is at location14. cabinet1 is at location20. stoveburner2 and stoveburner4 are at location22. shelf1 is at location18. fridge1 is at location11. countertop3 is at location4. cabinet2 is at location23. cabinet4 is at location15. shelf3 is at location27. drawer3 is at location28. shelf2 is at location30. drawer2 is at location16.  Currently, the objects are at locations as follows. sink1, fork1, egg2, cup3, tomato3, and potato3 are at location7. mug2, glassbottle2, bowl1, and creditcard2 are at location30. cellphone3, statue2, spatula3, butterknife1, butterknife2, knife1, fork2, peppershaker2, houseplant1, soapbottle3, and spoon2 are at location4. window1 is at location31. egg1, cup1, tomato1, potato2, lettuce1, cup2, tomato2, and potato1 are at location11. spoon1 and dishsponge1 are at location16. dishsponge3, apple1, bread1, apple3, creditcard1, cellphone1, spatula2, apple2, and statue1 are at location3. stoveknob1 is at location19. vase2 is at location18. creditcard3 and saltshaker1 are at location27. chair2 is at location1. vase1 and soapbottle1 are at location5. cellphone2 is at location21. lightswitch1 is at location26. glassbottle1, soapbottle2, and papertowelroll1 are at location9. plate2 and plate1 are at location14. spatula1 is at location28. chair1 is at location24. dishsponge2 is at location2. stoveknob3 and stoveknob2 are at location13. mug1 is at location25. window2 is at location29. pan1 is at location6. stoveknob4 is at location8. peppershaker1 is at location20. pot1 is at location22. agent agent1 is at location location21. The objects are in/on receptacle as follows. statue2, butterknife1, cellphone3, spoon2, houseplant1, peppershaker2, fork2, soapbottle3, knife1, butterknife2, and spatula3 are on countertop3. potato3, tomato3, egg2, cup3, and fork1 are in sinkbasin1. apple3, creditcard1, apple2, dishsponge3, cellphone1, apple1, spatula2, bread1, and statue1 are on countertop1. spatula1 is in drawer3. mug1 is in microwave1. potato1, tomato1, potato2, cup1, lettuce1, tomato2, egg1, and cup2 are in fridge1. glassbottle1, papertowelroll1, and soapbottle2 are in garbagecan1. dishsponge2, plate1, and plate2 are in cabinet5. pan1 is on stoveburner3. pot1 is on stoveburner2. pot1 is on stoveburner4. mug2, glassbottle2, bowl1, and creditcard2 are on shelf2. creditcard3 and saltshaker1 are on shelf3. vase1 and soapbottle1 are in cabinet6. dishsponge2 is on plate1. pan1 is on stoveburner1. vase2 is on shelf1. spoon1 and dishsponge1 are in drawer2. cellphone2 is in drawer1. peppershaker1 is in cabinet1. drawer2, drawer3, microwave1, cabinet5, cabinet2, drawer1, fridge1, and cabinet1 are closed. It has been validated that an object of type papertowelrolltype is in a receptacle of type garbagecantype. agent1's hands are empty. ", "question": "Is it possible to transition to a state where the following holds: teddybeartype is toggled?", "answer": "no"}
