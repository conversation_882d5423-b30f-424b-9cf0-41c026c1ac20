{"id": -3115201149135125328, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 2 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c1 and c0 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0 and Car c1 is at location l2.", "question": "Given the plan: \"load the car c1 at location l1 on to the ferry, unload the car c1 from the ferry to location l1, load the car c1 at location l1 on to the ferry, sail from location l1 to location l2, unload the car c1 from the ferry to location l2, load the car c1 at location l2 on to the ferry, unload the car c1 from the ferry to location l2, sail from location l2 to location l1, load the car c0 at location l1 on to the ferry, sail from location l1 to location l0, unload the car c0 from the ferry to location l0\"; can the following action be removed from this plan and still have a valid plan: load the car c1 at location l1 on to the ferry?", "answer": "no"}
{"id": 461367997804057344, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 2 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 and c1 are at l0. The goal is to reach a state where the following facts hold: Car c1 is at location l1 and Car c0 is at location l1.", "question": "Given the plan: \"board the car c1 at location l0 on to the ferry, debark car c1 to location l0 from the ferry, board the car c1 at location l0 on to the ferry, sail from location l0 to location l1, debark car c1 to location l1 from the ferry, board the car c1 at location l1 on to the ferry, debark car c1 to location l1 from the ferry, sail from location l1 to location l0, board the car c0 at location l0 on to the ferry, sail from location l0 to location l1, debark car c0 to location l1 from the ferry, board the car c0 at location l1 on to the ferry, debark car c0 to location l1 from the ferry\"; can the following action be removed from this plan and still have a valid plan: board the car c1 at location l0 on to the ferry?", "answer": "no"}
{"id": 6046980184545774381, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 2 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 and c1 are at l0. The goal is to reach a state where the following facts hold: Car c1 is at location l1 and Car c0 is at location l1.", "question": "Given the plan: \"embark the car c0 at location l0 on to the ferry, debark the car c0 to location l0 from the ferry, embark the car c0 at location l0 on to the ferry, sail from location l0 to location l1, sail from location l1 to location l0, sail from location l0 to location l1, debark the car c0 to location l1 from the ferry, sail from location l1 to location l0, embark the car c1 at location l0 on to the ferry, sail from location l0 to location l1, debark the car c1 to location l1 from the ferry, embark the car c0 at location l1 on to the ferry, sail from location l1 to location l0, sail from location l0 to location l1, debark the car c0 to location l1 from the ferry\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: debark the car c0 to location l0 from the ferry and embark the car c0 at location l0 on to the ferry?", "answer": "yes"}
{"id": 84104770484963469, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 2 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c1 and c0 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0 and Car c1 is at location l2.", "question": "Given the plan: \"embark the car c0 at location l1 on to the ferry, debark the car c0 from the ferry to location l1, embark the car c0 at location l1 on to the ferry, sail from location l1 to location l0, debark the car c0 from the ferry to location l0, sail from location l0 to location l1, embark the car c1 at location l1 on to the ferry, sail from location l1 to location l2, debark the car c1 from the ferry to location l2\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: embark the car c0 at location l1 on to the ferry and debark the car c0 from the ferry to location l1?", "answer": "yes"}
{"id": -6991290755221663171, "group": "action_justification_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 10 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0, c9, c2, c8, c3, c1, and c6 are at l0; c7, c5, and c4 are at l1. The goal is to reach a state where the following facts hold: Car c0 is at location l0, Car c4 is at location l0, Car c9 is at location l1, Car c5 is at location l0, Car c2 is at location l1, Car c8 is at location l0, Car c1 is at location l0, Car c6 is at location l0, Car c7 is at location l0, and Car c3 is at location l1.", "question": "Given the plan: \"board the car c3 at location l0, debark car c3 to location l0 from the ferry, board the car c9 at location l0, sail from location l0 to location l1, debark car c9 to location l1 from the ferry, board the car c7 at location l1, sail from location l1 to location l0, debark car c7 to location l0 from the ferry, board the car c3 at location l0, sail from location l0 to location l1, debark car c3 to location l1 from the ferry, board the car c4 at location l1, sail from location l1 to location l0, debark car c4 to location l0 from the ferry, board the car c2 at location l0, sail from location l0 to location l1, debark car c2 to location l1 from the ferry, board the car c5 at location l1, sail from location l1 to location l0, debark car c5 to location l0 from the ferry, sail from location l0 to location l1\"; can the following action be removed from this plan and still have a valid plan: board the car c3 at location l0?", "answer": "no"}
{"id": 5868471440508966448, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t0, p3, and p0 are at l0-0, p1 is at l0-1, a0 and t1 are at l1-0, p2 is at l1-1. The goal is to reach a state where the following facts hold: p1 is at l0-1, p2 is at l0-0, p3 is at l0-0, and p0 is at l0-1.", "question": "Given the plan: \"place the object p0 into the truck t0 at location l0-0, navigate the truck t1 which is in location l1-0 in city c1 to another location l1-1 in the same city, place the object p2 into the truck t1 at location l1-1, navigate the truck t1 which is in location l1-1 in city c1 to another location l1-0 in the same city, remove the object p2 from the truck t1 and place it on the location l1-0, place the object p2 onto the airplane a0 at location l1-0, fly airplane a0 from airport l1-0 to airport l0-0, offload the object p2 from the airplane a0 at location l0-0, place the object p2 into the truck t0 at location l0-0, remove the object p2 from the truck t0 and place it on the location l0-0, navigate the truck t0 which is in location l0-0 in city c0 to another location l0-1 in the same city, remove the object p0 from the truck t0 and place it on the location l0-1\"; can the following action be removed from this plan and still have a valid plan: remove the object p2 from the truck t1 and place it on the location l1-0?", "answer": "no"}
{"id": 4319533793500658709, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l2-2, l2-1, and l2-0 are in c2; l0-0, l0-2, and l0-1 are in c0. Currently, t0 is at l0-2, p0 and a0 are at l2-0, p3 is at l2-1, t1 and p1 are at l1-2, p2 is at l0-1, t2 is at l2-2. The goal is to reach a state where the following facts hold: p1 is at l2-0, p2 is at l2-2, p0 is at l2-1, and p3 is at l1-2.", "question": "Given the plan: \"place the object p1 into the truck t1 at location l1-2, drive the truck t0 in city c0 from location l0-2 to location l0-1, place the object p2 into the truck t0 at location l0-1, drive the truck t0 in city c0 from location l0-1 to location l0-0, offload the object p2 from the truck t0 at location l0-0, drive the truck t2 in city c2 from location l2-2 to location l2-0, place the object p0 into the truck t2 at location l2-0, drive the truck t2 in city c2 from location l2-0 to location l2-1, offload the object p0 from the truck t2 at location l2-1, place the object p3 into the truck t2 at location l2-1, drive the truck t1 in city c1 from location l1-2 to location l1-0, offload the object p1 from the truck t1 at location l1-0, operate the airplane a0 from airport l2-0 to airport l0-0, load object p2 into airplane a0 at location l0-0, operate the airplane a0 from airport l0-0 to airport l1-0, load object p1 into airplane a0 at location l1-0, drive the truck t2 in city c2 from location l2-1 to location l2-0, offload the object p3 from the truck t2 at location l2-0, operate the airplane a0 from airport l1-0 to airport l2-0, unload the object p1 from the airplane a0 at location l2-0, unload the object p2 from the airplane a0 at location l2-0, place the object p2 into the truck t2 at location l2-0, load object p3 into airplane a0 at location l2-0, drive the truck t2 in city c2 from location l2-0 to location l2-2, offload the object p2 from the truck t2 at location l2-2, operate the airplane a0 from airport l2-0 to airport l1-0, unload the object p3 from the airplane a0 at location l1-0, place the object p3 into the truck t1 at location l1-0, drive the truck t1 in city c1 from location l1-0 to location l1-2, offload the object p3 from the truck t1 at location l1-2, drive the truck t2 in city c2 from location l2-2 to location l2-1\"; can the following action be removed from this plan and still have a valid plan: drive the truck t2 in city c2 from location l2-0 to location l2-1?", "answer": "no"}
{"id": -5132796709481820963, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t0, p3, and p0 are at l0-0, p1 is at l0-1, a0 and t1 are at l1-0, p2 is at l1-1. The goal is to reach a state where the following facts hold: p1 is at l0-1, p2 is at l0-0, p3 is at l0-0, and p0 is at l0-1.", "question": "Given the plan: \"drive the truck t1 in city c1 from location l1-0 to location l1-1, load object p2 into truck t1 at location l1-1, drive the truck t1 in city c1 from location l1-1 to location l1-0, offload the object p2 from the truck t1 at location l1-0, load object p2 into truck t1 at location l1-0, load object p0 into truck t0 at location l0-0, drive the truck t0 in city c0 from location l0-0 to location l0-1, offload the object p2 from the truck t1 at location l1-0, load the object p2 from location l1-0 into the airplane a0, offload the object p0 from the truck t0 at location l0-1, fly the airplane a0 from the airport l1-0 to the airport l0-0, unload object p2 from airplane a0 at location l0-0, drive the truck t0 in city c0 from location l0-1 to location l0-0\"; can the following action be removed from this plan and still have a valid plan: load the object p2 from location l1-0 into the airplane a0?", "answer": "no"}
{"id": 3083207491224888048, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, a0 is at l0-0, t1, p3, and p0 are at l1-2, p1 is at l1-1, t0 is at l0-2, p2 is at l1-0. The goal is to reach a state where the following facts hold: p2 is at l1-2, p1 is at l1-2, p3 is at l0-1, and p0 is at l0-2.", "question": "Given the plan: \"place the object p0 into the truck t1 at location l1-2, place the object p3 into the truck t1 at location l1-2, drive truck t1 from location l1-2 in city c1 to location l1-0 in the same city, unload object p0 from truck t1 at location l1-0, place the object p2 into the truck t1 at location l1-0, unload object p3 from truck t1 at location l1-0, drive truck t1 from location l1-0 in city c1 to location l1-1 in the same city, place the object p1 into the truck t1 at location l1-1, fly the airplane a0 from location l0-0 to location l1-0, load the object p0 from location l1-0 into the airplane a0, load the object p3 from location l1-0 into the airplane a0, drive truck t1 from location l1-1 in city c1 to location l1-2 in the same city, unload object p1 from truck t1 at location l1-2, unload object p2 from truck t1 at location l1-2, fly the airplane a0 from location l1-0 to location l0-0, offload the object p0 from the airplane a0 at location l0-0, offload the object p3 from the airplane a0 at location l0-0, drive truck t0 from location l0-2 in city c0 to location l0-0 in the same city, place the object p0 into the truck t0 at location l0-0, place the object p3 into the truck t0 at location l0-0, drive truck t0 from location l0-0 in city c0 to location l0-1 in the same city, unload object p3 from truck t0 at location l0-1, drive truck t0 from location l0-1 in city c0 to location l0-2 in the same city, unload object p0 from truck t0 at location l0-2, place the object p2 into the truck t1 at location l1-2, drive truck t1 from location l1-2 in city c1 to location l1-1 in the same city, unload object p2 from truck t1 at location l1-1, place the object p2 into the truck t1 at location l1-1, unload object p2 from truck t1 at location l1-1, place the object p2 into the truck t1 at location l1-1, unload object p2 from truck t1 at location l1-1, place the object p2 into the truck t1 at location l1-1, drive truck t1 from location l1-1 in city c1 to location l1-2 in the same city, unload object p2 from truck t1 at location l1-2\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: unload object p2 from truck t1 at location l1-1 and place the object p2 into the truck t1 at location l1-1?", "answer": "yes"}
{"id": 5404878503955522396, "group": "action_justification_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l1-2, l1-0, and l1-1 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, a0 and p0 are at l0-0, p3 and t0 are at l0-2, t1 is at l1-2, p1 is at l1-1, p2 is at l0-1. The goal is to reach a state where the following facts hold: p1 is at l1-1, p0 is at l1-1, p2 is at l1-0, and p3 is at l1-2.", "question": "Given the plan: \"navigate the truck t1 from location l1-2 in city c1 to location l1-0 in the same city, load object p0 into airplane a0 at location l0-0, place the object p3 into the truck t0 at location l0-2, navigate the truck t0 from location l0-2 in city c0 to location l0-1 in the same city, place the object p2 into the truck t0 at location l0-1, navigate the truck t0 from location l0-1 in city c0 to location l0-0 in the same city, offload the object p2 from the truck t0 at location l0-0, load object p2 into airplane a0 at location l0-0, offload the object p3 from the truck t0 at location l0-0, load object p3 into airplane a0 at location l0-0, fly the airplane a0 from airport l0-0 to airport l1-0, remove the object p0 from the airplane a0 and place it on the location l1-0, place the object p0 into the truck t1 at location l1-0, remove the object p3 from the airplane a0 and place it on the location l1-0, place the object p3 into the truck t1 at location l1-0, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, navigate the truck t1 from location l1-1 in city c1 to location l1-2 in the same city, offload the object p3 from the truck t1 at location l1-2, navigate the truck t1 from location l1-2 in city c1 to location l1-0 in the same city, navigate the truck t1 from location l1-0 in city c1 to location l1-1 in the same city, remove the object p2 from the airplane a0 and place it on the location l1-0, offload the object p0 from the truck t1 at location l1-1\"; can the following action be removed from this plan and still have a valid plan: navigate the truck t1 from location l1-2 in city c1 to location l1-0 in the same city?", "answer": "no"}
{"id": 6805655646912598736, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_1, and block_3. The goal is to reach a state where the following facts hold: The block block_1 is currently situated under the block block_2.", "question": "Given the plan: \"pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, put down the object block_2, pick up object block_2 from the table, stack object block_2 on top of object block_1, unstack object block_2 from object block_1, put down the object block_2, pick up object block_2 from the table, stack object block_2 on top of object block_1\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: put down the object block_2 and pick up object block_2 from the table?", "answer": "yes"}
{"id": -3969747564111391627, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_3. The following block(s) is stacked on top of another block: block_2 is on block_1. The goal is to reach a state where the following facts hold: The block block_2 is on top of block block_1 and The block block_3 is currently situated under the block block_1.", "question": "Given the plan: \"collect the object block_3 from the table, put down object block_3, collect the object block_3 from the table, put down object block_3, remove the object block_2 from on top of the object block_1, put down object block_2, collect the object block_1 from the table, place the object block_1 on top of the object block_3, remove the object block_1 from on top of the object block_3, place the object block_1 on top of the object block_3, collect the object block_2 from the table, place the object block_2 on top of the object block_1\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: remove the object block_1 from on top of the object block_3 and place the object block_1 on top of the object block_3?", "answer": "yes"}
{"id": 4208848912703692972, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2 and block_1. The following block(s) is stacked on top of another block: block_3 is on block_2. The goal is to reach a state where the following facts hold: The block block_1 is currently situated above the block block_2 and The block block_3 is currently situated above the block block_1.", "question": "Given the plan: \"collect the object block_1 from the table, put down the object block_1, unstack the object block_3 from the object block_2, put down the object block_3, collect the object block_1 from the table, stack the object block_1 on top of the object block_2, unstack the object block_1 from the object block_2, put down the object block_1, collect the object block_2 from the table, put down the object block_2, collect the object block_1 from the table, stack the object block_1 on top of the object block_2, collect the object block_3 from the table, stack the object block_3 on top of the object block_1\"; can the following action be removed from this plan and still have a valid plan: put down the object block_3?", "answer": "no"}
{"id": 2208528741058528525, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_1, and block_3. The goal is to reach a state where the following facts hold: The block block_2 is currently situated above the block block_1.", "question": "Given the plan: \"collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, put down object block_2, collect the object block_2 from the table, place the object block_2 on top of the object block_1\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: collect the object block_2 from the table and put down object block_2?", "answer": "yes"}
{"id": -1638613008874701049, "group": "action_justification_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_3. The following block(s) is stacked on top of another block: block_2 is on block_3. The goal is to reach a state where the following facts hold: The block block_2 is currently situated under the block block_1.", "question": "Given the plan: \"remove the object block_2 from on top of the object block_3, put down object block_2, remove block_1 from table, stack object block_1 on top of object block_2, remove block_3 from table\"; can the following action be removed from this plan and still have a valid plan: remove block_1 from table?", "answer": "no"}
{"id": -4843711614533901271, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-1f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f4-0f. Key key0-0 is at position f0-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location.", "question": "Given the plan: \"move from f3-1f to f3-0f, move from f3-0f to f4-0f, pick up key key0-1 from place f4-0f, move from f4-0f to f3-0f, put down key key0-1 at current position place f3-0f, move from f3-0f to f2-0f, move from f2-0f to f2-1f, move from f2-1f to f2-0f, move from f2-0f to f2-1f, move from f2-1f to f1-1f, move from f1-1f to f0-1f, move from f0-1f to f0-2f, pick up key key0-0 from place f0-2f, unlock place f0-3f with key key0-0 of shape shape0 from current position place f0-2f, move from f0-2f to f0-3f, move from f0-3f to f0-4f, put down key key0-0 at current position place f0-4f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: move from f2-0f to f2-1f and move from f2-1f to f2-0f?", "answer": "yes"}
{"id": -1688917226210797311, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-3f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f0-1f has shape0 shaped lock, f2-2f has shape0 shaped lock. Key key0-1 is at position f3-3f. Key key0-2 is at position f1-2f. Key key0-0 is at position f4-1f. The goal is to reach a state where the following facts hold: Key key0-2 is at f1-1f location, Key key0-1 is at f2-4f location, and Key key0-0 is at f1-0f location.", "question": "Given the plan: \"move from place f0-3f to place f1-3f, move from place f1-3f to place f2-3f, move from place f2-3f to place f3-3f, acquire the key key0-1 from the place f3-3f, move from place f3-3f to place f2-3f, move from place f2-3f to place f2-4f, put down key key0-1 at current position place f2-4f, move from place f2-4f to place f2-3f, move from place f2-3f to place f3-3f, move from place f3-3f to place f3-2f, move from place f3-2f to place f3-1f, move from place f3-1f to place f4-1f, acquire the key key0-0 from the place f4-1f, move from place f4-1f to place f3-1f, unlock place f2-1f with key key0-0 of shape shape0 from current position place f3-1f, move from place f3-1f to place f2-1f, move from place f2-1f to place f1-1f, move from place f1-1f to place f1-0f, put down key key0-0 at current position place f1-0f, move from place f1-0f to place f1-1f, move from place f1-1f to place f1-2f, acquire the key key0-2 from the place f1-2f, move from place f1-2f to place f1-1f, put down key key0-2 at current position place f1-1f\"; can the following action be removed from this plan and still have a valid plan: move from place f3-3f to place f2-3f?", "answer": "no"}
{"id": -3271071171870454789, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-1f and its arm is empty. All the positions are open except the following: f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f0-1f. Key key0-0 is at position f4-4f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-2f location and Key key0-0 is at f2-4f location.", "question": "Given the plan: \"travel from the current position f2-1f to the next position f1-1f, travel from the current position f1-1f to the next position f0-1f, retrieve the key key0-1 from its current position f0-1f, travel from the current position f0-1f to the next position f1-1f, travel from the current position f1-1f to the next position f0-1f, travel from the current position f0-1f to the next position f1-1f, travel from the current position f1-1f to the next position f2-1f, travel from the current position f2-1f to the next position f2-2f, travel from the current position f2-2f to the next position f3-2f, travel from the current position f3-2f to the next position f4-2f, put down key key0-1 at current position place f4-2f, travel from the current position f4-2f to the next position f4-3f, travel from the current position f4-3f to the next position f4-4f, retrieve the key key0-0 from its current position f4-4f, travel from the current position f4-4f to the next position f3-4f, travel from the current position f3-4f to the next position f2-4f, put down key key0-0 at current position place f2-4f\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: travel from the current position f1-1f to the next position f0-1f and travel from the current position f0-1f to the next position f1-1f?", "answer": "yes"}
{"id": 3809837843735447162, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-1f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-1 is at position f4-0f. Key key0-0 is at position f0-2f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location.", "question": "Given the plan: \"move from place f3-1f to place f4-1f, move from place f4-1f to place f4-0f, acquire the key key0-1 from the place f4-0f, move from place f4-0f to place f3-0f, place the key key0-1 at the current position place f3-0f, move from place f3-0f to place f3-1f, move from place f3-1f to place f2-1f, move from place f2-1f to place f2-2f, move from place f2-2f to place f1-2f, move from place f1-2f to place f0-2f, acquire the key key0-0 from the place f0-2f, unlock the place f0-3f with key key0-0 of shape shape0 from the current position place f0-2f, move from place f0-2f to place f0-3f, move from place f0-3f to place f0-4f, place the key key0-0 at the current position place f0-4f, move from place f0-4f to place f0-3f\"; can the following action be removed from this plan and still have a valid plan: move from place f0-4f to place f0-3f?", "answer": "yes"}
{"id": -3904794027343157419, "group": "action_justification_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f3-4f has shape0 shaped lock. Key key0-1 is at position f3-0f. Key key0-0 is at position f1-2f. The goal is to reach a state where the following facts hold: Key key0-0 is at f1-0f location and Key key0-1 is at f4-4f location.", "question": "Given the plan: \"move from f0-4f to f0-3f, move from f0-3f to f1-3f, move from f1-3f to f1-2f, pick up key key0-0 from place f1-2f, move from f1-2f to f1-1f, move from f1-1f to f1-0f, place the key key0-0 at the current position place f1-0f, move from f1-0f to f2-0f, move from f2-0f to f3-0f, pick up key key0-1 from place f3-0f, move from f3-0f to f3-1f, move from f3-1f to f3-0f, move from f3-0f to f3-1f, move from f3-1f to f4-1f, move from f4-1f to f4-2f, move from f4-2f to f4-3f, move from f4-3f to f4-4f, place the key key0-1 at the current position place f4-4f\"; can the following action be removed from this plan and still have a valid plan: move from f3-0f to f3-1f?", "answer": "no"}
{"id": 6938851313964023072, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 16 tiles and 2 robots. The tiles locations are: tile_10 is to the right of tile_9, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_4 is to the right of tile_3, tile_14 is to the right of tile_13, tile_16 is to the right of tile_15, tile_6 is to the right of tile_5, tile_7 is to the right of tile_6, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_11 is to the right of tile_10. Further, tile_1 is down from tile_5, tile_2 is down from tile_6, tile_10 is down from tile_14, tile_4 is down from tile_8, tile_6 is down from tile_10, tile_3 is down from tile_7, tile_7 is down from tile_11, tile_11 is down from tile_15, tile_9 is down from tile_13, tile_5 is down from tile_9, tile_12 is down from tile_16, and tile_8 is down from tile_12 Currently, robot robot2 is at tile_14 and holding color black and robot robot1 is at tile_8 and holding color white; tile_13, tile_7, tile_9, tile_15, tile_3, tile_2, tile_16, tile_5, tile_11, tile_12, tile_6, tile_1, tile_4, and tile_10 are clear. The goal is to reach a state where the following facts hold: Tile tile_8 is painted in black color, Tile tile_5 is painted in white color, Tile tile_12 is painted in white color, Tile tile_6 is painted in black color, Tile tile_7 is painted in white color, Tile tile_10 is painted in white color, Tile tile_15 is painted in white color, Tile tile_13 is painted in white color, Tile tile_9 is painted in black color, Tile tile_16 is painted in black color, Tile tile_14 is painted in black color, and Tile tile_11 is painted in black color.", "question": "Given the plan: \"move robot robot1 down from tile tile_8 to tile tile_4, move robot robot2 down from tile tile_14 to tile tile_10, apply color black to tile tile_14 above tile tile_10 using robot robot2, navigate robot robot1 from tile tile_4 to tile tile_3 to its left, modify the color of the robot robot2 from black to white, navigate robot robot2 from tile tile_10 to tile tile_9 to its left, apply color white to tile tile_13 above tile tile_9 using robot robot2, move robot robot2 down from tile tile_9 to tile tile_5, move robot robot2 down from tile tile_5 to tile tile_1, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot1 from tile tile_3 to tile tile_7 going upwards, move the robot robot1 from tile tile_7 to tile tile_11 going upwards, apply color white to tile tile_15 above tile tile_11 using robot robot1, navigate robot robot1 from tile tile_11 to tile tile_12 to the right, modify the color of the robot robot2 from white to black, modify the color of the robot robot1 from white to black, apply color black to tile tile_16 above tile tile_12 using robot robot1, modify the color of the robot robot1 from black to white, move robot robot1 down from tile tile_12 to tile tile_8, apply color white to tile tile_12 above tile tile_8 using robot robot1, modify the color of the robot robot1 from white to black, navigate robot robot2 from tile tile_2 to tile tile_1 to its left, modify the color of the robot robot2 from black to white, move robot robot1 down from tile tile_8 to tile tile_4, apply color black to tile tile_8 above tile tile_4 using robot robot1, navigate robot robot1 from tile tile_4 to tile tile_3 to its left, navigate robot robot1 from tile tile_3 to tile tile_2 to its left, move the robot robot1 from tile tile_2 to tile tile_6 going upwards, navigate robot robot1 from tile tile_6 to tile tile_5 to its left, apply color black to tile tile_9 above tile tile_5 using robot robot1, navigate robot robot1 from tile tile_5 to tile tile_6 to the right, apply color white to tile tile_5 above tile tile_1 using robot robot2, move robot robot1 down from tile tile_6 to tile tile_2, navigate robot robot1 from tile tile_2 to tile tile_3 to the right, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot2 from tile tile_2 to tile tile_6 going upwards, apply color white to tile tile_10 above tile tile_6 using robot robot2, navigate robot robot1 from tile tile_3 to tile tile_2 to its left, modify the color of the robot robot2 from white to black, navigate robot robot1 from tile tile_2 to tile tile_3 to the right, navigate robot robot1 from tile tile_3 to tile tile_2 to its left, navigate robot robot2 from tile tile_6 to tile tile_7 to the right, apply color black to tile tile_6 above tile tile_2 using robot robot1, apply color black to tile tile_11 above tile tile_7 using robot robot2, modify the color of the robot robot2 from black to white, move robot robot2 down from tile tile_7 to tile tile_3, apply color white to tile tile_7 above tile tile_3 using robot robot2\"; can the following action be removed from this plan and still have a valid plan: move robot robot2 down from tile tile_7 to tile tile_3?", "answer": "no"}
{"id": 8823952657152655449, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 12 tiles and 2 robots. The tiles locations are: tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_11 is to the right of tile_10. Further, tile_7 is down from tile_10, tile_8 is down from tile_11, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_5 is down from tile_8, tile_4 is down from tile_7, tile_2 is down from tile_5, tile_9 is down from tile_12, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_7 and holding color black and robot robot1 is at tile_12 and holding color white; tile_9, tile_3, tile_11, tile_8, tile_2, tile_5, tile_6, tile_1, tile_4, and tile_10 are clear. The goal is to reach a state where the following facts hold: Tile tile_11 is painted in black color, Tile tile_5 is painted in black color, Tile tile_12 is painted in white color, Tile tile_10 is painted in white color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"move robot robot1 down from tile tile_12 to tile tile_9, use robot robot1 to paint the tile tile_12 above the tile tile_9 with the color white, move robot robot2 down from tile tile_7 to tile tile_4, move robot robot2 down from tile tile_4 to tile tile_1, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move the robot robot1 from the tile tile_9 to the tile on its left tile_8, move the robot robot1 from the tile tile_8 to the tile on its left tile_7, use robot robot1 to paint the tile tile_10 above the tile tile_7 with the color white, change the color of robot robot1 from color white to color black, navigate robot robot1 from tile tile_7 to tile tile_8 to the right, use robot robot1 to paint the tile tile_11 above the tile tile_8 with the color black, move robot robot1 down from tile tile_8 to tile tile_5, navigate robot robot1 from tile tile_5 to tile tile_6 to the right, use robot robot1 to paint the tile tile_9 above the tile tile_6 with the color black, move robot robot1 down from tile tile_6 to tile tile_3, change the color of robot robot1 from color black to color white, use robot robot1 to paint the tile tile_6 above the tile tile_3 with the color white, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, move the robot robot1 from the tile tile_3 to the tile on its left tile_2, move the robot robot1 from tile tile_2 to tile tile_5 going upwards, use robot robot1 to paint the tile tile_8 above the tile tile_5 with the color white, change the color of robot robot2 from color black to color white, change the color of robot robot1 from color white to color black, move the robot robot1 from the tile tile_5 to the tile on its left tile_4, use robot robot1 to paint the tile tile_7 above the tile tile_4 with the color black, navigate robot robot1 from tile tile_4 to tile tile_5 to the right, use robot robot2 to paint the tile tile_4 above the tile tile_1 with the color white, move robot robot1 down from tile tile_5 to tile tile_2, use robot robot1 to paint the tile tile_5 above the tile tile_2 with the color black, navigate robot robot1 from tile tile_2 to tile tile_3 to the right\"; can the following action be removed from this plan and still have a valid plan: change the color of robot robot1 from color white to color black?", "answer": "no"}
{"id": 5787312544848681243, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 9 tiles and 2 robots. The tiles locations are: tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, and tile_8 is to the right of tile_7. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_5 is down from tile_8, tile_4 is down from tile_7, tile_2 is down from tile_5, and tile_1 is down from tile_4 Currently, robot robot2 is at tile_6 and holding color black and robot robot1 is at tile_8 and holding color white; tile_7, tile_9, tile_3, tile_2, tile_5, tile_1, and tile_4 are clear. The goal is to reach a state where the following facts hold: Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_9 is painted in black color, Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, and Tile tile_8 is painted in white color.", "question": "Given the plan: \"paint the tile tile_9 above the tile tile_6 with color black using the robot robot2, move robot robot2 down from tile tile_6 to tile tile_3, modify the color of the robot robot1 from white to black, navigate robot robot1 from tile tile_8 to tile tile_7 to its left, move robot robot1 down from tile tile_7 to tile tile_4, paint the tile tile_7 above the tile tile_4 with color black using the robot robot1, move robot robot1 down from tile tile_4 to tile tile_1, modify the color of the robot robot1 from black to white, paint the tile tile_4 above the tile tile_1 with color white using the robot robot1, move the robot robot1 from the tile tile_1 to the tile tile_2 which is to the right of the tile tile_1, move the robot robot1 up from tile tile_2 to tile tile_5, modify the color of the robot robot2 from black to white, paint the tile tile_6 above the tile tile_3 with color white using the robot robot2, paint the tile tile_8 above the tile tile_5 with color white using the robot robot1, modify the color of the robot robot1 from white to black, move robot robot1 down from tile tile_5 to tile tile_2, move the robot robot1 up from tile tile_2 to tile tile_5, move robot robot1 down from tile tile_5 to tile tile_2, paint the tile tile_5 above the tile tile_2 with color black using the robot robot1\"; can the following pair of consecutive actions be removed from this plan and still have a valid plan: move robot robot1 down from tile tile_5 to tile tile_2 and move the robot robot1 up from tile tile_2 to tile tile_5?", "answer": "yes"}
{"id": -3627071012480285151, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 16 tiles and 2 robots. The tiles locations are: tile_10 is to the right of tile_9, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_4 is to the right of tile_3, tile_14 is to the right of tile_13, tile_16 is to the right of tile_15, tile_6 is to the right of tile_5, tile_7 is to the right of tile_6, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_11 is to the right of tile_10. Further, tile_1 is down from tile_5, tile_2 is down from tile_6, tile_10 is down from tile_14, tile_4 is down from tile_8, tile_6 is down from tile_10, tile_3 is down from tile_7, tile_7 is down from tile_11, tile_11 is down from tile_15, tile_9 is down from tile_13, tile_5 is down from tile_9, tile_12 is down from tile_16, and tile_8 is down from tile_12 Currently, robot robot2 is at tile_14 and holding color black and robot robot1 is at tile_8 and holding color white; tile_13, tile_7, tile_9, tile_15, tile_3, tile_2, tile_16, tile_5, tile_11, tile_12, tile_6, tile_1, tile_4, and tile_10 are clear. The goal is to reach a state where the following facts hold: Tile tile_8 is painted in black color, Tile tile_5 is painted in white color, Tile tile_12 is painted in white color, Tile tile_6 is painted in black color, Tile tile_7 is painted in white color, Tile tile_10 is painted in white color, Tile tile_15 is painted in white color, Tile tile_13 is painted in white color, Tile tile_9 is painted in black color, Tile tile_16 is painted in black color, Tile tile_14 is painted in black color, and Tile tile_11 is painted in black color.", "question": "Given the plan: \"move robot robot1 down from tile tile_8 to tile tile_4, move robot robot2 down from tile tile_14 to tile tile_10, apply color black to tile tile_14 above tile tile_10 using robot robot2, navigate robot robot1 from tile tile_4 to tile tile_3 to its left, alter the color of the robot robot2 from color black to color white, navigate robot robot2 from tile tile_10 to tile tile_9 to its left, apply color white to tile tile_13 above tile tile_9 using robot robot2, move robot robot2 down from tile tile_9 to tile tile_5, move robot robot2 down from tile tile_5 to tile tile_1, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, move robot robot1 up from tile tile_3 to tile tile_7, move robot robot1 up from tile tile_7 to tile tile_11, apply color white to tile tile_15 above tile tile_11 using robot robot1, navigate robot robot1 from tile tile_11 to tile tile_12 to the right, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot1 from color white to color black, apply color black to tile tile_16 above tile tile_12 using robot robot1, alter the color of the robot robot1 from color black to color white, move robot robot1 down from tile tile_12 to tile tile_8, apply color white to tile tile_12 above tile tile_8 using robot robot1, alter the color of the robot robot1 from color white to color black, navigate robot robot2 from tile tile_2 to tile tile_1 to its left, alter the color of the robot robot2 from color black to color white, move robot robot1 down from tile tile_8 to tile tile_4, apply color black to tile tile_8 above tile tile_4 using robot robot1, navigate robot robot1 from tile tile_4 to tile tile_3 to its left, navigate robot robot1 from tile tile_3 to tile tile_2 to its left, move robot robot1 up from tile tile_2 to tile tile_6, navigate robot robot1 from tile tile_6 to tile tile_5 to its left, navigate robot robot2 from tile tile_1 to tile tile_2 to the right, navigate robot robot2 from tile tile_2 to tile tile_3 to the right, apply color black to tile tile_9 above tile tile_5 using robot robot1, alter the color of the robot robot1 from color black to color white, move robot robot1 down from tile tile_5 to tile tile_1, apply color white to tile tile_5 above tile tile_1 using robot robot1, navigate robot robot1 from tile tile_1 to tile tile_2 to the right, move robot robot1 up from tile tile_2 to tile tile_6, apply color white to tile tile_10 above tile tile_6 using robot robot1, alter the color of the robot robot1 from color white to color black, move robot robot1 down from tile tile_6 to tile tile_2, apply color black to tile tile_6 above tile tile_2 using robot robot1, alter the color of the robot robot2 from color white to color black, move robot robot2 up from tile tile_3 to tile tile_7, alter the color of the robot robot1 from color black to color white, navigate robot robot1 from tile tile_2 to tile tile_3 to the right, move robot robot2 up from tile tile_7 to tile tile_11, move robot robot2 down from tile tile_11 to tile tile_7, move robot robot2 up from tile tile_7 to tile tile_11, move robot robot2 down from tile tile_11 to tile tile_7, move robot robot2 up from tile tile_7 to tile tile_11, move robot robot2 down from tile tile_11 to tile tile_7, apply color black to tile tile_11 above tile tile_7 using robot robot2, navigate robot robot1 from tile tile_3 to tile tile_4 to the right, move robot robot2 down from tile tile_7 to tile tile_3, alter the color of the robot robot2 from color black to color white, apply color white to tile tile_7 above tile tile_3 using robot robot2\"; can the following action be removed from this plan and still have a valid plan: alter the color of the robot robot1 from color black to color white?", "answer": "yes"}
{"id": -5131405564931607295, "group": "action_justification_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 16 tiles and 2 robots. The tiles locations are: tile_10 is to the right of tile_9, tile_3 is to the right of tile_2, tile_15 is to the right of tile_14, tile_4 is to the right of tile_3, tile_14 is to the right of tile_13, tile_16 is to the right of tile_15, tile_6 is to the right of tile_5, tile_7 is to the right of tile_6, tile_2 is to the right of tile_1, tile_8 is to the right of tile_7, tile_12 is to the right of tile_11, and tile_11 is to the right of tile_10. Further, tile_1 is down from tile_5, tile_2 is down from tile_6, tile_10 is down from tile_14, tile_4 is down from tile_8, tile_6 is down from tile_10, tile_3 is down from tile_7, tile_7 is down from tile_11, tile_11 is down from tile_15, tile_9 is down from tile_13, tile_5 is down from tile_9, tile_12 is down from tile_16, and tile_8 is down from tile_12 Currently, robot robot2 is at tile_14 and holding color black and robot robot1 is at tile_8 and holding color white; tile_13, tile_7, tile_9, tile_15, tile_3, tile_2, tile_16, tile_5, tile_11, tile_12, tile_6, tile_1, tile_4, and tile_10 are clear. The goal is to reach a state where the following facts hold: Tile tile_8 is painted in black color, Tile tile_5 is painted in white color, Tile tile_12 is painted in white color, Tile tile_6 is painted in black color, Tile tile_7 is painted in white color, Tile tile_10 is painted in white color, Tile tile_15 is painted in white color, Tile tile_13 is painted in white color, Tile tile_9 is painted in black color, Tile tile_16 is painted in black color, Tile tile_14 is painted in black color, and Tile tile_11 is painted in black color.", "question": "Given the plan: \"move robot robot1 down from tile tile_8 to tile tile_4, move robot robot2 down from tile tile_14 to tile tile_10, paint the tile tile_14 above the tile tile_10 with color black using the robot robot2, move the robot robot1 from the tile tile_4 to the tile on its left tile_3, alter the color of the robot robot2 from color black to color white, move the robot robot2 from the tile tile_10 to the tile on its left tile_9, paint the tile tile_13 above the tile tile_9 with color white using the robot robot2, move robot robot2 down from tile tile_9 to tile tile_5, move robot robot2 down from tile tile_5 to tile tile_1, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot1 up from tile tile_3 to tile tile_7, move the robot robot1 up from tile tile_7 to tile tile_11, paint the tile tile_15 above the tile tile_11 with color white using the robot robot1, move the robot robot1 from tile tile_11 to the right tile tile_12, alter the color of the robot robot2 from color white to color black, alter the color of the robot robot1 from color white to color black, paint the tile tile_16 above the tile tile_12 with color black using the robot robot1, alter the color of the robot robot1 from color black to color white, move robot robot1 down from tile tile_12 to tile tile_8, paint the tile tile_12 above the tile tile_8 with color white using the robot robot1, alter the color of the robot robot1 from color white to color black, move the robot robot2 from the tile tile_2 to the tile on its left tile_1, alter the color of the robot robot2 from color black to color white, move robot robot1 down from tile tile_8 to tile tile_4, paint the tile tile_8 above the tile tile_4 with color black using the robot robot1, move the robot robot1 from the tile tile_4 to the tile on its left tile_3, move the robot robot1 from the tile tile_3 to the tile on its left tile_2, move the robot robot1 up from tile tile_2 to tile tile_6, move the robot robot1 from the tile tile_6 to the tile on its left tile_5, move the robot robot2 from tile tile_1 to the right tile tile_2, move the robot robot2 from tile tile_2 to the right tile tile_3, paint the tile tile_9 above the tile tile_5 with color black using the robot robot1, alter the color of the robot robot1 from color black to color white, move robot robot1 down from tile tile_5 to tile tile_1, paint the tile tile_5 above the tile tile_1 with color white using the robot robot1, move the robot robot1 from tile tile_1 to the right tile tile_2, move the robot robot1 up from tile tile_2 to tile tile_6, paint the tile tile_10 above the tile tile_6 with color white using the robot robot1, alter the color of the robot robot1 from color white to color black, move robot robot1 down from tile tile_6 to tile tile_2, paint the tile tile_6 above the tile tile_2 with color black using the robot robot1, move the robot robot2 from tile tile_3 to the right tile tile_4, move the robot robot1 from tile tile_2 to the right tile tile_3, move the robot robot1 up from tile tile_3 to tile tile_7, move the robot robot2 from the tile tile_4 to the tile on its left tile_3, move the robot robot1 up from tile tile_7 to tile tile_11, move robot robot1 down from tile tile_11 to tile tile_7, move the robot robot1 up from tile tile_7 to tile tile_11, move robot robot1 down from tile tile_11 to tile tile_7, paint the tile tile_11 above the tile tile_7 with color black using the robot robot1, move the robot robot2 from tile tile_3 to the right tile tile_4, alter the color of the robot robot1 from color black to color white, move robot robot1 down from tile tile_7 to tile tile_3, paint the tile tile_7 above the tile tile_3 with color white using the robot robot1\"; can the following action be removed from this plan and still have a valid plan: paint the tile tile_12 above the tile tile_8 with color white using the robot robot1?", "answer": "no"}
{"id": 8049239880103146702, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball3 is at room3, ball1 and ball2 are at room2, ball4 is at room4. The goal is to reach a state where the following facts hold: Ball ball4 is in room room1, Ball ball1 is in room room1, Ball ball3 is at room3 location, and Ball ball2 is in room room2.", "question": "Given the plan: \"move the robot robot1 from room room3 to room room2, move the robot robot1 from room room2 to room room4, grasp the object ball4 from room room4 with the left1 gripper of robot robot1, move the robot robot1 from room room4 to room room2, grasp the object ball1 from room room2 with the right1 gripper of robot robot1, move the robot robot1 from room room2 to room room1, place the object ball4 in the room room1 using the robot robot1 with left1 gripper, place the object ball1 in the room room1 using the robot robot1 with right1 gripper, move the robot robot1 from room room1 to room room4\"; can the following action be removed from this plan and still have a valid plan: move the robot robot1 from room room1 to room room4?", "answer": "yes"}
{"id": -47681752613971714, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball3 is at room3, ball1 and ball2 are at room2, ball4 is at room4. The goal is to reach a state where the following facts hold: Ball ball4 is in room room1, Ball ball1 is at room1 location, Ball ball3 is in room room3, and Ball ball2 is in room room2.", "question": "Given the plan: \"move the robot robot1 from room room3 to room room4, use the right1 gripper of robot robot1 to pick up the object ball4 from room room4, move the robot robot1 from room room4 to room room1, move the robot robot1 from room room1 to room room2, use the left1 gripper of robot robot1 to pick up the object ball1 from room room2, move the robot robot1 from room room2 to room room1, use the left1 gripper of robot robot1 to drop the object ball1 in room room1, use the right1 gripper of robot robot1 to drop the object ball4 in room room1, move the robot robot1 from room room1 to room room5\"; can the following action be removed from this plan and still have a valid plan: move the robot robot1 from room room4 to room room1?", "answer": "no"}
{"id": 5128857836047184399, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball4 and ball3 are at room2, ball1 and ball2 are at room1. The goal is to reach a state where the following facts hold: Ball ball4 is in room room1, Ball ball1 is in room room1, Ball ball2 is at room1 location, and Ball ball3 is at room1 location.", "question": "Given the plan: \"move robot robot1 from room room1 to room room2, pick up object ball4 with robot robot1 using left1 gripper from room room2, drop the object ball4 in the left1 gripper of the robot robot1 at the room room2, pick up object ball4 with robot robot1 using right1 gripper from room room2, pick up object ball3 with robot robot1 using left1 gripper from room room2, move robot robot1 from room room2 to room room1, drop the object ball4 in the right1 gripper of the robot robot1 at the room room1, pick up object ball4 with robot robot1 using right1 gripper from room room1, drop the object ball4 in the right1 gripper of the robot robot1 at the room room1, drop the object ball3 in the left1 gripper of the robot robot1 at the room room1\"; can the following action be removed from this plan and still have a valid plan: drop the object ball4 in the right1 gripper of the robot robot1 at the room room1?", "answer": "no"}
{"id": 6233806691835181069, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball4 and ball3 are at room2, ball1 and ball2 are at room1. The goal is to reach a state where the following facts hold: Ball ball4 is at room1 location, Ball ball1 is at room1 location, Ball ball2 is at room1 location, and Ball ball3 is at room1 location.", "question": "Given the plan: \"move the robot robot1 from room room1 to room room2, pick up the object ball3 with the robot robot1 using the left1 gripper from the room room2, pick up the object ball4 with the robot robot1 using the right1 gripper from the room room2, move the robot robot1 from room room2 to room room1, move the robot robot1 from room room1 to room room2, move the robot robot1 from room room2 to room room1, use robot robot1 with left1 gripper to place the object ball3 in room room1, use robot robot1 with right1 gripper to place the object ball4 in room room1\"; can the following action be removed from this plan and still have a valid plan: move the robot robot1 from room room2 to room room1?", "answer": "no"}
{"id": 7364705805325411321, "group": "action_justification_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2 and both grippers are free. Additionally, ball4 is at room3, ball3 and ball1 are at room2, ball2 is at room1. The goal is to reach a state where the following facts hold: Ball ball4 is in room room1, Ball ball3 is at room2 location, Ball ball1 is at room1 location, and Ball ball2 is in room room1.", "question": "Given the plan: \"pick up object ball1 with robot robot1 using left1 gripper from room room2, move robot robot1 from room room2 to room room3, pick up object ball4 with robot robot1 using right1 gripper from room room3, move robot robot1 from room room3 to room room2, move robot robot1 from room room2 to room room1, drop the object ball1 in the left1 gripper of the robot robot1 at the room room1, drop the object ball4 in the right1 gripper of the robot robot1 at the room room1, pick up object ball2 with robot robot1 using right1 gripper from room room1, drop the object ball2 in the right1 gripper of the robot robot1 at the room room1\"; can the following action be removed from this plan and still have a valid plan: drop the object ball2 in the right1 gripper of the robot robot1 at the room room1?", "answer": "no"}
{"id": -823323299480448130, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera2 can be calibrated on objective2. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera2 supports high_res. Camera camera1 supports high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective0 is visible from waypoint2. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, and Rock data was communicated from waypoint waypoint0;.", "question": "Given the plan: \"move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, calibrate the camera camera0 on the rover rover1 for the objective objective0 at waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint0, use the rover rover1 to collect soil samples at waypoint waypoint0 and store them in the store store1, transmit soil data from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, transmit image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint0 to the lander general at waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint0, calibrate the camera camera2 on the rover rover0 for the objective objective2 at waypoint waypoint0, sample a rock at waypoint waypoint0 using rover rover0, then store it in the storage unit store0, communicate the rock data from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0, transmit image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, empty the store store1 from rover rover1, move the rover rover1 from waypoint waypoint0 to waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: move the rover rover1 from waypoint waypoint0 to waypoint waypoint1?", "answer": "yes"}
{"id": 3434323998818943333, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera0 on board. Rover rover0 has camera1 on board. Camera camera0 can be calibrated on objective2. Camera camera1 can be calibrated on objective1. Camera camera1 supports low_res and high_res. Camera camera0 supports low_res. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Rover rover0 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint0 and waypoint2. Soil can be sampled at the following location(s): waypoint0 and waypoint2. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint2;, Image objective1 was communicated in mode high_res, Image objective0 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint2;.", "question": "Given the plan: \"sample a rock at waypoint waypoint2 using rover rover1, then store it in the storage unit store1, transmit the rock data from rover rover1 at waypoint waypoint2 to the lander general at waypoint waypoint1 through waypoint waypoint2, sample soil at waypoint waypoint2 with rover rover0 and store in the store store0, transmit soil data from rover rover0 at waypoint waypoint2 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint2, navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at the waypoint waypoint1, take an image of the objective objective1 in mode high_res using the camera camera1 on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at the waypoint waypoint1, take an image of the objective objective1 in mode low_res using the camera camera1 on the rover rover0 from the waypoint waypoint1, calibrate the camera camera1 on the rover rover0 for the objective objective1 at the waypoint waypoint1, take an image of the objective objective0 in mode low_res using the camera camera1 on the rover rover0 from the waypoint waypoint1, navigate with rover rover0 from waypoint waypoint1 to waypoint waypoint2, communicate the image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, communicate the image data of objective objective1 in mode low_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint1, navigate with rover rover0 from waypoint waypoint2 to waypoint waypoint0, empty the store store0 from rover rover0, sample soil at waypoint waypoint0 with rover rover0 and store in the store store0, transmit soil data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1, regarding the soil analysis of waypoint waypoint0, empty the store store0 from rover rover0, sample a rock at waypoint waypoint0 using rover rover0, then store it in the storage unit store0, transmit the rock data from rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 through waypoint waypoint0, navigate with rover rover1 from waypoint waypoint2 to waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: take an image of the objective objective1 in mode high_res using the camera camera1 on the rover rover0 from the waypoint waypoint1?", "answer": "no"}
{"id": -1094315416979394159, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera2 can be calibrated on objective2. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera2 supports high_res. Camera camera1 supports high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective0 is visible from waypoint2. Objective objective2 is visible from waypoint0. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Soil data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode high_res, and Rock data was communicated from waypoint waypoint0;.", "question": "Given the plan: \"move the rover rover1 from waypoint waypoint1 to waypoint waypoint2, calibrate camera camera1 on rover rover1 for objective objective0 at waypoint waypoint2, calibrate camera camera0 on rover rover1 for objective objective0 at waypoint waypoint2, move the rover rover1 from waypoint waypoint2 to waypoint waypoint1, move the rover rover1 from waypoint waypoint1 to waypoint waypoint0, capture an image of the objective objective1 in mode low_res with the camera camera0 on the rover rover1 at waypoint waypoint0, sample soil at waypoint waypoint0 with rover rover1 and store in store store1, communicate the soil data from rover rover1 at waypoint waypoint0 with the soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate image data of objective objective1 in mode low_res from rover rover1 at waypoint waypoint0 to lander general at waypoint waypoint1, move the rover rover0 from waypoint waypoint1 to waypoint waypoint0, collect a sample from the waypoint waypoint0 using the rover rover0 and store it in the store store0, communicate the rock data from the rover rover0 at waypoint waypoint0 to the lander general at waypoint waypoint1 via waypoint waypoint0, calibrate camera camera2 on rover rover0 for objective objective2 at waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0, communicate image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, capture an image of the objective objective1 in mode high_res with the camera camera1 on the rover rover1 at waypoint waypoint0\"; can the following action be removed from this plan and still have a valid plan: capture an image of the objective objective1 in mode high_res with the camera camera1 on the rover rover1 at waypoint waypoint0?", "answer": "yes"}
{"id": -4740218684046217943, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera1 supports low_res. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint1;, Image objective1 was communicated in mode colour, Rock data was communicated from waypoint waypoint1;, and Image objective0 was communicated in mode low_res.", "question": "Given the plan: \"calibrate the camera camera1 on rover rover0 for the objective objective0 at the waypoint waypoint0, take a picture of the objective objective0 in mode low_res using the camera camera1 mounted on the rover rover0 from the waypoint waypoint0, communicate the image data of objective objective0 in mode low_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint1, calibrate the camera camera2 on rover rover1 for the objective objective0 at the waypoint waypoint1, collect a rock sample from waypoint waypoint1 using rover rover1 and store it in store store1, empty the store store1 from rover rover1, sample soil at waypoint waypoint1 with rover rover1 and store in store store1, guide the rover rover1 from waypoint waypoint1 to waypoint waypoint2, take a picture of the objective objective1 in mode colour using the camera camera2 mounted on the rover rover1 from the waypoint waypoint2, communicate the image data of objective objective1 in mode colour from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint1, guide the rover rover1 from waypoint waypoint2 to waypoint waypoint0, communicate the soil data from rover rover1 at waypoint waypoint0 with the soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1, communicate rock data from rover rover1 at waypoint waypoint0 about waypoint waypoint1 to lander general at waypoint waypoint1, communicate the soil data from rover rover1 at waypoint waypoint0 with the soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1\"; can the following action be removed from this plan and still have a valid plan: sample soil at waypoint waypoint1 with rover rover1 and store in store store1?", "answer": "no"}
{"id": 3953279470711986231, "group": "action_justification_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera2 supports high_res. Camera camera1 supports high_res. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0, waypoint0 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint1: waypoint2 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0 and waypoint2. Soil can be sampled at the following location(s): waypoint0 and waypoint1. Rovers rover1 and rover0 are available. Store(s) store0 and store1 are empty. The goal is to reach a state where the following facts hold: Soil data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode high_res, Image objective1 was communicated in mode high_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Rock data was communicated from waypoint waypoint2;.", "question": "Given the plan: \"calibrate camera camera2 on rover rover0 for objective objective1 at waypoint waypoint1, navigate with rover rover0 to waypoint waypoint2 from waypoint waypoint1, collect a sample from the waypoint waypoint2 using the rover rover0 and store it in the store store0, capture an image of the objective objective0 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint2, navigate with rover rover0 to waypoint waypoint1 from waypoint waypoint2, navigate with rover rover0 to waypoint waypoint0 from waypoint waypoint1, calibrate camera camera2 on rover rover0 for objective objective1 at waypoint waypoint0, capture an image of the objective objective1 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint0, communicate image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, collect a soil sample from waypoint waypoint1 using rover rover1 and deposit it in the store store1, navigate with rover rover1 to waypoint waypoint2 from waypoint waypoint1, communicate soil data from rover rover1 at waypoint waypoint2 with soil analysis of waypoint waypoint1 to lander general at waypoint waypoint1, drop the content from store store0 of the rover rover0, communicate the rock data from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint2, communicate image data of objective objective0 in mode high_res from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1, collect a sample from the waypoint waypoint0 using the rover rover0 and store it in the store store0, drop the content from store store0 of the rover rover0, collect a soil sample from waypoint waypoint0 using rover rover0 and deposit it in the store store0, communicate soil data from rover rover0 at waypoint waypoint0 with soil analysis of waypoint waypoint0 to lander general at waypoint waypoint1, communicate the rock data from rover rover0 at waypoint waypoint0 to lander general at waypoint waypoint1 via waypoint waypoint0\"; can the following action be removed from this plan and still have a valid plan: capture an image of the objective objective0 in mode high_res with the camera camera2 on the rover rover0 at waypoint waypoint2?", "answer": "no"}
{"id": 8781957024954838143, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x0-y0. Currently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x3-y1 has been visited.", "question": "Given the plan: \"navigate from loc-x3-y3 to loc-x2-y3, navigate from loc-x2-y3 to loc-x3-y3, navigate from loc-x3-y3 to loc-x3-y2, navigate from loc-x3-y2 to loc-x3-y1, navigate from loc-x3-y1 to loc-x3-y0, navigate from loc-x3-y0 to loc-x2-y0, navigate from loc-x2-y0 to loc-x1-y0, navigate from loc-x1-y0 to loc-x1-y1, navigate from loc-x1-y1 to loc-x0-y1, navigate from loc-x0-y1 to loc-x0-y2, navigate from loc-x0-y2 to loc-x0-y3, navigate from loc-x0-y3 to loc-x1-y3, navigate from loc-x1-y3 to loc-x1-y2, navigate from loc-x1-y2 to loc-x2-y2, navigate from loc-x2-y2 to loc-x2-y1\"; can the following action be removed from this plan and still have a valid plan: navigate from loc-x3-y3 to loc-x3-y2?", "answer": "no"}
{"id": -7058996813788868725, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3 and loc-x0-y0. Currently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x3-y1 has been visited.", "question": "Given the plan: \"travel from loc-x3-y3 to loc-x2-y3, travel from loc-x2-y3 to loc-x1-y3, travel from loc-x1-y3 to loc-x1-y2, travel from loc-x1-y2 to loc-x2-y2, travel from loc-x2-y2 to loc-x3-y2, travel from loc-x3-y2 to loc-x2-y2, travel from loc-x2-y2 to loc-x2-y1, travel from loc-x2-y1 to loc-x3-y1, travel from loc-x3-y1 to loc-x3-y0, travel from loc-x3-y0 to loc-x2-y0, travel from loc-x2-y0 to loc-x1-y0, travel from loc-x1-y0 to loc-x1-y1, travel from loc-x1-y1 to loc-x1-y2, travel from loc-x1-y2 to loc-x0-y2, travel from loc-x0-y2 to loc-x0-y1\"; can the following action be removed from this plan and still have a valid plan: travel from loc-x2-y3 to loc-x1-y3?", "answer": "no"}
{"id": -5976098153454642118, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x0-y0. Currently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x3-y1 has been visited.", "question": "Given the plan: \"move to place loc-x3-y2 from place loc-x3-y3, move to place loc-x3-y1 from place loc-x3-y2, move to place loc-x3-y0 from place loc-x3-y1, move to place loc-x2-y0 from place loc-x3-y0, move to place loc-x1-y0 from place loc-x2-y0, move to place loc-x1-y1 from place loc-x1-y0, move to place loc-x2-y1 from place loc-x1-y1, move to place loc-x1-y1 from place loc-x2-y1, move to place loc-x0-y1 from place loc-x1-y1, move to place loc-x0-y2 from place loc-x0-y1, move to place loc-x0-y3 from place loc-x0-y2, move to place loc-x1-y3 from place loc-x0-y3, move to place loc-x2-y3 from place loc-x1-y3, move to place loc-x2-y2 from place loc-x2-y3, move to place loc-x1-y2 from place loc-x2-y2\"; can the following action be removed from this plan and still have a valid plan: move to place loc-x1-y0 from place loc-x2-y0?", "answer": "no"}
{"id": 570687605183040984, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x0-y3.Place loc-x0-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y3 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, Place loc-x3-y1 has been visited, and Place loc-x0-y0 has been visited.", "question": "Given the plan: \"travel from loc-x0-y3 to loc-x0-y2, travel from loc-x0-y2 to loc-x0-y1, travel from loc-x0-y1 to loc-x0-y0, travel from loc-x0-y0 to loc-x1-y0, travel from loc-x1-y0 to loc-x1-y1, travel from loc-x1-y1 to loc-x2-y1, travel from loc-x2-y1 to loc-x2-y0, travel from loc-x2-y0 to loc-x3-y0, travel from loc-x3-y0 to loc-x3-y1, travel from loc-x3-y1 to loc-x3-y2, travel from loc-x3-y2 to loc-x3-y3, travel from loc-x3-y3 to loc-x2-y3, travel from loc-x2-y3 to loc-x2-y2, travel from loc-x2-y2 to loc-x1-y2, travel from loc-x1-y2 to loc-x1-y3\"; can the following action be removed from this plan and still have a valid plan: travel from loc-x0-y2 to loc-x0-y1?", "answer": "no"}
{"id": 3692863935243343437, "group": "action_justification_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3 and loc-x0-y0. Currently, the robot is in place loc-x3-y3.Place loc-x3-y3 has been visited. The goal is to reach a state where the following facts hold: Place loc-x2-y2 has been visited, Place loc-x2-y3 has been visited, Place loc-x1-y0 has been visited, Place loc-x3-y2 has been visited, Place loc-x2-y1 has been visited, Place loc-x1-y1 has been visited, Place loc-x0-y2 has been visited, Place loc-x3-y0 has been visited, Place loc-x1-y2 has been visited, Place loc-x3-y3 has been visited, Place loc-x2-y0 has been visited, Place loc-x1-y3 has been visited, Place loc-x0-y1 has been visited, and Place loc-x3-y1 has been visited.", "question": "Given the plan: \"move from place loc-x3-y3 to place loc-x3-y2, move from place loc-x3-y2 to place loc-x3-y1, move from place loc-x3-y1 to place loc-x2-y1, move from place loc-x2-y1 to place loc-x2-y0, move from place loc-x2-y0 to place loc-x3-y0, move from place loc-x3-y0 to place loc-x2-y0, move from place loc-x2-y0 to place loc-x1-y0, move from place loc-x1-y0 to place loc-x1-y1, move from place loc-x1-y1 to place loc-x0-y1, move from place loc-x0-y1 to place loc-x0-y2, move from place loc-x0-y2 to place loc-x1-y2, move from place loc-x1-y2 to place loc-x1-y3, move from place loc-x1-y3 to place loc-x2-y3, move from place loc-x2-y3 to place loc-x2-y2, move from place loc-x2-y2 to place loc-x3-y2\"; can the following action be removed from this plan and still have a valid plan: move from place loc-x2-y2 to place loc-x3-y2?", "answer": "yes"}
