{"id": 2356866965899380801, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c21 on board. The cars are at locations as follows: c26, c40, c39, c42, c47, c10, c43, c46, c37, c49, c4, c31, c34, c45, c41, c27, c32, c24, c15, c28, c48, c5, c7, c25, c22, c16, c36, and c1 are at l1; c18, c33, c29, c12, c14, c19, c35, c0, c11, c8, c2, c3, c30, c9, c38, c6, c23, c13, c20, c44, and c17 are at l0.", "question": "Is it possible to transition to a state where the action \"debark the car c0 to location l1 from the ferry\" can be applied?", "answer": "yes"}
{"id": 7698814873333889767, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c16 on board. The cars are at locations as follows: c26, c40, c39, c42, c47, c10, c43, c46, c37, c49, c4, c31, c34, c45, c41, c27, c32, c24, c15, c21, c28, c48, c5, c7, c25, c22, c36, and c1 are at l1; c18, c33, c29, c12, c14, c19, c35, c0, c11, c8, c2, c3, c30, c9, c38, c6, c23, c13, c20, c44, and c17 are at l0.", "question": "Is it possible to transition to a state where the action \"debark the car c21 to location l0 from the ferry\" can be applied?", "answer": "yes"}
{"id": -4558179676875521210, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l3, with the car c6 on board. The cars are at locations as follows: c39, c3, c40, c21, c35, and c26 are at l2; c34, c31, c23, c22, c37, c5, c20, c42, c43, c7, c12, c13, c49, and c25 are at l3; c30, c29, c14, c28, c47, c38, c36, c41, c27, c0, c8, and c45 are at l4; c18, c4, c24, c9, c10, c1, c16, and c17 are at l0; c2, c19, c46, c33, c32, c15, c11, c48, and c44 are at l1.", "question": "Is it possible to transition to a state where the action \"debark car c6 to location l1 from the ferry\" can be applied?", "answer": "yes"}
{"id": 9124159555422587712, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c5, c6, c3, and c0 are at l2; c9, c2, and c7 are at l0; c8, c1, and c4 are at l1.", "question": "Is it possible to transition to a state where the action \"embark the car l2 at location c8 on to the ferry\" can be applied?", "answer": "no"}
{"id": -8595330654831203901, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0, with the car c0 on board. The cars are at locations as follows: c2 and c1 are at l1.", "question": "Is it possible to transition to a state where the action \"debark the car c0 to location l0 from the airplane\" can be applied?", "answer": "no"}
{"id": -8308441047776414098, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l3 location and it is empty. The cars are at locations as follows: c2 and c1 are at l3; c0 is at l0.", "question": "Is it possible to transition to a state where the action \"travel by sea from location l2 to location l1\" can be applied?", "answer": "yes"}
{"id": 1960596875631641725, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1, with the car c2 on board. The cars are at locations as follows: c1 is at l3; c0 is at l1.", "question": "Is it possible to transition to a state where the action \"unload the car c2 from the ferry to location l4\" can be applied?", "answer": "yes"}
{"id": -7437794737911834133, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c36 on board. The cars are at locations as follows: c39, c3, c40, c21, c35, c26, and c22 are at l2; c34, c31, c23, c37, c5, c20, c42, c43, c7, c12, c6, c13, c49, and c25 are at l3; c30, c29, c14, c28, c47, c38, c41, c9, c27, c0, c8, and c45 are at l4; c18, c4, c24, c10, c1, c44, c16, and c17 are at l0; c2, c19, c46, c33, c32, c15, c11, and c48 are at l1.", "question": "Is it possible to transition to a state where the action \"fly from location l4 to location l1\" can be applied?", "answer": "no"}
{"id": -7164236778301594118, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c0 is at l4; c2 is at l0; c1 is at l3.", "question": "Is it possible to transition to a state where the action \"debark the car c0 from the ferry to location c1\" can be applied?", "answer": "no"}
{"id": 5808532745636152861, "group": "reachable_action_bool", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c40 on board. The cars are at locations as follows: c29, c12, c26, c14, c25, c19, c35, c0, c11, c8, c2, c22, c30, c9, c38, c6, c21, c13, c36, c20, and c44 are at l0; c23, c39, c42, c47, c10, c18, c43, c3, c46, c37, c33, c49, c4, c31, c34, c45, c41, c27, c32, c24, c15, c28, c48, c5, c7, c16, c17, and c1 are at l1.", "question": "Is it possible to transition to a state where the action \"drive from location l0 to location l1\" can be applied?", "answer": "no"}
{"id": 1991549721694001435, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l2-1, l2-0, and l2-2 are in c2; l1-2, l1-0, and l1-1 are in c1. Currently, t0 is at l0-1, a0 and t2 are at l2-0, p4 and t1 are at l1-0, p1, p3, and p0 are in a0, p2 is in t0.", "question": "Is it possible to transition to a state where the action \"place the object t2 into the truck l0-1 at location c2\" can be applied?", "answer": "no"}
{"id": -2724827960285167407, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-0, and l1-1 are in c1. Currently, p0 is at l0-1, p3, t0, and p1 are at l0-2, t1 is at l1-2, a0 is at l0-0, p2 is in t0.", "question": "Is it possible to transition to a state where the action \"unload the object l0-0 from the truck t0 at location c0\" can be applied?", "answer": "no"}
{"id": 5540851196970732681, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, t0 is at l0-1, p1 and a0 are at l1-0, p2 is at l0-0, t1 is at l1-1, p0 is in t1, p3 is in a0.", "question": "Is it possible to transition to a state where the action \"load object p2 into truck t0 at location l0-0\" can be applied?", "answer": "yes"}
{"id": 2566074982906870688, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-0, and l1-1 are in c1. Currently, p3 is at l0-2, t0 and p2 are at l0-1, a0 is at l1-0, t1 is at l1-2, p0 and p1 are in t0.", "question": "Is it possible to transition to a state where the action \"fly the airplane l1-1 from the airport p3 to the airport l1-2\" can be applied?", "answer": "no"}
{"id": 3565936110601348422, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-0 and l1-1 are in c1. Currently, a0 and t1 are at l1-0, t0 is at l0-0, p2, p0, and p3 are in t1, p1 is in a0.", "question": "Is it possible to transition to a state where the action \"load the object c0 from location t1 into the airplane p0\" can be applied?", "answer": "no"}
{"id": -9182711933972997217, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l2-1, l2-0, and l2-2 are in c2; l1-2, l1-0, and l1-1 are in c1. Currently, p1, a0, and p3 are at l2-0, t0 is at l0-1, p0 and t1 are at l1-2, t2 is at l2-1, p4 is in a0, p2 is in t0.", "question": "Is it possible to transition to a state where the action \"drive the truck t2 in city c2 from location l2-2 to location l2-0\" can be applied?", "answer": "yes"}
{"id": -5074069474542963394, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. The locations are in cities as follows: l0-2, l0-1, and l0-0 are in c0; l1-2, l1-0, and l1-1 are in c1. Currently, p0 and t0 are at l0-1, p3 and p1 are at l0-2, a0 and t1 are at l1-0, p2 is in t1.", "question": "Is it possible to transition to a state where the action \"unstack p2 from crate l1-0 into the truck t1\" can be applied?", "answer": "no"}
{"id": 2497787696614096034, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l3-4, l3-7, l3-3, l3-2, l3-0, l3-8, l3-6, l3-1, l3-9, and l3-5 are in c3; l0-2, l0-9, l0-1, l0-6, l0-8, l0-0, l0-4, l0-5, l0-7, and l0-3 are in c0; l2-2, l2-7, l2-0, l2-3, l2-9, l2-4, l2-1, l2-6, l2-8, and l2-5 are in c2; l4-5, l4-2, l4-7, l4-9, l4-8, l4-0, l4-6, l4-4, l4-3, and l4-1 are in c4; l1-8, l1-7, l1-3, l1-2, l1-0, l1-6, l1-4, l1-5, l1-1, and l1-9 are in c1. Currently, t2 is at l2-9, t0 is at l0-2, t4 is at l4-2, a0 is at l2-0, t1 is at l1-0, t3 is at l3-0, p2 is at l4-8, p0 and p3 are in t2, p1 is in a0.", "question": "Is it possible to transition to a state where the action \"navigate the truck t1 from location l1-5 in city c1 to location l1-2 in the same city\" can be applied?", "answer": "yes"}
{"id": -4980077240752893964, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-0, l4-2, and l4-1 are in c4; l3-1, l3-0, and l3-2 are in c3; l0-2, l0-1, and l0-0 are in c0; l2-1, l2-0, and l2-2 are in c2; l1-2, l1-0, and l1-1 are in c1. Currently, p3 is at l4-1, t0 is at l0-2, a0 and t3 are at l3-0, t4 is at l4-0, t2 is at l2-0, t1 is at l1-2, p2 is in t2, p1 is in t3, p0 is in a0.", "question": "Is it possible to transition to a state where the action \"remove the object c1 from the truck t3 and place it on the location l1-2\" can be applied?", "answer": "no"}
{"id": -928897422565075377, "group": "reachable_action_bool", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l4-0, l4-2, and l4-1 are in c4; l3-1, l3-0, and l3-2 are in c3; l0-2, l0-1, and l0-0 are in c0; l2-1, l2-0, and l2-2 are in c2; l1-2, l1-0, and l1-1 are in c1. Currently, p3 is at l4-1, p2 is at l2-2, t0 is at l0-2, a0 and t2 are at l2-0, t3 is at l3-0, t4 is at l4-0, t1 is at l1-2, p1 is in t3, p0 is in a0.", "question": "Is it possible to transition to a state where the action \"sail the ship t1 into city c1 from location l1-2 in city l1-1\" can be applied?", "answer": "no"}
{"id": 2642877709553038988, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_3 and block_5. The following block(s) are stacked on top of another block: block_1 is on block_3 and block_2 is on block_5.", "question": "Is it possible to transition to a state where the action \"stack object block_1 on top of object block_4\" can be applied?", "answer": "yes"}
{"id": -8843862381011450481, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_2, block_1, and block_4. The following block(s) is stacked on top of another block: block_5 is on block_1.", "question": "Is it possible to transition to a state where the action \"unstack object block_1 from object block_1\" can be applied?", "answer": "no"}
{"id": 8671269818463951114, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_2 and block_1. The following block(s) are stacked on top of another block: block_5 is on block_3 and block_3 is on block_1.", "question": "Is it possible to transition to a state where the action \"place the object block_2 on top of the object block_4\" can be applied?", "answer": "yes"}
{"id": 6147226057332730055, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_2, block_1, block_4, and block_5. The following block(s) is stacked on top of another block: block_3 is on block_1.", "question": "Is it possible to transition to a state where the action \"place the object block_4 on top of the object block_3\" can be applied?", "answer": "yes"}
{"id": -1502113976886974126, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_14. The following block(s) are on the table: block_6, block_10, block_1, block_20, block_16, block_5, block_18, and block_7. The following block(s) are stacked on top of another block: block_8 is on block_7, block_12 is on block_6, block_15 is on block_9, block_3 is on block_13, block_4 is on block_11, block_13 is on block_4, block_9 is on block_2, block_19 is on block_16, block_17 is on block_12, block_2 is on block_18, and block_11 is on block_1.", "question": "Is it possible to transition to a state where the action \"pick up object block_10 from the table\" can be applied?", "answer": "yes"}
{"id": 3771720756564434423, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_6, block_10, block_19, block_1, block_9, block_18, block_14, and block_7. The following block(s) are stacked on top of another block: block_20 is on block_8, block_8 is on block_7, block_15 is on block_9, block_3 is on block_13, block_12 is on block_2, block_4 is on block_11, block_13 is on block_4, block_16 is on block_20, block_5 is on block_14, block_17 is on block_12, block_2 is on block_18, and block_11 is on block_1.", "question": "Is it possible to transition to a state where the action \"stack object block_5 on top of object block_17\" can be applied?", "answer": "yes"}
{"id": 1190867249158433837, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_2 and block_1.", "question": "Is it possible to transition to a state where the action \"stack object block_1 on top of object block_3\" can be applied?", "answer": "yes"}
{"id": -4026253485069427142, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_12, block_6, block_10, block_19, block_1, block_18, block_14, and block_7. The following block(s) are stacked on top of another block: block_3 is on block_2, block_20 is on block_8, block_8 is on block_7, block_15 is on block_16, block_4 is on block_11, block_13 is on block_4, block_16 is on block_20, block_5 is on block_14, block_9 is on block_15, block_17 is on block_12, block_2 is on block_18, and block_11 is on block_1.", "question": "Is it possible to transition to a state where the action \"stack the object block_3 on top of the object block_3\" can be applied?", "answer": "no"}
{"id": -4609158751711069255, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_12, block_6, block_19, block_1, block_18, block_14, and block_7. The following block(s) are stacked on top of another block: block_20 is on block_8, block_8 is on block_7, block_10 is on block_19, block_15 is on block_16, block_4 is on block_11, block_3 is on block_6, block_13 is on block_4, block_16 is on block_20, block_5 is on block_14, block_9 is on block_15, block_17 is on block_12, block_2 is on block_18, and block_11 is on block_1.", "question": "Is it possible to transition to a state where the action \"place the object block_2 on top of the object block_4\" can be applied?", "answer": "yes"}
{"id": 4710328513831901010, "group": "reachable_action_bool", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_3 is on block_5 and block_2 is on block_1.", "question": "Is it possible to transition to a state where the action \"acquire the key block_4 from the place block_3\" can be applied?", "answer": "no"}
{"id": 4495088736228089217, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f3-2f and is holding key0-0. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-4f has shape0 shaped lock, f4-0f has shape0 shaped lock. Key key0-1 is at position f1-2f. Key key0-2 is at position f1-4f.", "question": "Is it possible to transition to a state where the action \"pick up the key key0-2 at the current position place f4-2f and loose the key key0-2 being held\" can be applied?", "answer": "no"}
{"id": -3652889119381205262, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-2f and is holding key0-0. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-1 is at position f2-2f.", "question": "Is it possible to transition to a state where the action \"transition from the current position f1-4f to the next position f0-4f\" can be applied?", "answer": "yes"}
{"id": 8447014087913446853, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-3f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-2 is at position f0-4f. Key key0-0 is at position f3-3f. Key key0-1 is at position f1-2f.", "question": "Is it possible to transition to a state where the action \"travel from the current position f0-3f to the next position f0-4f\" can be applied?", "answer": "yes"}
{"id": 6087420608423366634, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f3-3f and is holding key1-1. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-0f.", "question": "Is it possible to transition to a state where the action \"pick up the key key1-1 at the current position place f2-2f and loose the key key1-1 being held\" can be applied?", "answer": "no"}
{"id": -1152859031210628295, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-3 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-4f and is holding key0-1. All the positions are open except the following: f0-3f has shape0 shaped lock, f0-2f has shape0 shaped lock. Key key0-3 is at position f0-1f. Key key0-0 is at position f0-1f. Key key0-2 is at position f4-0f.", "question": "Is it possible to transition to a state where the action \"place the key key0-1 at the current position place f2-3f\" can be applied?", "answer": "yes"}
{"id": -3255777502980333296, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f2-4f and is holding key1-1. All the positions are open except the following: . Key key1-0 is at position f1-0f.", "question": "Is it possible to transition to a state where the action \"travel from the current position f1-3f to the next position f1-2f\" can be applied?", "answer": "yes"}
{"id": -1221257497462132960, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f4-4f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-0 is at position f3-3f. Key key0-1 is at position f1-2f. Key key0-2 is at position f3-4f.", "question": "Is it possible to transition to a state where the action \"destroy the key key0-2 at the current position f3-3f\" can be applied?", "answer": "no"}
{"id": -7217612928648648, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-3 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f0-1f and is holding key0-3. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock, f0-2f has shape0 shaped lock. Key key0-0 is at position f0-0f. Key key0-2 is at position f3-0f. Key key0-1 is at position f4-2f.", "question": "Is it possible to transition to a state where the action \"place the key key0-3 at the current position place f1-0f\" can be applied?", "answer": "yes"}
{"id": -3841135662995941881, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-2 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f1-1f and is holding key0-1. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-2 is at position f3-3f. Key key0-0 is at position f1-3f.", "question": "Is it possible to transition to a state where the action \"put down the key key0-1 at the current position f0-2f\" can be applied?", "answer": "yes"}
{"id": 8142048986121093941, "group": "reachable_action_bool", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  Currently, the robot is at position f2-0f and is holding key0-0. All the positions are open except the following: f2-1f has shape0 shaped lock, f0-1f has shape0 shaped lock. Key key0-1 is at position f4-1f.", "question": "Is it possible to transition to a state where the action \"pick up key f1-2f at current position place f2-3f and loose key shape0 being held\" can be applied?", "answer": "no"}
{"id": 7992871375413117481, "group": "reachable_action_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_8 is to the right of tile_7, tile_17 is to the right of tile_16, tile_3 is to the right of tile_2, tile_14 is to the right of tile_13, tile_18 is to the right of tile_17, tile_7 is to the right of tile_6, tile_20 is to the right of tile_19, tile_19 is to the right of tile_18, tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, tile_13 is to the right of tile_12, tile_2 is to the right of tile_1, tile_4 is to the right of tile_3, and tile_15 is to the right of tile_14. Further, tile_9 is down from tile_14, tile_14 is down from tile_19, tile_3 is down from tile_8, tile_7 is down from tile_12, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_11 is down from tile_16, tile_13 is down from tile_18, tile_5 is down from tile_10, tile_10 is down from tile_15, tile_1 is down from tile_6, tile_15 is down from tile_20, tile_8 is down from tile_13, tile_2 is down from tile_7, and tile_4 is down from tile_9 Currently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_4 and holding color white; tile_12, tile_7, tile_9, tile_5, tile_14, tile_3, tile_19, tile_17, and tile_1 are clear; tile_16 is painted white, tile_15 is painted black, tile_20 is painted white, tile_11 is painted black, tile_18 is painted white, tile_6 is painted white, tile_10 is painted white, tile_8 is painted white, and tile_13 is painted black.", "question": "Is it possible to transition to a state where the action \"move package robot1 up from tile tile_4 to tile tile_9\" can be applied?", "answer": "no"}
{"id": 3212795049275590825, "group": "reachable_action_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_8 is to the right of tile_7, tile_17 is to the right of tile_16, tile_3 is to the right of tile_2, tile_14 is to the right of tile_13, tile_18 is to the right of tile_17, tile_7 is to the right of tile_6, tile_20 is to the right of tile_19, tile_19 is to the right of tile_18, tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, tile_13 is to the right of tile_12, tile_2 is to the right of tile_1, tile_4 is to the right of tile_3, and tile_15 is to the right of tile_14. Further, tile_9 is down from tile_14, tile_14 is down from tile_19, tile_3 is down from tile_8, tile_7 is down from tile_12, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_11 is down from tile_16, tile_13 is down from tile_18, tile_5 is down from tile_10, tile_10 is down from tile_15, tile_1 is down from tile_6, tile_15 is down from tile_20, tile_8 is down from tile_13, tile_2 is down from tile_7, and tile_4 is down from tile_9 Currently, robot robot1 is at tile_3 and holding color black and robot robot2 is at tile_4 and holding color white; tile_2, tile_9, tile_5, tile_14, and tile_1 are clear; tile_12 is painted white, tile_16 is painted white, tile_13 is painted black, tile_15 is painted black, tile_20 is painted white, tile_7 is painted black, tile_19 is painted black, tile_11 is painted black, tile_18 is painted white, tile_6 is painted white, tile_10 is painted white, tile_8 is painted white, and tile_17 is painted black.", "question": "Is it possible to transition to a state where the action \"move the crate robot2 from the tile tile_5 to the tile on its left tile_4\" can be applied?", "answer": "no"}
{"id": 1212449806650805701, "group": "reachable_action_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 3 robots and 20 tiles. The tiles locations are: tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_8 is to the right of tile_7, tile_17 is to the right of tile_16, tile_3 is to the right of tile_2, tile_14 is to the right of tile_13, tile_18 is to the right of tile_17, tile_7 is to the right of tile_6, tile_20 is to the right of tile_19, tile_19 is to the right of tile_18, tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, tile_13 is to the right of tile_12, tile_2 is to the right of tile_1, tile_4 is to the right of tile_3, and tile_15 is to the right of tile_14. Further, tile_9 is down from tile_14, tile_14 is down from tile_19, tile_3 is down from tile_8, tile_7 is down from tile_12, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_11 is down from tile_16, tile_13 is down from tile_18, tile_5 is down from tile_10, tile_10 is down from tile_15, tile_1 is down from tile_6, tile_15 is down from tile_20, tile_8 is down from tile_13, tile_2 is down from tile_7, and tile_4 is down from tile_9 Currently, robot robot2 is at tile_2 and holding color white, robot robot1 is at tile_4 and holding color black, and robot robot3 is at tile_5 and holding color white; tile_12, tile_7, tile_9, tile_15, tile_3, tile_1, and tile_10 are clear; tile_16 is painted white, tile_13 is painted black, tile_14 is painted white, tile_20 is painted white, tile_19 is painted black, tile_11 is painted black, tile_18 is painted white, tile_6 is painted white, tile_8 is painted white, and tile_17 is painted black.", "question": "Is it possible to transition to a state where the action \"move the crate robot1 from tile tile_4 to tile tile_9 going upwards\" can be applied?", "answer": "no"}
{"id": 6927532821179104007, "group": "reachable_action_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 24 tiles. The tiles locations are: tile_11 is to the right of tile_10, tile_16 is to the right of tile_15, tile_15 is to the right of tile_14, tile_17 is to the right of tile_16, tile_22 is to the right of tile_21, tile_23 is to the right of tile_22, tile_18 is to the right of tile_17, tile_9 is to the right of tile_8, tile_2 is to the right of tile_1, tile_12 is to the right of tile_11, tile_24 is to the right of tile_23, tile_10 is to the right of tile_9, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_14 is to the right of tile_13, tile_5 is to the right of tile_4, tile_21 is to the right of tile_20, tile_6 is to the right of tile_5, tile_20 is to the right of tile_19, and tile_4 is to the right of tile_3. Further, tile_5 is down from tile_11, tile_3 is down from tile_9, tile_11 is down from tile_17, tile_12 is down from tile_18, tile_2 is down from tile_8, tile_8 is down from tile_14, tile_13 is down from tile_19, tile_15 is down from tile_21, tile_17 is down from tile_23, tile_14 is down from tile_20, tile_16 is down from tile_22, tile_4 is down from tile_10, tile_18 is down from tile_24, tile_6 is down from tile_12, tile_9 is down from tile_15, tile_7 is down from tile_13, tile_1 is down from tile_7, and tile_10 is down from tile_16 Currently, robot robot2 is at tile_5 and holding color black and robot robot1 is at tile_6 and holding color black; tile_2, tile_16, tile_4, tile_11, tile_3, tile_1, and tile_10 are clear; tile_23 is painted white, tile_13 is painted black, tile_19 is painted white, tile_22 is painted black, tile_15 is painted black, tile_8 is painted black, tile_14 is painted white, tile_7 is painted white, tile_9 is painted white, tile_18 is painted white, tile_24 is painted black, tile_20 is painted black, tile_12 is painted black, tile_21 is painted white, and tile_17 is painted black.", "question": "Is it possible to transition to a state where the action \"use truck robot2 to load the tile tile_11 above the tile tile_5 with the color white\" can be applied?", "answer": "no"}
{"id": 5947167300639839129, "group": "reachable_action_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 24 tiles. The tiles locations are: tile_11 is to the right of tile_10, tile_16 is to the right of tile_15, tile_15 is to the right of tile_14, tile_17 is to the right of tile_16, tile_22 is to the right of tile_21, tile_23 is to the right of tile_22, tile_18 is to the right of tile_17, tile_9 is to the right of tile_8, tile_2 is to the right of tile_1, tile_12 is to the right of tile_11, tile_24 is to the right of tile_23, tile_10 is to the right of tile_9, tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_14 is to the right of tile_13, tile_5 is to the right of tile_4, tile_21 is to the right of tile_20, tile_6 is to the right of tile_5, tile_20 is to the right of tile_19, and tile_4 is to the right of tile_3. Further, tile_5 is down from tile_11, tile_3 is down from tile_9, tile_11 is down from tile_17, tile_12 is down from tile_18, tile_2 is down from tile_8, tile_8 is down from tile_14, tile_13 is down from tile_19, tile_15 is down from tile_21, tile_17 is down from tile_23, tile_14 is down from tile_20, tile_16 is down from tile_22, tile_4 is down from tile_10, tile_18 is down from tile_24, tile_6 is down from tile_12, tile_9 is down from tile_15, tile_7 is down from tile_13, tile_1 is down from tile_7, and tile_10 is down from tile_16 Currently, robot robot1 is at tile_12 and holding color white and robot robot2 is at tile_3 and holding color white; tile_2, tile_9, tile_15, tile_6, tile_5, tile_4, and tile_1 are clear; tile_16 is painted white, tile_23 is painted white, tile_13 is painted black, tile_19 is painted white, tile_22 is painted black, tile_8 is painted black, tile_14 is painted white, tile_7 is painted white, tile_11 is painted white, tile_18 is painted white, tile_24 is painted black, tile_20 is painted black, tile_10 is painted black, tile_21 is painted white, and tile_17 is painted black.", "question": "Is it possible to transition to a state where the action \"unload robot robot1 from tile tile_12 to tile tile_6\" can be applied?", "answer": "no"}
{"id": 7294377934420678482, "group": "reachable_action_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_11 is to the right of tile_10, tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, tile_12 is to the right of tile_11, and tile_6 is to the right of tile_5. Further, tile_6 is down from tile_9, tile_7 is down from tile_10, tile_5 is down from tile_8, tile_8 is down from tile_11, tile_4 is down from tile_7, tile_9 is down from tile_12, tile_3 is down from tile_6, tile_1 is down from tile_4, and tile_2 is down from tile_5 Currently, robot robot2 is at tile_6 and holding color black and robot robot1 is at tile_3 and holding color white; tile_2, tile_1, and tile_5 are clear; tile_9 is painted black, tile_10 is painted white, tile_12 is painted white, tile_7 is painted black, tile_11 is painted black, tile_8 is painted white, and tile_4 is painted white.", "question": "Is it possible to transition to a state where the action \"move package robot1 up from tile tile_3 to tile tile_6\" can be applied?", "answer": "no"}
{"id": -2426670103330823678, "group": "reachable_action_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_11 is to the right of tile_10, tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, tile_12 is to the right of tile_11, and tile_6 is to the right of tile_5. Further, tile_6 is down from tile_9, tile_7 is down from tile_10, tile_5 is down from tile_8, tile_8 is down from tile_11, tile_4 is down from tile_7, tile_9 is down from tile_12, tile_3 is down from tile_6, tile_1 is down from tile_4, and tile_2 is down from tile_5 Currently, robot robot1 is at tile_8 and holding color black and robot robot2 is at tile_2 and holding color black; tile_3, tile_7, tile_6, tile_1, tile_5, tile_4, and tile_9 are clear; tile_10 is painted white, tile_12 is painted white, and tile_11 is painted black.", "question": "Is it possible to transition to a state where the action \"move crate robot1 from tile tile_5 to the right tile tile tile_6\" can be applied?", "answer": "no"}
{"id": -5448745349897120238, "group": "reachable_action_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, and tile_6 is to the right of tile_5. Further, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_5 is down from tile_8, tile_4 is down from tile_7, tile_3 is down from tile_6, and tile_2 is down from tile_5 Currently, robot robot2 is at tile_4 and holding color black and robot robot1 is at tile_3 and holding color white; tile_2, tile_1, tile_5, and tile_8 are clear; tile_9 is painted black, tile_7 is painted black, and tile_6 is painted white.", "question": "Is it possible to transition to a state where the action \"apply color black to tile tile_5 above tile tile_2 using robot robot2\" can be applied?", "answer": "yes"}
{"id": 2095153708607896028, "group": "reachable_action_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 3 robots and 20 tiles. The tiles locations are: tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_8 is to the right of tile_7, tile_17 is to the right of tile_16, tile_3 is to the right of tile_2, tile_14 is to the right of tile_13, tile_18 is to the right of tile_17, tile_7 is to the right of tile_6, tile_20 is to the right of tile_19, tile_19 is to the right of tile_18, tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, tile_13 is to the right of tile_12, tile_2 is to the right of tile_1, tile_4 is to the right of tile_3, and tile_15 is to the right of tile_14. Further, tile_9 is down from tile_14, tile_14 is down from tile_19, tile_3 is down from tile_8, tile_7 is down from tile_12, tile_12 is down from tile_17, tile_6 is down from tile_11, tile_11 is down from tile_16, tile_13 is down from tile_18, tile_5 is down from tile_10, tile_10 is down from tile_15, tile_1 is down from tile_6, tile_15 is down from tile_20, tile_8 is down from tile_13, tile_2 is down from tile_7, and tile_4 is down from tile_9 Currently, robot robot1 is at tile_2 and holding color white, robot robot2 is at tile_5 and holding color white, and robot robot3 is at tile_13 and holding color black; tile_8, tile_7, tile_9, tile_6, tile_4, tile_11, tile_14, tile_3, and tile_1 are clear; tile_12 is painted white, tile_16 is painted white, tile_15 is painted black, tile_20 is painted white, tile_19 is painted black, tile_18 is painted white, tile_10 is painted white, and tile_17 is painted black.", "question": "Is it possible to transition to a state where the action \"move the robot robot1 from the tile tile_6 to the tile tile_1 going downwards\" can be applied?", "answer": "yes"}
{"id": -5322748262299235051, "group": "reachable_action_bool", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 12 tiles. The tiles locations are: tile_8 is to the right of tile_7, tile_3 is to the right of tile_2, tile_11 is to the right of tile_10, tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, tile_2 is to the right of tile_1, tile_12 is to the right of tile_11, and tile_6 is to the right of tile_5. Further, tile_6 is down from tile_9, tile_7 is down from tile_10, tile_5 is down from tile_8, tile_8 is down from tile_11, tile_4 is down from tile_7, tile_9 is down from tile_12, tile_3 is down from tile_6, tile_1 is down from tile_4, and tile_2 is down from tile_5 Currently, robot robot1 is at tile_8 and holding color white and robot robot2 is at tile_2 and holding color black; tile_3, tile_7, tile_6, tile_1, tile_5, tile_4, tile_9, and tile_11 are clear; tile_10 is painted white and tile_12 is painted white.", "question": "Is it possible to transition to a state where the action \"move robot robot1 down from tile tile_7 to tile tile_4\" can be applied?", "answer": "yes"}
{"id": -413082499312830855, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball3. Additionally, ball2 and ball4 are at room1.", "question": "Is it possible to transition to a state where the action \"use robot robot1 with gripper ball3 to place the object right1 in room room2\" can be applied?", "answer": "no"}
{"id": 3276849274604368659, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball5 and ball1 are at room3, ball3, ball2, ball7, and ball4 are at room1, ball6 is at room2.", "question": "Is it possible to transition to a state where the action \"pick up the object right1 with the robot robot1 using the left1 gripper from the room room3\" can be applied?", "answer": "no"}
{"id": -7341678854950350349, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball1, ball4, and ball2 are at room2, ball3 is at room1.", "question": "Is it possible to transition to a state where the action \"fly drone robot1 from room room1 to room room2\" can be applied?", "answer": "no"}
{"id": 5313842507172118773, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball3, and right gripper is carrying the ball ball2. Additionally, ball1 is at room2, ball4 is at room1.", "question": "Is it possible to transition to a state where the action \"use the right1 gripper of robot robot1 to drop the object ball1 in room room2\" can be applied?", "answer": "yes"}
{"id": 202439922878848245, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is free, and right gripper is carrying the ball ball6. Additionally, ball3, ball12, ball1, and ball13 are at room2, ball7 and ball5 are at room3, ball11, ball8, ball9, ball2, ball15, ball10, ball4, and ball14 are at room1.", "question": "Is it possible to transition to a state where the action \"fly the drone robot1 from room room2 to room room1\" can be applied?", "answer": "no"}
{"id": -5509035351241272419, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball7. Additionally, ball5 is at room3, ball3, ball2, and ball4 are at room1, ball6 is at room2.", "question": "Is it possible to transition to a state where the action \"drop the object ball1 in room room2 using robot robot1 with the left1 gripper\" can be applied?", "answer": "yes"}
{"id": -395911225839478783, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball6, and right gripper is carrying the ball ball7. Additionally, ball3, ball2, and ball4 are at room1, ball1 is at room3, ball5 is at room2.", "question": "Is it possible to transition to a state where the action \"pick up the object ball2 with robot robot1 using left1 gripper from room room1\" can be applied?", "answer": "yes"}
{"id": -568780221617038387, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1 and both grippers are free. Additionally, ball1 and ball4 are at room2, ball3 and ball2 are at room1.", "question": "Is it possible to transition to a state where the action \"pick up the object ball3 with robot robot1 using left1 gripper from the table room1\" can be applied?", "answer": "no"}
{"id": -2528214975426279629, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball2. Additionally, ball3 and ball1 are at room4, ball4 is at room5.", "question": "Is it possible to transition to a state where the action \"use the robot robot1 equipped with right1 gripper to retrieve the object room4 from room room3\" can be applied?", "answer": "no"}
{"id": 7899061807573419434, "group": "reachable_action_bool", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball10, and right gripper is carrying the ball ball3. Additionally, ball12, ball1, and ball13 are at room2, ball7, ball11, and ball5 are at room3, ball8, ball9, ball2, ball15, ball6, ball4, and ball14 are at room1.", "question": "Is it possible to transition to a state where the action \"use the left1 gripper of robot robot1 to drop the object ball10 in room room1\" can be applied?", "answer": "yes"}
{"id": 2660727076706033349, "group": "reachable_action_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode low_res. Rover rover1 has image objective1 in mode low_res. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Is it possible to transition to a state where the action \"navigate with rover rover0 to waypoint waypoint2 from waypoint waypoint0\" can be applied?", "answer": "yes"}
{"id": 230722108409223916, "group": "reachable_action_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint2. Rovers rover0 and rover1 are available. Image objective1 was communicated in mode low_res. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Is it possible to transition to a state where the action \"sample the soil at waypoint waypoint0 using the rover rover0, then store it in the storage unit store1\" can be applied?", "answer": "no"}
{"id": 4770355527731704119, "group": "reachable_action_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint1. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective1 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover0 has image objective1 in mode colour. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Is it possible to transition to a state where the action \"take an image of the objective objective0 in mode colour using the camera camera2 on the rover rover1 from the waypoint waypoint0\" can be applied?", "answer": "yes"}
{"id": -1600275235447532015, "group": "reachable_action_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint1. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode colour. Store(s) store0 and store1 are full. ", "question": "Is it possible to transition to a state where the action \"wash store store0 of the rover rover0\" can be applied?", "answer": "no"}
{"id": 4368414648995623981, "group": "reachable_action_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective4. Camera camera1 can be calibrated on objective1. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint1. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective4 is visible from waypoint1. Objective objective3 is visible from waypoint2. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Objective objective5 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint1. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rock data were communicated from the following waypoints: waypoint0, waypoint2. Soil data was communicated from waypoint waypoint2; Image objective0 was communicated in mode colour. Image objective3 was communicated in mode high_res. Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover1 has rock analyzed in waypoint waypoint2. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective0 in mode colour. Rover rover1 has image objective3 in mode high_res. Rover rover0 has its camera camera1 calibrated. Store(s) store0 and store1 are empty. ", "question": "Is it possible to transition to a state where the action \"navigate with rover rover1 to waypoint waypoint1 from waypoint waypoint2\" can be applied?", "answer": "yes"}
{"id": 2773569452670034774, "group": "reachable_action_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Rover rover1 has its camera camera0 calibrated. Store(s) store1 is empty. Store(s) store0 is full. ", "question": "Is it possible to transition to a state where the action \"take an image of the objective objective1 in mode high_res using the camera camera1 on the rover rover1 from the waypoint waypoint1\" can be applied?", "answer": "no"}
{"id": 1534586298578037983, "group": "reachable_action_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective0. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint1. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint0. Rovers rover0 and rover1 are available. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has its camera camera2 calibrated. Rover rover0 has its camera camera1 calibrated. Store(s) store0 and store1 are full. ", "question": "Is it possible to transition to a state where the action \"navigate rover rover1 from waypoint waypoint2 to waypoint waypoint1\" can be applied?", "answer": "yes"}
{"id": 8799603491221589763, "group": "reachable_action_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera1 supports colour and low_res. Camera camera0 supports colour and low_res. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint0: waypoint3, waypoint4, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint1 and waypoint2. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint1. Rovers rover0 and rover1 are available. Rock data were communicated from the following waypoints: waypoint0, waypoint1. Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode low_res. Image objective0 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective0 in mode colour. Rover rover1 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Store(s) store0 and store1 are full. ", "question": "Is it possible to transition to a state where the action \"navigate with rover rover1 to waypoint waypoint1 from waypoint waypoint4\" can be applied?", "answer": "no"}
{"id": -5228807064688545932, "group": "reachable_action_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera2 on board. Rover rover0 has camera1 and camera0 on board. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective4. Camera camera1 can be calibrated on objective1. Camera camera2 supports high_res and colour. Camera camera0 supports colour. Camera camera1 supports low_res. Rover rover0 can traverse from waypoint0 to waypoint2, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint1. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Objective objective4 is visible from waypoint1. Objective objective3 is visible from waypoint2. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective1 is visible from waypoint2. Objective objective5 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data were communicated from the following waypoints: waypoint2, waypoint1. Image objective0 was communicated in mode low_res. Image objective0 was communicated in mode colour. Image objective3 was communicated in mode high_res. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover1 has soil analyzed in waypoint waypoint2. Rover rover1 has rock analyzed in waypoint waypoint2. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective0 in mode colour. Rover rover0 has image objective0 in mode low_res. Rover rover1 has image objective3 in mode high_res. Store(s) store0 and store1 are full. ", "question": "Is it possible to transition to a state where the action \"communicate image data of objective objective1 in mode high_res from rover rover0 at waypoint waypoint2 to lander general at waypoint waypoint0\" can be applied?", "answer": "no"}
{"id": -3760732311126952632, "group": "reachable_action_bool", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera0 on board. Rover rover1 has camera1 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera1 supports colour and low_res. Camera camera0 supports colour and low_res. Rover rover1 can traverse from waypoint0 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0. Rover rover0 can traverse from waypoint1 to waypoint0, waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint4: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint0: waypoint3, waypoint4, waypoint1, and waypoint2. Waypoint(s) are visible from waypoint2: waypoint0, waypoint3, and waypoint1. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Objective objective1 is visible from waypoint4. Objective objective0 is visible from waypoint1 and waypoint2. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint0. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Image objective0 was communicated in mode colour. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode colour. Rover rover1 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Is it possible to transition to a state where the action \"communicate the image data of objective objective0 in mode low_res from rover rover1 at waypoint waypoint2 to lander general at waypoint waypoint3\" can be applied?", "answer": "yes"}
{"id": 542097817266415445, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x0-y4, and loc-x2-y3. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x0-y0, loc-x3-y2, loc-x1-y1, loc-x3-y3, loc-x0-y2, loc-x2-y0, loc-x1-y0, loc-x3-y0, loc-x3-y1, loc-x0-y1, loc-x3-y4, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"push box to place loc-x3-y4 from place loc-x2-y4\" can be applied?", "answer": "no"}
{"id": 5002196130158764572, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x0-y0, loc-x1-y4, loc-x1-y1, loc-x2-y2, loc-x2-y0, loc-x3-y2, loc-x1-y2, loc-x1-y3, loc-x1-y0, loc-x3-y1, loc-x3-y4, loc-x2-y1, loc-x0-y2, loc-x0-y4, loc-x0-y3, loc-x3-y3, loc-x3-y0, loc-x0-y1, and loc-x2-y4.", "question": "Is it possible to transition to a state where the action \"check that position loc-x2-y4 is connected to position loc-x3-y4\" can be applied?", "answer": "no"}
{"id": -676010873499900832, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x0-y4.The following places have been visited: loc-x0-y0, loc-x1-y2, loc-x1-y3, loc-x1-y1, loc-x0-y4, loc-x0-y3, loc-x0-y2, loc-x2-y0, loc-x1-y0, loc-x3-y0, loc-x3-y1, loc-x0-y1, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"travel from loc-x0-y2 to loc-x0-y1\" can be applied?", "answer": "yes"}
{"id": 4730290038882355459, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x0-y3.The following places have been visited: loc-x0-y0, loc-x0-y3, loc-x0-y2, loc-x1-y0, and loc-x0-y1.", "question": "Is it possible to transition to a state where the action \"push box to place loc-x0-y2 from place loc-x1-y2\" can be applied?", "answer": "no"}
{"id": 5813408539699212715, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x0-y2, loc-x1-y0, and loc-x3-y3. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x1-y2, loc-x1-y3, loc-x0-y3, loc-x2-y2, loc-x2-y0, loc-x3-y0, loc-x3-y2, loc-x2-y3, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"push box to place loc-x1-y2 from place loc-x1-y3\" can be applied?", "answer": "no"}
{"id": 7974901258154901219, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x2-y0, loc-x3-y0, loc-x1-y0, loc-x3-y1, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"check that position loc-x2-y1 is connected to position loc-x3-y1\" can be applied?", "answer": "no"}
{"id": 3232358558587572451, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x2-y0.The following places have been visited: loc-x1-y2, loc-x1-y3, loc-x1-y1, loc-x3-y3, loc-x2-y2, loc-x2-y0, loc-x3-y0, loc-x3-y2, loc-x2-y3, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"check that position loc-x2-y0 is connected to position loc-x2-y1\" can be applied?", "answer": "no"}
{"id": -7046691313528293343, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x0-y4, and loc-x2-y3. Currently, the robot is in place loc-x2-y2.The following places have been visited: loc-x0-y0, loc-x1-y1, loc-x2-y2, loc-x0-y2, loc-x2-y0, loc-x1-y0, loc-x3-y0, loc-x3-y1, loc-x0-y1, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"check that position loc-x2-y1 is connected to position loc-x3-y1\" can be applied?", "answer": "no"}
{"id": -7701635755153878241, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x0-y2, loc-x1-y0, and loc-x3-y3. Currently, the robot is in place loc-x2-y3.The following places have been visited: loc-x0-y0, loc-x1-y2, loc-x1-y1, loc-x2-y2, loc-x2-y0, loc-x3-y0, loc-x3-y2, loc-x0-y1, loc-x2-y3, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"Please move to loc-x3-y2 position from loc-x2-y2 position\" can be applied?", "answer": "yes"}
{"id": -5407966406891222591, "group": "reachable_action_bool", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x3-y3.The following places have been visited: loc-x0-y0, loc-x1-y2, loc-x1-y3, loc-x3-y2, loc-x1-y1, loc-x0-y3, loc-x3-y3, loc-x0-y2, loc-x2-y0, loc-x1-y0, loc-x3-y0, loc-x3-y1, loc-x0-y1, and loc-x2-y1.", "question": "Is it possible to transition to a state where the action \"navigate from loc-x1-y3 to loc-x0-y3\" can be applied?", "answer": "yes"}
{"id": -4342773075478632270, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 hoists, 2 distributors, 4 depots, 3 crates, 6 pallets, numbered consecutively. Currently, pallet0, pallet2, crate0, pallet5, pallet4, and pallet1 are clear; hoist5, hoist2, hoist3, hoist4, and hoist0 are available; hoist1 is at depot1, crate0 is at depot3, hoist3 is at depot3, pallet2 is at depot2, pallet0 is at depot0, truck0 is at depot1, pallet4 is at distributor0, pallet3 is at depot3, truck1 is at depot3, pallet1 is at depot1, hoist5 is at distributor1, hoist2 is at depot2, hoist4 is at distributor0, pallet5 is at distributor1, and hoist0 is at depot0; crate0 is on pallet3; crate1 is in truck0; hoist1 is lifting crate2.", "question": "Is it possible to transition to a state where the action \"drive truck truck0 from place depot3 to place distributor1\" can be applied?", "answer": "yes"}
{"id": -5541930256487907280, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 9 hoists, 2 distributors, 7 depots, 2 crates, 9 pallets, numbered consecutively. Currently, pallet8, pallet0, pallet2, pallet7, pallet1, crate1, pallet6, pallet5, and pallet4 are clear; hoist5, hoist6, hoist7, hoist1, hoist2, hoist8, hoist3, hoist4, and hoist0 are available; hoist1 is at depot1, pallet6 is at depot6, hoist3 is at depot3, pallet2 is at depot2, pallet0 is at depot0, hoist4 is at depot4, truck0 is at distributor1, pallet3 is at depot3, hoist8 is at distributor1, pallet7 is at distributor0, pallet8 is at distributor1, pallet1 is at depot1, hoist2 is at depot2, pallet4 is at depot4, truck1 is at depot6, hoist5 is at depot5, hoist0 is at depot0, pallet5 is at depot5, hoist7 is at distributor0, hoist6 is at depot6, and crate1 is at depot3; crate1 is on pallet3; crate0 is in truck0.", "question": "Is it possible to transition to a state where the action \"lift the crate crate0 from the surface crate0 at place depot2 using the hoist hoist2\" can be applied?", "answer": "no"}
{"id": -7684579972944915918, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 5 hoists, 2 distributors, 3 depots, 2 crates, 5 pallets, numbered consecutively. Currently, pallet0, crate1, pallet4, pallet3, and pallet1 are clear; hoist2, hoist3, hoist4, and hoist0 are available; hoist1 is at depot1, truck1 is at depot0, pallet2 is at depot2, pallet0 is at depot0, pallet1 is at depot1, truck0 is at depot2, hoist2 is at depot2, pallet4 is at distributor1, crate1 is at depot2, hoist3 is at distributor0, hoist0 is at depot0, pallet3 is at distributor0, and hoist4 is at distributor1; crate1 is on pallet2; hoist1 is lifting crate0.", "question": "Is it possible to transition to a state where the action \"navigate the truck truck1 from place distributor0 to place distributor1\" can be applied?", "answer": "yes"}
{"id": 765685902985112447, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 distributors, 2 depots, 2 crates, 4 pallets, numbered consecutively. Currently, pallet0, pallet2, pallet3, and pallet1 are clear; hoist1, hoist2, and hoist3 are available; pallet3 is at distributor1, hoist2 is at distributor0, hoist1 is at depot1, pallet2 is at distributor0, pallet0 is at depot0, truck0 is at distributor1, hoist3 is at distributor1, pallet1 is at depot1, truck1 is at depot1, and hoist0 is at depot0; crate1 is in truck1; hoist0 is lifting crate0.", "question": "Is it possible to transition to a state where the action \"sail boat truck0 from place distributor1 to place depot0\" can be applied?", "answer": "no"}
{"id": -7925189841199365641, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 9 hoists, 2 distributors, 7 depots, 2 crates, 9 pallets, numbered consecutively. Currently, pallet0, pallet2, pallet7, pallet1, crate0, crate1, pallet6, pallet5, and pallet4 are clear; hoist5, hoist6, hoist7, hoist1, hoist2, hoist8, hoist3, hoist4, and hoist0 are available; hoist1 is at depot1, pallet6 is at depot6, truck1 is at distributor0, crate0 is at distributor1, hoist3 is at depot3, pallet2 is at depot2, pallet0 is at depot0, hoist4 is at depot4, truck0 is at distributor1, pallet3 is at depot3, hoist8 is at distributor1, pallet7 is at distributor0, pallet8 is at distributor1, pallet1 is at depot1, hoist2 is at depot2, pallet4 is at depot4, hoist5 is at depot5, hoist0 is at depot0, pallet5 is at depot5, hoist7 is at distributor0, hoist6 is at depot6, and crate1 is at depot3; crate0 is on pallet8 and crate1 is on pallet3.", "question": "Is it possible to transition to a state where the action \"drive truck truck0 from place depot3 to place depot2\" can be applied?", "answer": "yes"}
{"id": -4561319760216957932, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 9 hoists, 2 distributors, 7 depots, 2 crates, 9 pallets, numbered consecutively. Currently, pallet8, pallet0, pallet2, pallet7, crate0, pallet6, pallet5, pallet4, and pallet3 are clear; hoist5, hoist6, hoist7, hoist1, hoist2, hoist8, hoist4, and hoist0 are available; hoist1 is at depot1, crate0 is at depot1, pallet6 is at depot6, hoist3 is at depot3, pallet2 is at depot2, pallet0 is at depot0, hoist4 is at depot4, truck1 is at depot5, pallet3 is at depot3, hoist8 is at distributor1, pallet7 is at distributor0, pallet8 is at distributor1, pallet1 is at depot1, truck0 is at depot2, hoist2 is at depot2, pallet4 is at depot4, hoist5 is at depot5, hoist0 is at depot0, pallet5 is at depot5, hoist7 is at distributor0, and hoist6 is at depot6; crate0 is on pallet1; hoist3 is lifting crate1.", "question": "Is it possible to transition to a state where the action \"navigate the truck truck1 from location depot0 to location distributor1\" can be applied?", "answer": "yes"}
{"id": -3289685932003074007, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 distributors, 2 depots, 2 crates, 4 pallets, numbered consecutively. Currently, pallet2, crate1, pallet3, and pallet1 are clear; hoist1, hoist2, and hoist3 are available; pallet3 is at distributor1, hoist2 is at distributor0, hoist1 is at depot1, pallet2 is at distributor0, pallet0 is at depot0, truck0 is at distributor1, hoist3 is at distributor1, pallet1 is at depot1, truck1 is at depot1, hoist0 is at depot0, and crate1 is at depot0; crate1 is on pallet0; hoist0 is lifting crate0.", "question": "Is it possible to transition to a state where the action \"navigate the boat truck0 from the place distributor1 to the place depot1\" can be applied?", "answer": "no"}
{"id": -464943208582304044, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 12 hoists, 2 distributors, 10 depots, 2 crates, 12 pallets, numbered consecutively. Currently, pallet8, pallet9, pallet0, pallet2, pallet7, pallet10, pallet11, pallet6, pallet5, pallet4, pallet3, and pallet1 are clear; hoist5, hoist6, hoist7, hoist9, hoist1, hoist8, hoist10, hoist3, hoist4, and hoist0 are available; hoist1 is at depot1, pallet6 is at depot6, pallet8 is at depot8, hoist3 is at depot3, pallet2 is at depot2, pallet0 is at depot0, pallet7 is at depot7, hoist4 is at depot4, hoist7 is at depot7, hoist8 is at depot8, hoist11 is at distributor1, pallet3 is at depot3, hoist10 is at distributor0, pallet10 is at distributor0, pallet1 is at depot1, hoist9 is at depot9, hoist2 is at depot2, pallet4 is at depot4, pallet11 is at distributor1, hoist5 is at depot5, hoist0 is at depot0, truck1 is at depot8, pallet5 is at depot5, truck0 is at depot0, pallet9 is at depot9, and hoist6 is at depot6; hoist2 is lifting crate1 and hoist11 is lifting crate0.", "question": "Is it possible to transition to a state where the action \"drop crate crate0 from hoist hoist7 onto surface crate0 at place depot7\" can be applied?", "answer": "no"}
{"id": 360372597903677301, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 hoists, 2 distributors, 4 depots, 3 crates, 6 pallets, numbered consecutively. Currently, pallet0, pallet2, pallet5, pallet4, pallet3, and pallet1 are clear; hoist5, hoist1, hoist2, hoist3, and hoist4 are available; hoist1 is at depot1, truck1 is at depot0, hoist3 is at depot3, pallet2 is at depot2, pallet0 is at depot0, pallet4 is at distributor0, pallet3 is at depot3, truck0 is at depot3, pallet1 is at depot1, hoist5 is at distributor1, hoist2 is at depot2, hoist4 is at distributor0, pallet5 is at distributor1, and hoist0 is at depot0; crate2 is in truck0 and crate1 is in truck0; hoist0 is lifting crate0.", "question": "Is it possible to transition to a state where the action \"lift the crate crate1 from the surface crate1 at place depot1 using the hoist hoist1\" can be applied?", "answer": "no"}
{"id": -8631580485741033800, "group": "reachable_action_bool", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 distributors, 2 depots, 2 crates, 4 pallets, numbered consecutively. Currently, pallet2, crate1, pallet3, and pallet1 are clear; hoist1, hoist2, hoist3, and hoist0 are available; pallet3 is at distributor1, hoist2 is at distributor0, hoist1 is at depot1, pallet2 is at distributor0, pallet0 is at depot0, truck0 is at depot1, hoist3 is at distributor1, pallet1 is at depot1, truck1 is at distributor1, hoist0 is at depot0, and crate1 is at depot0; crate1 is on pallet0; crate0 is in truck1.", "question": "Is it possible to transition to a state where the action \"pack hoist hoist0 into crate crate1 in truck truck0 at place depot0\" can be applied?", "answer": "no"}
{"id": -5185900214886679112, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f3-1f, f1-2f, f1-1f, f3-3f, and f1-3f. The following locations have soft rock: f2-2f, f2-3f, f0-2f, f3-2f, and f0-3f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Is it possible to transition to a state where the action \"pick up gold at loc f2-1f\" can be applied?", "answer": "no"}
{"id": -2040999324568741556, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a bomb. The following locations have hard rock: f3-1f, f1-2f, f3-3f, and f1-3f. The following locations have soft rock: f2-1f, f2-2f, f2-3f, and f3-2f. The gold is at f0-3f location. The laser is at f0-1f location.", "question": "Is it possible to transition to a state where the action \"detonate the bomb at location f0-3f connected to location f1-3f\" can be applied?", "answer": "no"}
{"id": 7988110880572192527, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and is holding a bomb. The following locations have hard rock: f3-1f, f1-2f, f1-1f, f3-3f, and f1-3f. The following locations have soft rock: f2-1f, f2-2f, f2-3f, f0-2f, f3-2f, f0-1f, and f0-3f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Is it possible to transition to a state where the action \"pick up gold at loc f1-1f\" can be applied?", "answer": "no"}
{"id": 612869136284885273, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and its arm is empty. The following locations have hard rock: f2-1f, f2-2f, and f0-3f. The following locations have soft rock: f1-3f, f2-3f, f1-2f, and f0-2f. The gold is at f1-3f location. The laser is at f1-0f location.", "question": "Is it possible to transition to a state where the action \"retrieve gold from location f1-2f\" can be applied?", "answer": "no"}
{"id": -4298082174934767471, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-3f and is holding gold. The following locations have hard rock: f1-1f, f1-4f, and f1-3f. The following locations have soft rock: f2-1f, f2-2f, f2-3f, and f2-4f. The gold is at f0-4f location. The laser is at f0-1f location.", "question": "Is it possible to transition to a state where the action \"connect location f0-1f to location f0-0f\" can be applied?", "answer": "no"}
{"id": 5161508049851913784, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and is holding a bomb. The following locations have hard rock: f2-1f, f2-2f, and f0-3f. The following locations have soft rock: f1-3f, f2-3f, and f0-2f. The gold is at f1-3f location. The laser is at f1-1f location.", "question": "Is it possible to transition to a state where the action \"travel from location f1-0f to location f0-0f\" can be applied?", "answer": "yes"}
{"id": -5805041894911372400, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-1f, f1-4f, and f1-3f. The following locations have soft rock: f0-4f, f2-1f, f2-2f, f2-3f, f1-2f, and f2-4f. The gold is at f0-4f location. The laser is at f0-1f location.", "question": "Is it possible to transition to a state where the action \"move from loc f0-2f to loc f0-3f\" can be applied?", "answer": "yes"}
{"id": -3674750475377599172, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and its arm is empty. The following locations have hard rock: f2-1f, f2-2f, and f0-3f. The following locations have soft rock: f1-3f, f2-3f, and f0-2f. The gold is at f1-3f location. The laser is at f1-0f location.", "question": "Is it possible to transition to a state where the action \"connect location f0-0f to location f1-0f\" can be applied?", "answer": "no"}
{"id": 8439843970908597774, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-0f and is holding a bomb. The following locations have hard rock: f2-1f, f2-2f, and f0-3f. The following locations have soft rock: f1-3f, f2-3f, and f0-2f. The gold is at f1-3f location. The laser is at f1-0f location.", "question": "Is it possible to transition to a state where the action \"move from location f1-2f to location f1-1f\" can be applied?", "answer": "yes"}
{"id": -8042048943001215427, "group": "reachable_action_bool", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f2-0f and its arm is empty. The following locations have hard rock: f0-2f and f1-1f. The following locations have soft rock: f2-1f, f0-1f, f1-3f, f2-3f, f0-3f, f2-2f, and f1-2f. The gold is at f0-3f location. The laser is at f2-0f location.", "question": "Is it possible to transition to a state where the action \"trigger the explosion of the bomb at location f1-0f, which is connected to location f0-0f\" can be applied?", "answer": "no"}
{"id": 697982829725650112, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): star0, groundstation5, planet8, planet7, groundstation3, star2, star9, groundstation1, groundstation4, star10, groundstation6. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument5, instrument4, instrument3. Satellite satellite5 has following instruments onboard: instrument9, instrument10. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite0 has following instruments onboard: instrument1, instrument0. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument6 supports image of mode infrared1 and its calibration target is groundstation4. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument1 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to planet7. Satellite satellite1 is pointing to star9. Satellite satellite5 is pointing to star2. Satellite satellite4 is pointing to star10. Satellite satellite0 is pointing to star10. Satellite satellite3 is pointing to star9. Power is available on the following satellite(s): satellite4, satellite0, satellite3, satellite1, satellite5. Following instruments are powered on: instrument3. Following instruments are calibrated: instrument3. A thermograph2 mode image of target planet7 is available. A infrared1 mode image of target planet8 is available. A infrared1 mode image of target star9 is available. A infrared1 mode image of target star10 is available. ", "question": "Is it possible to transition to a state where the action \"point the satellite satellite0 to direction star9 instead of planet7\" can be applied?", "answer": "yes"}
{"id": -6521562304411901465, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): star0, groundstation3, star2, planet5, phenomenon6, groundstation1, groundstation4. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to planet5. Satellite satellite1 is pointing to phenomenon6. Satellite satellite0 is pointing to groundstation4. Power is available on the following satellite(s): satellite0, satellite1. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A spectrograph0 mode image of target phenomenon6 is available. ", "question": "Is it possible to transition to a state where the action \"fire laser in direction planet5 in mode infrared1 using the instrument instrument4 on satellite satellite2\" can be applied?", "answer": "no"}
{"id": -8904592331809701451, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): star0, groundstation5, planet8, planet7, groundstation3, star2, star9, groundstation1, groundstation4, star10, groundstation6. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument5, instrument4, instrument3. Satellite satellite5 has following instruments onboard: instrument9, instrument10. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite0 has following instruments onboard: instrument1, instrument0. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument6 supports image of mode infrared1 and its calibration target is groundstation4. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument1 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to star10. Satellite satellite5 is pointing to star2. Satellite satellite4 is pointing to star10. Satellite satellite0 is pointing to star10. Satellite satellite3 is pointing to star9. Satellite satellite1 is pointing to groundstation6. Power is available on the following satellite(s): satellite4, satellite0, satellite3, satellite1, satellite5. Following instruments are powered on: instrument3. Following instruments are calibrated: instrument3. A infrared1 mode image of target planet8 is available. A infrared1 mode image of target star9 is available. ", "question": "Is it possible to transition to a state where the action \"move cargo on satellite satellite4 from star10 to groundstation4\" can be applied?", "answer": "no"}
{"id": 4224778746955172081, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, star3, star1, phenomenon5, groundstation4, groundstation2. There are 3 image mode(s): image0, image2, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument3, instrument5, instrument4. Satellite satellite5 has following instruments onboard: instrument13, instrument12, instrument14. Satellite satellite4 has following instruments onboard: instrument11, instrument9, instrument10. Satellite satellite6 has following instruments onboard: instrument17, instrument16, instrument15. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument2 supports image of mode image1 and its calibration target is star1. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image1 and its calibration target is star3. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument6 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument8 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite0 is pointing to groundstation2. Satellite satellite3 is pointing to groundstation2. Satellite satellite4 is pointing to star1. Satellite satellite6 is pointing to groundstation0. Satellite satellite1 is pointing to phenomenon5. Satellite satellite5 is pointing to groundstation0. Satellite satellite2 is pointing to phenomenon5. Power is available on the following satellite(s): satellite4, satellite0, satellite3, satellite6, satellite1, satellite5. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. A image1 mode image of target star6 is available. A image0 mode image of target phenomenon5 is available. ", "question": "Is it possible to transition to a state where the action \"turn the satellite satellite2 from direction star3 to direction star6\" can be applied?", "answer": "yes"}
{"id": 4936761361611780016, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): star0, groundstation5, planet8, planet7, groundstation3, star2, star9, groundstation1, groundstation4, star10, groundstation6. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument5, instrument4, instrument3. Satellite satellite5 has following instruments onboard: instrument9, instrument10. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite0 has following instruments onboard: instrument1, instrument0. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument6 supports image of mode infrared1 and its calibration target is groundstation4. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument9 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument4 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation1. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument1 supports image of mode infrared1 and its calibration target is groundstation5. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to star0. Satellite satellite1 is pointing to groundstation4. Satellite satellite5 is pointing to star2. Satellite satellite4 is pointing to star10. Satellite satellite0 is pointing to star10. Satellite satellite3 is pointing to star9. Power is available on the following satellite(s): satellite3, satellite1. Following instruments are powered on: instrument10, instrument5, instrument7, instrument0. ", "question": "Is it possible to transition to a state where the action \"turn satellite satellite5 to point from star10 direction to groundstation4\" can be applied?", "answer": "yes"}
{"id": 2069740558730661775, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, star3, star1, phenomenon5, groundstation4, groundstation2. There are 3 image mode(s): image0, image2, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument3, instrument5, instrument4. Satellite satellite5 has following instruments onboard: instrument13, instrument12, instrument14. Satellite satellite4 has following instruments onboard: instrument11, instrument9, instrument10. Satellite satellite6 has following instruments onboard: instrument17, instrument16, instrument15. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument2 supports image of mode image1 and its calibration target is star1. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image1 and its calibration target is star3. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument6 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument8 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite2 is pointing to groundstation2. Satellite satellite3 is pointing to star1. Satellite satellite0 is pointing to groundstation2. Satellite satellite4 is pointing to star1. Satellite satellite6 is pointing to groundstation0. Satellite satellite1 is pointing to phenomenon5. Satellite satellite5 is pointing to star6. Power is available on the following satellite(s): satellite4, satellite0, satellite2, satellite3, satellite1, satellite5. Following instruments are powered on: instrument16. ", "question": "Is it possible to transition to a state where the action \"fix instrument instrument1 on the satellite satellite0\" can be applied?", "answer": "no"}
{"id": 7216992357351929206, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star6, star3, star1, phenomenon5, groundstation4, groundstation2. There are 3 image mode(s): image0, image2, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument3, instrument5, instrument4. Satellite satellite5 has following instruments onboard: instrument13, instrument12, instrument14. Satellite satellite4 has following instruments onboard: instrument11, instrument9, instrument10. Satellite satellite6 has following instruments onboard: instrument17, instrument16, instrument15. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite2 has following instruments onboard: instrument6. Instrument instrument2 supports image of mode image1 and its calibration target is star1. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument3 supports image of mode image1 and its calibration target is star3. Instrument instrument7 supports image of mode image0 and its calibration target is star3. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument5 supports image of mode image0 and its calibration target is star3. Instrument instrument6 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument8 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument14 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument13 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0.  Currently, Satellite satellite4 is pointing to star3. Satellite satellite3 is pointing to groundstation0. Satellite satellite5 is pointing to star1. Satellite satellite6 is pointing to groundstation0. Satellite satellite0 is pointing to star1. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to phenomenon5. Power is available on the following satellite(s): satellite4, satellite0, satellite3, satellite6, satellite1, satellite5. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. A image2 mode image of target phenomenon5 is available. ", "question": "Is it possible to transition to a state where the action \"transfer weight on satellite satellite2 from section star1 to section star3\" can be applied?", "answer": "no"}
{"id": -5030808179310385287, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): star0, groundstation3, star2, planet5, phenomenon6, groundstation1, groundstation4. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite0 is pointing to groundstation3. Satellite satellite1 is pointing to groundstation3. Satellite satellite2 is pointing to phenomenon6. Power is available on the following satellite(s): satellite0, satellite1. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. A spectrograph0 mode image of target phenomenon6 is available. ", "question": "Is it possible to transition to a state where the action \"move cargo on satellite satellite1 from star2 to groundstation1\" can be applied?", "answer": "no"}
{"id": 1677785867634412520, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, groundstation3, star2, planet5, groundstation1, star4, planet6. There are 3 image mode(s): infrared2, thermograph0, image1. There are 16 instrument(s), numbered consecutively.  Satellite satellite6 has following instruments onboard: instrument9, instrument11, instrument10. Satellite satellite3 has following instruments onboard: instrument4, instrument5. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite8 has following instruments onboard: instrument14, instrument13. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Satellite satellite4 has following instruments onboard: instrument7, instrument6. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite7 has following instruments onboard: instrument12. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument0 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument12 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument7 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument6 supports image of mode thermograph0 and its calibration target is groundstation0. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument4 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument2 supports image of mode thermograph0 and its calibration target is star2. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3.  Currently, Satellite satellite8 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite0 is pointing to groundstation1. Satellite satellite2 is pointing to planet6. Satellite satellite4 is pointing to planet6. Satellite satellite9 is pointing to star4. Satellite satellite3 is pointing to planet5. Satellite satellite6 is pointing to star4. Satellite satellite5 is pointing to groundstation3. Satellite satellite7 is pointing to star2. Power is available on the following satellite(s): satellite0, satellite7, satellite6, satellite8, satellite1, satellite5, satellite4, satellite2, satellite9. Following instruments are powered on: instrument5. Following instruments are calibrated: instrument5. A infrared2 mode image of target planet6 is available. A thermograph0 mode image of target planet5 is available. ", "question": "Is it possible to transition to a state where the action \"move cargo on satellite satellite9 from star4 to groundstation0\" can be applied?", "answer": "no"}
{"id": 3060792481324172375, "group": "reachable_action_bool", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): star0, groundstation3, star2, planet5, phenomenon6, groundstation1, groundstation4. There are 3 image mode(s): infrared1, thermograph2, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite0 has following instruments onboard: instrument1, instrument0, instrument2. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument0 supports image of mode infrared1 and its calibration target is star0. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is star2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is groundstation3.  Currently, Satellite satellite2 is pointing to star2. Satellite satellite0 is pointing to groundstation4. Satellite satellite1 is pointing to star0. Power is available on the following satellite(s): satellite0, satellite2, satellite1. ", "question": "Is it possible to transition to a state where the action \"move cargo on satellite satellite2 from groundstation3 to groundstation1\" can be applied?", "answer": "no"}
{"id": -5521162125335532902, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, dave, xena, vic, carol, zoe, and alice. There are 8 items/roles: necklace, frisbee, quadcopter, guitar, zebra, iceskates, whale, and slinky. Currently, michelle is assigned zebra, alice is assigned quadcopter, carol is assigned guitar, zoe is assigned frisbee, vic is assigned necklace, dave is assigned slinky, heidi is assigned whale, and xena is assigned iceskates.", "question": "Is it possible to transition to a state where the action \"drive michelle and xena from guitar to frisbee\" can be applied?", "answer": "no"}
{"id": 8743683866226050442, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, bob, dave, xena, kevin, ted, and alice. There are 7 items/roles: quince, leek, yam, mushroom, valerian, parsnip, and ulluco. Currently, alice is assigned valerian, kevin is assigned ulluco, heidi is assigned quince, xena is assigned parsnip, dave is assigned leek, ted is assigned mushroom, and bob is assigned yam.", "question": "Is it possible to transition to a state where the action \"marry alice with kevin using parsnip and ulluco\" can be applied?", "answer": "no"}
{"id": -5160240541075683291, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: heidi, bob, dave, xena, kevin, ted, and alice. There are 7 items/roles: quince, leek, yam, mushroom, valerian, parsnip, and ulluco. Currently, heidi is assigned mushroom, xena is assigned quince, alice is assigned yam, ted is assigned leek, kevin is assigned parsnip, bob is assigned ulluco, and dave is assigned valerian.", "question": "Is it possible to transition to a state where the action \"marry alice with ted using yam and leek\" can be applied?", "answer": "no"}
{"id": -7207305354013748401, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, dave, xena, vic, carol, zoe, and alice. There are 8 items/roles: necklace, frisbee, quadcopter, guitar, zebra, iceskates, whale, and slinky. Currently, xena is assigned frisbee, dave is assigned necklace, michelle is assigned slinky, carol is assigned iceskates, vic is assigned quadcopter, heidi is assigned guitar, zoe is assigned whale, and alice is assigned zebra.", "question": "Is it possible to transition to a state where the action \"swap xena with xena, frisbee for guitar\" can be applied?", "answer": "no"}
{"id": -8115253248543848352, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, dave, xena, vic, carol, zoe, and alice. There are 8 items/roles: necklace, frisbee, quadcopter, guitar, zebra, iceskates, whale, and slinky. Currently, carol is assigned guitar, vic is assigned zebra, zoe is assigned frisbee, alice is assigned iceskates, dave is assigned slinky, michelle is assigned quadcopter, xena is assigned whale, and heidi is assigned necklace.", "question": "Is it possible to transition to a state where the action \"exchange necklace of vic with iceskates of heidi\" can be applied?", "answer": "yes"}
{"id": 3424125903127121322, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, xena, frank, vic, quentin, and liam. There are 6 items/roles: wrench, knead, ratchet, nibbler, pliers, and sander. Currently, bob is assigned ratchet, frank is assigned wrench, xena is assigned pliers, liam is assigned sander, quentin is assigned knead, and vic is assigned nibbler.", "question": "Is it possible to transition to a state where the action \"exchange ratchet of frank with pliers of frank\" can be applied?", "answer": "no"}
{"id": -8225768601398321772, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, xena, frank, vic, quentin, and liam. There are 6 items/roles: wrench, knead, ratchet, nibbler, pliers, and sander. Currently, xena is assigned nibbler, frank is assigned ratchet, bob is assigned pliers, vic is assigned sander, quentin is assigned knead, and liam is assigned wrench.", "question": "Is it possible to transition to a state where the action \"swap vic with frank, pliers for knead\" can be applied?", "answer": "yes"}
{"id": -1111435978795325206, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, xena, frank, vic, quentin, and liam. There are 6 items/roles: wrench, knead, ratchet, nibbler, pliers, and sander. Currently, vic is assigned wrench, xena is assigned sander, bob is assigned nibbler, liam is assigned ratchet, quentin is assigned knead, and frank is assigned pliers.", "question": "Is it possible to transition to a state where the action \"trade ratchet of quentin for pliers of frank\" can be applied?", "answer": "yes"}
{"id": -4264426084168253139, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, dave, xena, vic, carol, zoe, and alice. There are 8 items/roles: necklace, frisbee, quadcopter, guitar, zebra, iceskates, whale, and slinky. Currently, michelle is assigned zebra, xena is assigned quadcopter, zoe is assigned frisbee, alice is assigned iceskates, dave is assigned slinky, vic is assigned guitar, carol is assigned whale, and heidi is assigned necklace.", "question": "Is it possible to transition to a state where the action \"trade whale of vic for necklace of heidi\" can be applied?", "answer": "yes"}
{"id": 6767165740127222054, "group": "reachable_action_bool", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: heidi, michelle, dave, xena, vic, carol, zoe, and alice. There are 8 items/roles: necklace, frisbee, quadcopter, guitar, zebra, iceskates, whale, and slinky. Currently, carol is assigned guitar, xena is assigned frisbee, vic is assigned necklace, heidi is assigned iceskates, dave is assigned slinky, michelle is assigned quadcopter, zoe is assigned whale, and alice is assigned zebra.", "question": "Is it possible to transition to a state where the action \"drive michelle and vic from quadcopter to frisbee\" can be applied?", "answer": "no"}
