{"id": 3605767053429137153, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c45 on board. The cars are at locations as follows: c14, c9, c38, c0, c41, c29, c28, c30, c46, c47, c8, and c27 are at l4; c18, c17, c1, c10, c36, c24, c44, and c16 are at l0; c31, c13, c43, c20, c7, c4, c42, c5, c12, c37, c25, c6, c49, and c23 are at l3; c19, c33, c15, c34, c48, c32, c2, and c11 are at l1; c21, c26, c40, c35, c39, c22, and c3 are at l2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car c29 is on board the ferry and The ferry is empty. B. Car c30 is at location c36. C. The ferry is at l3 location. D. The ferry is at c30 location and Car c2 is at location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c29 is on board the ferry and The ferry is empty", "Car c30 is at location c36", "The ferry is at l3 location", "The ferry is at c30 location and Car c2 is at location l1"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -3764045347364188946, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l4 location and it is empty. The cars are at locations as follows: c1 and c2 are at l3; c0 is at l4.", "question": "Which of the following options can hold in a state that can potentially be reached? A. There are no cars on the ferry and Car c0 is at location c2. B. Car c1 is at location c1. C. Car c2 is at location l1 and Car c2 is at location l2. D. The ferry is at l3 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["There are no cars on the ferry and Car c0 is at location c2", "Car c1 is at location c1", "Car c2 is at location l1 and Car c2 is at location l2", "The ferry is at l3 location"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -2051081572271352155, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l0, with the car c25 on board. The cars are at locations as follows: c18, c22, c2, c23, c17, c11, c20, c29, c35, c21, c0, c8, c19, c14, c13, c3, c6, c33, c44, c9, and c30 are at l0; c4, c28, c41, c12, c15, c40, c45, c42, c24, c39, c49, c7, c26, c16, c47, c27, c34, c5, c48, c10, c38, c43, c36, c46, c32, c31, c37, and c1 are at l1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The ferry is at c31 location. B. Car c25 is at location l1 and The ferry is at l1 location. C. Car c31 is at location l0 and Car c31 is at location l1. D. The ferry is at c5 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The ferry is at c31 location", "Car c25 is at location l1 and The ferry is at l1 location", "Car c31 is at location l0 and Car c31 is at location l1", "The ferry is at c5 location"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -3336074200616058544, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l3 location and it is empty. The cars are at locations as follows: c14, c9, c38, c0, c41, c29, c28, c30, c46, c47, c8, and c27 are at l4; c18, c17, c1, c10, c36, c45, c4, c42, c24, c44, c48, and c16 are at l0; c31, c13, c20, c34, c7, c5, c12, c37, c25, c6, c3, and c23 are at l3; c19, c33, c15, c32, c2, c49, and c11 are at l1; c21, c26, c40, c35, c39, c43, and c22 are at l2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car c20 is at location l3 and Car l3 is on the ferry. B. Car c46 is at location l1 and Car c46 is on board the ferry. C. Car c39 is on the ferry and The ferry is at l2 location. D. Ferry has car l1 on board and Car c45 is at location l0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c20 is at location l3 and Car l3 is on the ferry", "Car c46 is at location l1 and Car c46 is on board the ferry", "Car c39 is on the ferry and The ferry is at l2 location", "Ferry has car l1 on board and Car c45 is at location l0"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -4339646594736032462, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l2 location and it is empty. The cars are at locations as follows: c14, c35, c36, c38, c45, c0, c26, c29, c28, c30, c47, and c8 are at l4; c18, c40, c17, c41, c1, c10, c6, c24, c27, and c9 are at l0; c31, c13, c43, c20, c34, c7, c4, c42, c5, c12, c25, c37, c49, and c23 are at l3; c19, c33, c44, c15, c48, c46, c32, c2, and c11 are at l1; c21, c16, c39, c22, and c3 are at l2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car l3 is on the ferry. B. Car c26 is on board the ferry and The ferry is at l4 location. C. Car c24 is at location l0 and The ferry is at c42 location. D. The ferry is at l1 location and The ferry is at l4 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car l3 is on the ferry", "Car c26 is on board the ferry and The ferry is at l4 location", "Car c24 is at location l0 and The ferry is at c42 location", "The ferry is at l1 location and The ferry is at l4 location"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 7834509669496799127, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l1, with the car c2 on board. The cars are at locations as follows: c1 is at l3; c0 is at l4.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Ferry has car l4 on board. B. The ferry is at l1 location and The ferry is at l0 location. C. The ferry is empty and The ferry is at l4 location. D. The ferry is at c2 location and Car c0 is at location l4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ferry has car l4 on board", "The ferry is at l1 location and The ferry is at l0 location", "The ferry is empty and The ferry is at l4 location", "The ferry is at c2 location and Car c0 is at location l4"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -9026460909770995699, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 50 cars, numbered consecutively. Currently, the ferry is at l4, with the car c26 on board. The cars are at locations as follows: c14, c2, c35, c36, c38, c45, c0, c10, c20, c28, c47, and c27 are at l4; c18, c40, c11, c17, c41, c25, c1, c6, c9, and c16 are at l0; c31, c13, c39, c43, c29, c7, c4, c42, c5, c37, c3, c49, c23, and c15 are at l3; c19, c33, c44, c21, c24, c34, c48, c46, and c32 are at l1; c8, c30, c22, and c12 are at l2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car l0 is on board the ferry. B. Car l4 is on board the ferry. C. Car c45 is at location l4 and Car c45 is on the ferry. D. The ferry is at l1 location and Car c26 is at location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car l0 is on board the ferry", "Car l4 is on board the ferry", "Car c45 is at location l4 and Car c45 is on the ferry", "The ferry is at l1 location and Car c26 is at location l1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -698570149508129447, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 3 locations and 10 cars, numbered consecutively. Currently, the ferry is at l1, with the car c5 on board. The cars are at locations as follows: c0, c2, and c1 are at l1; c9, c4, and c7 are at l0; c8, c3, and c6 are at l2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The ferry is at c9 location and Car c6 is at location l2. B. The ferry is at l0 location and The ferry is at l2 location. C. Car l2 is on the ferry and Car c4 is at location l0. D. There are no cars on the ferry and Car c5 is at location l1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The ferry is at c9 location and Car c6 is at location l2", "The ferry is at l0 location and The ferry is at l2 location", "Car l2 is on the ferry and Car c4 is at location l0", "There are no cars on the ferry and Car c5 is at location l1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 6393147995374560141, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 2 locations and 50 cars, numbered consecutively. Currently, the ferry is at l1, with the car c38 on board. The cars are at locations as follows: c22, c2, c29, c11, c20, c40, c21, c0, c25, c8, c19, c12, c36, c14, c13, c6, c26, c44, c9, c30, and c37 are at l0; c4, c28, c33, c41, c15, c35, c18, c45, c42, c24, c39, c7, c3, c31, c23, c16, c47, c27, c34, c5, c48, c10, c43, c46, c32, c17, c49, and c1 are at l1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car c41 is at location l1 and The ferry is at c38 location. B. Car c21 is on board the ferry and Car c42 is on the ferry. C. Ferry has car c5 on board and Car c38 is at location l1. D. Car c10 is at location l1 and Car l1 is on the ferry.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c41 is at location l1 and The ferry is at c38 location", "Car c21 is on board the ferry and Car c42 is on the ferry", "Ferry has car c5 on board and Car c38 is at location l1", "Car c10 is at location l1 and Car l1 is on the ferry"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -5468068658442193148, "group": "reachable_atom_mc", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. There are 5 locations and 3 cars, numbered consecutively. Currently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c0 is at l0; c1 and c2 are at l3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Car c0 is at location l0 and Car c1 is at location c1. B. Ferry has car c1 on board and The ferry is at l3 location. C. Car c1 is at location l3 and Car c1 is on the ferry. D. The ferry is at c0 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Car c0 is at location l0 and Car c1 is at location c1", "Ferry has car c1 on board and The ferry is at l3 location", "Car c1 is at location l3 and Car c1 is on the ferry", "The ferry is at c0 location"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 6194891727821342417, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p2 and t0 are at l0-1, a0 is at l0-0, p3 and p1 are at l2-0, p4 and t1 are at l1-0, t2 is at l2-1, p0 is in a0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. p3 is at l2-1 and p3 is at l0-1. B. t1 is at l1-1. C. l0-2 is in p4. D. t1 is at l1-0 and t0 is at l2-2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p3 is at l2-1 and p3 is at l0-1", "t1 is at l1-1", "l0-2 is in p4", "t1 is at l1-0 and t0 is at l2-2"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4111531860873872566, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 50 locations across 5 cities. The locations are in cities as follows: l1-9, l1-6, l1-1, l1-4, l1-2, l1-7, l1-8, l1-0, l1-3, and l1-5 are in c1; l2-1, l2-3, l2-9, l2-0, l2-4, l2-6, l2-2, l2-5, l2-7, and l2-8 are in c2; l4-6, l4-5, l4-4, l4-0, l4-1, l4-3, l4-2, l4-8, l4-9, and l4-7 are in c4; l3-8, l3-0, l3-4, l3-1, l3-7, l3-5, l3-2, l3-9, l3-3, and l3-6 are in c3; l0-9, l0-3, l0-8, l0-5, l0-4, l0-0, l0-7, l0-6, l0-2, and l0-1 are in c0. Currently, p2 and t4 are at l4-8, t1, p0, and a0 are at l1-0, p1 and t2 are at l2-7, t3 is at l3-0, t0 is at l0-2, p3 is in t1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. l2-8 is in t4 and t4 is at l4-8. B. l4-5 is at l0-9. C. p1 is at l2-5 and p1 is at l4-0. D. t1 is at l1-8 and t2 is at l2-5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["l2-8 is in t4 and t4 is at l4-8", "l4-5 is at l0-9", "p1 is at l2-5 and p1 is at l4-0", "t1 is at l1-8 and t2 is at l2-5"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 5280329106889876102, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, p4, t1, p0, and a0 are at l1-0, t2 is at l2-1, t0 is at l0-2, p1 and p3 are in a0, p2 is in t0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. t1 is at l1-2. B. t0 is in l0-1. C. t1 is at l1-0 and t0 is in l2-1. D. p0 is in t1 and p0 is in t0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["t1 is at l1-2", "t0 is in l0-1", "t1 is at l1-0 and t0 is in l2-1", "p0 is in t1 and p0 is in t0"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -3326286317526361133, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, a0 is at l0-0, p1 and t1 are at l1-1, t0 is at l0-1, p3 is in a0, p0 and p2 are in t0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. t1 is in t1. B. p2 is in l1-1. C. p0 is at l0-0 and p0 is in t1. D. p2 is at l0-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["t1 is in t1", "p2 is in l1-1", "p0 is at l0-0 and p0 is in t1", "p2 is at l0-1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 788178444313684963, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, p1, p3, and p0 are at l1-1, t1 and a0 are at l1-0, p2 and t0 are at l0-0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. p2 is in t0. B. l1-1 is at p3. C. c1 is at t0. D. p1 is at l0-1 and p1 is in a0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p2 is in t0", "l1-1 is at p3", "c1 is at t0", "p1 is at l0-1 and p1 is in a0"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 6283080294290357241, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, p2, p0, and t0 are at l0-1, a0 is at l0-0, t1 is at l1-0, p3 is in a0, p1 is in t1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. p1 is in p1. B. p0 is in t0 and p0 is in a0. C. t1 is at l1-1 and a0 is at l1-0. D. t1 is at l1-0 and t0 is at p2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p1 is in p1", "p0 is in t0 and p0 is in a0", "t1 is at l1-1 and a0 is at l1-0", "t1 is at l1-0 and t0 is at p2"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 2421713025018886959, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l4-1, l4-2, and l4-0 are in c4; l2-2, l2-0, and l2-1 are in c2; l0-1, l0-0, and l0-2 are in c0; l3-0, l3-2, and l3-1 are in c3. Currently, t1 is at l1-2, p1 is at l3-1, t4 and a0 are at l4-0, t3 is at l3-0, t0 is at l0-2, t2 is at l2-0, p2 is in t2, p0 and p3 are in a0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. c2 is at c4 and t1 is at l1-2. B. p0 is at l3-2 and p0 is in a0. C. t1 is at l1-1 and t3 is at l3-1. D. l2-1 is at p1 and t0 is at l0-2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["c2 is at c4 and t1 is at l1-2", "p0 is at l3-2 and p0 is in a0", "t1 is at l1-1 and t3 is at l3-1", "l2-1 is at p1 and t0 is at l0-2"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -3576794639122596064, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 2 trucks and 1 airplane, as well as 4 packages. There are 4 locations across 2 cities. The locations are in cities as follows: l0-1 and l0-0 are in c0; l1-1 and l1-0 are in c1. Currently, p2 and t0 are at l0-1, a0 is at l0-0, t1 is at l1-0, p3 is in a0, p0 is in t0, p1 is in t1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. p3 is in a0 and p3 is in t0. B. p0 is at l0-1. C. t1 is at l1-0 and l1-0 is in c1. D. c0 is at a0 and p1 is in t1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p3 is in a0 and p3 is in t0", "p0 is at l0-1", "t1 is at l1-0 and l1-0 is in c1", "c0 is at a0 and p1 is in t1"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 2323768416669865391, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 5 trucks and 1 airplane, as well as 4 packages. There are 15 locations across 5 cities. The locations are in cities as follows: l1-0, l1-1, and l1-2 are in c1; l4-1, l4-2, and l4-0 are in c4; l2-2, l2-0, and l2-1 are in c2; l0-1, l0-0, and l0-2 are in c0; l3-0, l3-2, and l3-1 are in c3. Currently, p2 and t2 are at l2-1, p3 is at l4-1, t4 is at l4-0, t1 and a0 are at l1-0, t3 is at l3-0, t0 is at l0-2, p1 is in t3, p0 is in a0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. l2-2 is at l1-0. B. l2-0 is at p2 and a0 is at l1-0. C. p0 is in t1 and p0 is in t2. D. a0 is at l0-0 and t0 is at l0-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["l2-2 is at l1-0", "l2-0 is at p2 and a0 is at l1-0", "p0 is in t1 and p0 is in t2", "a0 is at l0-0 and t0 is at l0-0"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 8659425604451728577, "group": "reachable_atom_mc", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. There are 3 trucks and 1 airplane, as well as 5 packages. There are 9 locations across 3 cities. The locations are in cities as follows: l2-0, l2-2, and l2-1 are in c2; l1-1, l1-2, and l1-0 are in c1; l0-0, l0-2, and l0-1 are in c0. Currently, t1 and p0 are at l1-2, p1 and a0 are at l1-0, t2 is at l2-1, p4 and t0 are at l0-0, p2 and p3 are in a0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. p4 is in t2 and p4 is in t1. B. c2 is at p3 and a0 is at l1-0. C. l1-0 is in l0-2 and t1 is at l1-2. D. p2 is at l1-0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["p4 is in t2 and p4 is in t1", "c2 is at p3 and a0 is at l1-0", "l1-0 is in l0-2 and t1 is at l1-2", "p2 is at l1-0"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 6778983232697869830, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_7. The following block(s) are on the table: block_8, block_10, block_6, and block_2. The following block(s) are stacked on top of another block: block_4 is on block_9, block_3 is on block_4, block_5 is on block_2, block_1 is on block_8, and block_9 is on block_5.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robotic arm is holding block_5 and The robotic arm is holding block_7. B. The block block_4 is currently situated under the block block_10 and Block block_4 is clear. C. The block block_2 is on top of block block_3 and The block block_3 is currently situated under the block block_7. D. The block block_7 is on top of block block_10 and The block block_1 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robotic arm is holding block_5 and The robotic arm is holding block_7", "The block block_4 is currently situated under the block block_10 and Block block_4 is clear", "The block block_2 is on top of block block_3 and The block block_3 is currently situated under the block block_7", "The block block_7 is on top of block block_10 and The block block_1 is currently being held by the robotic arm"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -62621284872717015, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_6. The following block(s) are on the table: block_8, block_10, block_2, and block_1. The following block(s) are stacked on top of another block: block_4 is on block_9, block_3 is on block_7, block_5 is on block_2, block_7 is on block_10, and block_9 is on block_5.", "question": "Which of the following options can hold in a state that can potentially be reached? A. block_9 is not obstructed by any other blocks and Block block_6 is clear. B. The block block_6 is currently situated under the block block_3 and The block block_8 is on top of block block_6. C. The robotic arm is holding block_10 and The robotic arm is empty. D. The robotic arm is holding block_6 and The block block_10 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["block_9 is not obstructed by any other blocks and Block block_6 is clear", "The block block_6 is currently situated under the block block_3 and The block block_8 is on top of block block_6", "The robotic arm is holding block_10 and The robotic arm is empty", "The robotic arm is holding block_6 and The block block_10 is currently being held by the robotic arm"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -2212005186588612364, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is holding block_3. The following block(s) are on the table: block_8, block_10, block_6, and block_2. The following block(s) are stacked on top of another block: block_5 is on block_9, block_4 is on block_5, block_9 is on block_1, block_1 is on block_8, and block_7 is on block_10.", "question": "Which of the following options can hold in a state that can potentially be reached? A. block_5 is not obstructed by any other blocks and Block block_3 is on the table. B. The block block_8 is currently situated above the block block_7 and Block block_8 is located on the table. C. The block block_8 is on top of block block_1 and No blocks are placed on top of block_1. D. The block block_1 is on top of block block_4 and The block block_4 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["block_5 is not obstructed by any other blocks and Block block_3 is on the table", "The block block_8 is currently situated above the block block_7 and Block block_8 is located on the table", "The block block_8 is on top of block block_1 and No blocks are placed on top of block_1", "The block block_1 is on top of block block_4 and The block block_4 is currently being held by the robotic arm"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 4669369140635682769, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is holding block_15. The following block(s) are on the table: block_6, block_20, block_9, block_10, and block_13. The following block(s) are stacked on top of another block: block_1 is on block_17, block_3 is on block_13, block_8 is on block_4, block_7 is on block_10, block_16 is on block_11, block_4 is on block_6, block_17 is on block_12, block_2 is on block_14, block_19 is on block_16, block_11 is on block_8, block_14 is on block_20, block_18 is on block_5, block_12 is on block_7, and block_5 is on block_1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_4 is on top of block block_13 and The robotic arm is holding block_4. B. block_14 is not obstructed by any other blocks and The block block_2 is currently being held by the robotic arm. C. The block block_19 is on top of block block_20 and The block block_6 is currently situated under the block block_19. D. The robotic arm is holding block_4 and The robotic arm is holding block_3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_4 is on top of block block_13 and The robotic arm is holding block_4", "block_14 is not obstructed by any other blocks and The block block_2 is currently being held by the robotic arm", "The block block_19 is on top of block block_20 and The block block_6 is currently situated under the block block_19", "The robotic arm is holding block_4 and The robotic arm is holding block_3"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -3299265913143099877, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 3 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_3 and block_2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. block_1 is not obstructed by any other blocks and Block block_1 is on the table. B. The block block_1 is currently situated under the block block_1 and The block block_1 is currently being held by the robotic arm. C. The block block_3 is currently situated under the block block_3 and The robotic arm is holding block_3. D. The block block_2 is on top of block block_2 and Block block_2 is on the table.", "choices": {"label": ["A", "B", "C", "D"], "text": ["block_1 is not obstructed by any other blocks and Block block_1 is on the table", "The block block_1 is currently situated under the block block_1 and The block block_1 is currently being held by the robotic arm", "The block block_3 is currently situated under the block block_3 and The robotic arm is holding block_3", "The block block_2 is on top of block block_2 and Block block_2 is on the table"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 8516534063391446966, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 20 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_18, block_14, block_7, block_6, block_1, block_20, block_12, block_10, and block_16. The following block(s) are stacked on top of another block: block_2 is on block_18, block_4 is on block_11, block_11 is on block_1, block_3 is on block_13, block_9 is on block_2, block_5 is on block_14, block_15 is on block_9, block_17 is on block_12, block_13 is on block_4, block_19 is on block_16, and block_8 is on block_7.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_2 is currently situated above the block block_20 and The block block_15 is currently situated above the block block_20. B. The block block_20 is currently situated under the block block_8 and No blocks are placed on top of block_7. C. The block block_2 is currently situated under the block block_6 and The block block_2 is on top of block block_2. D. The block block_15 is on top of block block_18 and The block block_10 is currently situated above the block block_18.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_2 is currently situated above the block block_20 and The block block_15 is currently situated above the block block_20", "The block block_20 is currently situated under the block block_8 and No blocks are placed on top of block_7", "The block block_2 is currently situated under the block block_6 and The block block_2 is on top of block block_2", "The block block_15 is on top of block block_18 and The block block_10 is currently situated above the block block_18"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -677443437841026435, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_4. The following block(s) are on the table: block_3 and block_5. The following block(s) are stacked on top of another block: block_1 is on block_3 and block_2 is on block_1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Block block_1 is clear and The block block_5 is currently situated under the block block_4. B. The block block_2 is on top of block block_4 and Block block_2 is located on the table. C. The block block_1 is currently being held by the robotic arm and The robotic arm is empty. D. The block block_3 is currently situated under the block block_2 and The block block_3 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Block block_1 is clear and The block block_5 is currently situated under the block block_4", "The block block_2 is on top of block block_4 and Block block_2 is located on the table", "The block block_1 is currently being held by the robotic arm and The robotic arm is empty", "The block block_3 is currently situated under the block block_2 and The block block_3 is currently being held by the robotic arm"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -5338147461821950878, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_3, block_5, and block_1. The following block(s) are stacked on top of another block: block_4 is on block_5 and block_2 is on block_3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_4 is currently situated above the block block_1 and The block block_4 is currently being held by the robotic arm. B. The robotic arm is holding block_4 and The robotic arm is not holding anything. C. The block block_3 is currently being held by the robotic arm and The robotic arm is not holding anything. D. No blocks are placed on top of block_3 and The block block_1 is currently situated under the block block_2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_4 is currently situated above the block block_1 and The block block_4 is currently being held by the robotic arm", "The robotic arm is holding block_4 and The robotic arm is not holding anything", "The block block_3 is currently being held by the robotic arm and The robotic arm is not holding anything", "No blocks are placed on top of block_3 and The block block_1 is currently situated under the block block_2"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 6521555625121049564, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 5 blocks. Currently, the robotic arm is holding block_1. The following block(s) are on the table: block_4 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_5 and block_3 is on block_4.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The block block_1 is currently being held by the robotic arm and The robotic arm is not holding anything. B. The block block_3 is on top of block block_2 and The block block_2 is currently being held by the robotic arm. C. block_1 is not obstructed by any other blocks and The block block_2 is currently situated under the block block_1. D. The block block_2 is currently being held by the robotic arm and The robotic arm is empty.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The block block_1 is currently being held by the robotic arm and The robotic arm is not holding anything", "The block block_3 is on top of block block_2 and The block block_2 is currently being held by the robotic arm", "block_1 is not obstructed by any other blocks and The block block_2 is currently situated under the block block_1", "The block block_2 is currently being held by the robotic arm and The robotic arm is empty"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 3200330902639271941, "group": "reachable_atom_mc", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. There are 10 blocks. Currently, the robotic arm is empty. The following block(s) are on the table: block_4, block_8, block_10, block_6, and block_2. The following block(s) are stacked on top of another block: block_7 is on block_10, block_3 is on block_7, block_9 is on block_1, block_5 is on block_2, and block_1 is on block_8.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robotic arm is holding block_10 and The robotic arm is holding block_5. B. The block block_7 is currently situated under the block block_2 and block_7 is not obstructed by any other blocks. C. The block block_9 is currently situated under the block block_4. D. The robotic arm is holding block_2 and The block block_1 is currently being held by the robotic arm.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robotic arm is holding block_10 and The robotic arm is holding block_5", "The block block_7 is currently situated under the block block_2 and block_7 is not obstructed by any other blocks", "The block block_9 is currently situated under the block block_4", "The robotic arm is holding block_2 and The block block_1 is currently being held by the robotic arm"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -989245294227982764, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f0-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock, f3-4f has shape1 shaped lock. Key key1-0 is at position f1-2f. Key key1-1 is at position f3-0f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Key key1-0 is at f0-4f location and Key key1-0 is at f1-1f location. B. Robot is at f0-2f location. C. Location f2-3f is locked. D. Robot is at shape0 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key1-0 is at f0-4f location and Key key1-0 is at f1-1f location", "Robot is at f0-2f location", "Location f2-3f is locked", "Robot is at shape0 location"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 8596659750862324339, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f0-2f and its arm is empty. All the positions are open except the following: f0-1f has shape0 shaped lock. Key key0-0 is at position f3-1f. Key key0-1 is at position f4-1f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Location f2-4f is locked. B. Robot is at f3-2f location and Robot is at f3-1f location. C. Robot is at f0-4f location. D. Location f4-3f is open and Location f2-3f is locked.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Location f2-4f is locked", "Robot is at f3-2f location and Robot is at f3-1f location", "Robot is at f0-4f location", "Location f4-3f is open and Location f2-3f is locked"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 8365064581165078754, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f4-1f and its arm is empty. All the positions are open except the following: f2-1f has shape0 shaped lock, f0-1f has shape0 shaped lock. Key key0-0 is at position f3-1f. Key key0-1 is at position f3-1f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Key f1-3f is at f4-1f location. B. Location f4-1f is locked. C. Robot is holding key0-0 and Robot is at f3-1f location. D. Key key0-1 is at f1-4f location and Robot is holding key0-1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key f1-3f is at f4-1f location", "Location f4-1f is locked", "Robot is holding key0-0 and Robot is at f3-1f location", "Key key0-1 is at f1-4f location and Robot is holding key0-1"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -9079134825059331541, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f0-2f and its arm is empty. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-0 is at position f1-3f. Key key0-1 is at position f1-2f. Key key0-2 is at position f3-3f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot is at f0-4f location. B. Location f2-3f is locked. C. Robot is holding key0-1 and Robot is holding key0-2. D. Robot is at key0-1 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f0-4f location", "Location f2-3f is locked", "Robot is holding key0-1 and Robot is holding key0-2", "Robot is at key0-1 location"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -7789321838638536209, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 2 different shapes: Key key1-1 is of shape shape1, Key key1-0 is of shape shape1.  Currently, the robot is at position f4-4f and its arm is empty. All the positions are open except the following: f2-1f has shape1 shaped lock. Key key1-1 is at position f4-4f. Key key1-0 is at position f1-0f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Location f2-0f is open and Location f2-3f is locked. B. Location key1-0 is open. C. Robot is at f3-3f location. D. Robot is holding key1-0 and Robot's arm is empty.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Location f2-0f is open and Location f2-3f is locked", "Location key1-0 is open", "Robot is at f3-3f location", "Robot is holding key1-0 and Robot's arm is empty"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -3967746676511258343, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-3f and is holding key0-2. All the positions are open except the following: f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock, f4-4f has shape0 shaped lock. Key key0-1 is at position f1-2f. Key key0-0 is at position f0-1f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Key key0-1 is at f0-2f location and Key key0-1 is at f2-0f location. B. Location f3-2f is locked. C. Robot is at f0-4f location. D. Location shape0 is locked.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-1 is at f0-2f location and Key key0-1 is at f2-0f location", "Location f3-2f is locked", "Robot is at f0-4f location", "Location shape0 is locked"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -4583866225020199652, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-1 is of shape shape0, Key key0-3 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f4-1f and is holding key0-3. All the positions are open except the following: f4-0f has shape0 shaped lock, f0-3f has shape0 shaped lock, f0-2f has shape0 shaped lock, f0-0f has shape0 shaped lock. Key key0-0 is at position f0-0f. Key key0-1 is at position f4-2f. Key key0-2 is at position f3-0f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot is at f4-2f location and Key key0-3 is at f4-2f location. B. Location f3-1f is open and Location f1-1f is locked. C. Key key0-0 is at f0-4f location and Key key0-0 is at f2-0f location. D. Location f4-4f is locked.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f4-2f location and Key key0-3 is at f4-2f location", "Location f3-1f is open and Location f1-1f is locked", "Key key0-0 is at f0-4f location and Key key0-0 is at f2-0f location", "Location f4-4f is locked"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -2068590970820042144, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 4 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-1 is of shape shape0, Key key0-3 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f2-4f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f0-2f has shape0 shaped lock. Key key0-2 is at position f4-0f. Key key0-3 is at position f0-1f. Key key0-1 is at position f4-2f. Key key0-0 is at position f0-1f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot is at f4-4f location. B. Location f1-1f is locked. C. Robot is at f4-0f location and Robot is at f4-2f location. D. Location f2-0f is locked.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot is at f4-4f location", "Location f1-1f is locked", "Robot is at f4-0f location and Robot is at f4-2f location", "Location f2-0f is locked"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 8066425899228182667, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 3 keys in 1 different shapes: Key key0-2 is of shape shape0, Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f2-4f and is holding key0-0. All the positions are open except the following: f2-0f has shape0 shaped lock, f4-2f has shape0 shaped lock. Key key0-1 is at position f2-2f. Key key0-2 is at position f0-4f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Key key0-0 is at f2-4f location and Robot is at f3-4f location. B. Location f2-4f is locked and Location f4-1f is open. C. Robot is at f4-1f location and Robot is at f1-0f location. D. Location f2-3f is locked.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Key key0-0 is at f2-4f location and Robot is at f3-4f location", "Location f2-4f is locked and Location f4-1f is open", "Robot is at f4-1f location and Robot is at f1-0f location", "Location f2-3f is locked"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -4387882018975386720, "group": "reachable_atom_mc", "context": "A robot is in a grid and can only move to places that are connected to its current position. The grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  There are 2 keys in 1 different shapes: Key key0-1 is of shape shape0, Key key0-0 is of shape shape0.  Currently, the robot is at position f1-2f and is holding key0-0. All the positions are open except the following: f2-1f has shape0 shaped lock, f0-1f has shape0 shaped lock. Key key0-1 is at position f4-1f.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Location f4-0f is locked. B. Robot is at f1-1f location and Robot's arm is empty. C. Key key0-0 is at f2-1f location and Key key0-0 is at f3-0f location. D. Robot is at shape0 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Location f4-0f is locked", "Robot is at f1-1f location and Robot's arm is empty", "Key key0-0 is at f2-1f location and Key key0-0 is at f3-0f location", "Robot is at shape0 location"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -3652362225546767274, "group": "reachable_atom_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 3 robots and 20 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_20 is to the right of tile_19, tile_8 is to the right of tile_7, tile_10 is to the right of tile_9, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_19 is to the right of tile_18, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, tile_14 is to the right of tile_13, tile_13 is to the right of tile_12, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_17 is to the right of tile_16, tile_12 is to the right of tile_11, and tile_4 is to the right of tile_3. Further, tile_15 is down from tile_20, tile_12 is down from tile_17, tile_1 is down from tile_6, tile_4 is down from tile_9, tile_14 is down from tile_19, tile_13 is down from tile_18, tile_9 is down from tile_14, tile_10 is down from tile_15, tile_5 is down from tile_10, tile_11 is down from tile_16, tile_6 is down from tile_11, tile_3 is down from tile_8, tile_2 is down from tile_7, tile_7 is down from tile_12, and tile_8 is down from tile_13 Currently, robot robot3 is at tile_3 and holding color white, robot robot1 is at tile_5 and holding color white, and robot robot2 is at tile_9 and holding color black; tile_13, tile_1, tile_4, tile_8, and tile_2 are clear; tile_6 is painted white, tile_15 is painted black, tile_7 is painted black, tile_20 is painted white, tile_14 is painted white, tile_19 is painted black, tile_18 is painted white, tile_12 is painted white, tile_17 is painted black, tile_16 is painted white, tile_11 is painted black, and tile_10 is painted white.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot2 is at tile_4 location and tile_3 is clear. B. Tile tile_19 is painted in black color and tile_19 is clear. C. Robot robot2 is at tile_2 location and tile_2 is clear. D. Robot robot3 is at tile_6 location and Robot robot1 is at tile_6 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot2 is at tile_4 location and tile_3 is clear", "Tile tile_19 is painted in black color and tile_19 is clear", "Robot robot2 is at tile_2 location and tile_2 is clear", "Robot robot3 is at tile_6 location and Robot robot1 is at tile_6 location"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -8937768131717704983, "group": "reachable_atom_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_6 is to the right of tile_5. Further, tile_4 is down from tile_7, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_2 is down from tile_5, tile_5 is down from tile_8, and tile_1 is down from tile_4 Currently, robot robot1 is at tile_5 and holding color white and robot robot2 is at tile_3 and holding color white; tile_2, tile_1, and tile_8 are clear; tile_7 is painted black, tile_4 is painted white, tile_9 is painted black, and tile_6 is painted white.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot2 is holding white paint and Robot robot2 is holding black paint. B. Robot robot1 is holding black paint and Robot robot1 is holding white paint. C. Robot robot2 is holding black paint and Robot robot1 is at tile_2 location. D. Robot robot1 is at tile_8 location and Robot robot1 is at tile_2 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot2 is holding white paint and Robot robot2 is holding black paint", "Robot robot1 is holding black paint and Robot robot1 is holding white paint", "Robot robot2 is holding black paint and Robot robot1 is at tile_2 location", "Robot robot1 is at tile_8 location and Robot robot1 is at tile_2 location"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -6813696634323167835, "group": "reachable_atom_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_6 is to the right of tile_5. Further, tile_4 is down from tile_7, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_2 is down from tile_5, tile_5 is down from tile_8, and tile_1 is down from tile_4 Currently, robot robot1 is at tile_3 and holding color white and robot robot2 is at tile_4 and holding color black; tile_2, tile_1, tile_8, and tile_5 are clear; tile_7 is painted black, tile_9 is painted black, and tile_6 is painted white.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot2 is holding white paint and Robot robot2 is holding black paint. B. Robot robot1 is at tile_6 location and Robot robot2 is at tile_6 location. C. Robot robot2 is at tile_7 location and Robot robot2 is at tile_9 location. D. tile_3 is clear and Robot robot1 is at tile_2 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot2 is holding white paint and Robot robot2 is holding black paint", "Robot robot1 is at tile_6 location and Robot robot2 is at tile_6 location", "Robot robot2 is at tile_7 location and Robot robot2 is at tile_9 location", "tile_3 is clear and Robot robot1 is at tile_2 location"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -8741889273004308207, "group": "reachable_atom_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 9 tiles. The tiles locations are: tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, and tile_6 is to the right of tile_5. Further, tile_4 is down from tile_7, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_2 is down from tile_5, tile_5 is down from tile_8, and tile_1 is down from tile_4 Currently, robot robot1 is at tile_6 and holding color black and robot robot2 is at tile_1 and holding color white; tile_2, tile_8, tile_4, tile_3, and tile_5 are clear; tile_7 is painted black and tile_9 is painted black.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is holding black paint and Robot robot1 is holding white paint. B. Robot robot2 is at tile_4 location and tile_1 is clear. C. Robot robot1 is at tile_5 location and tile_5 is clear. D. Robot robot2 is at tile_8 location and Robot robot2 is at tile_4 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is holding black paint and Robot robot1 is holding white paint", "Robot robot2 is at tile_4 location and tile_1 is clear", "Robot robot1 is at tile_5 location and tile_5 is clear", "Robot robot2 is at tile_8 location and Robot robot2 is at tile_4 location"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -6926510244463077524, "group": "reachable_atom_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 24 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_2 is to the right of tile_1, tile_24 is to the right of tile_23, tile_16 is to the right of tile_15, tile_6 is to the right of tile_5, tile_14 is to the right of tile_13, tile_15 is to the right of tile_14, tile_17 is to the right of tile_16, tile_4 is to the right of tile_3, tile_21 is to the right of tile_20, tile_20 is to the right of tile_19, tile_8 is to the right of tile_7, tile_23 is to the right of tile_22, tile_3 is to the right of tile_2, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, and tile_22 is to the right of tile_21. Further, tile_2 is down from tile_8, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_13 is down from tile_19, tile_17 is down from tile_23, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_12 is down from tile_18, tile_3 is down from tile_9, tile_7 is down from tile_13, tile_4 is down from tile_10, tile_6 is down from tile_12, tile_8 is down from tile_14, tile_9 is down from tile_15, tile_14 is down from tile_20, tile_16 is down from tile_22, tile_5 is down from tile_11, and tile_11 is down from tile_17 Currently, robot robot1 is at tile_6 and holding color white and robot robot2 is at tile_15 and holding color black; tile_1, tile_16, tile_4, tile_11, tile_22, tile_2, tile_17, tile_10, tile_5, tile_12, tile_7, tile_9, tile_21, and tile_3 are clear; tile_19 is painted white, tile_24 is painted black, tile_8 is painted black, tile_14 is painted white, tile_18 is painted white, tile_13 is painted black, tile_20 is painted black, and tile_23 is painted white.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is at tile_17 location and Tile tile_17 is painted in white color. B. Robot robot2 is at tile_21 location and tile_15 is clear. C. Robot robot1 is at tile_11 location and Robot robot2 is at tile_11 location. D. Robot robot1 is at tile_9 location and tile_9 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is at tile_17 location and Tile tile_17 is painted in white color", "Robot robot2 is at tile_21 location and tile_15 is clear", "Robot robot1 is at tile_11 location and Robot robot2 is at tile_11 location", "Robot robot1 is at tile_9 location and tile_9 is clear"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 6564489968027261047, "group": "reachable_atom_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 24 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_2 is to the right of tile_1, tile_24 is to the right of tile_23, tile_16 is to the right of tile_15, tile_6 is to the right of tile_5, tile_14 is to the right of tile_13, tile_15 is to the right of tile_14, tile_17 is to the right of tile_16, tile_4 is to the right of tile_3, tile_21 is to the right of tile_20, tile_20 is to the right of tile_19, tile_8 is to the right of tile_7, tile_23 is to the right of tile_22, tile_3 is to the right of tile_2, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, and tile_22 is to the right of tile_21. Further, tile_2 is down from tile_8, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_13 is down from tile_19, tile_17 is down from tile_23, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_12 is down from tile_18, tile_3 is down from tile_9, tile_7 is down from tile_13, tile_4 is down from tile_10, tile_6 is down from tile_12, tile_8 is down from tile_14, tile_9 is down from tile_15, tile_14 is down from tile_20, tile_16 is down from tile_22, tile_5 is down from tile_11, and tile_11 is down from tile_17 Currently, robot robot1 is at tile_5 and holding color black and robot robot2 is at tile_9 and holding color black; tile_13, tile_15, tile_1, tile_16, tile_4, tile_11, tile_8, tile_2, tile_18, tile_10, tile_12, tile_6, tile_7, tile_21, tile_3, and tile_14 are clear; tile_19 is painted white, tile_24 is painted black, tile_22 is painted black, tile_17 is painted black, tile_20 is painted black, and tile_23 is painted white.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is at tile_18 location and Tile tile_18 is painted in white color. B. Robot robot2 is at tile_3 location and tile_3 is clear. C. tile_9 is clear and Robot robot1 is at tile_4 location. D. Robot robot2 is at tile_8 location and Robot robot2 is at tile_13 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is at tile_18 location and Tile tile_18 is painted in white color", "Robot robot2 is at tile_3 location and tile_3 is clear", "tile_9 is clear and Robot robot1 is at tile_4 location", "Robot robot2 is at tile_8 location and Robot robot2 is at tile_13 location"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 8065519993002672884, "group": "reachable_atom_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 24 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_2 is to the right of tile_1, tile_24 is to the right of tile_23, tile_16 is to the right of tile_15, tile_6 is to the right of tile_5, tile_14 is to the right of tile_13, tile_15 is to the right of tile_14, tile_17 is to the right of tile_16, tile_4 is to the right of tile_3, tile_21 is to the right of tile_20, tile_20 is to the right of tile_19, tile_8 is to the right of tile_7, tile_23 is to the right of tile_22, tile_3 is to the right of tile_2, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, and tile_22 is to the right of tile_21. Further, tile_2 is down from tile_8, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_13 is down from tile_19, tile_17 is down from tile_23, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_12 is down from tile_18, tile_3 is down from tile_9, tile_7 is down from tile_13, tile_4 is down from tile_10, tile_6 is down from tile_12, tile_8 is down from tile_14, tile_9 is down from tile_15, tile_14 is down from tile_20, tile_16 is down from tile_22, tile_5 is down from tile_11, and tile_11 is down from tile_17 Currently, robot robot1 is at tile_1 and holding color black and robot robot2 is at tile_11 and holding color black; tile_13, tile_15, tile_16, tile_4, tile_2, tile_17, tile_10, tile_5, tile_6, tile_23, tile_7, tile_9, and tile_3 are clear; tile_19 is painted white, tile_24 is painted black, tile_8 is painted black, tile_14 is painted white, tile_22 is painted black, tile_18 is painted white, tile_12 is painted black, tile_20 is painted black, and tile_21 is painted white.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Tile tile_11 is painted in white color and Tile tile_11 is painted in black color. B. Robot robot2 is at tile_4 location and tile_11 is clear. C. Tile tile_23 is painted in white color and Tile tile_23 is painted in black color. D. Robot robot1 is at tile_2 location and Tile tile_2 is painted in white color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Tile tile_11 is painted in white color and Tile tile_11 is painted in black color", "Robot robot2 is at tile_4 location and tile_11 is clear", "Tile tile_23 is painted in white color and Tile tile_23 is painted in black color", "Robot robot1 is at tile_2 location and Tile tile_2 is painted in white color"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 9025597115780380573, "group": "reachable_atom_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 20 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_20 is to the right of tile_19, tile_8 is to the right of tile_7, tile_10 is to the right of tile_9, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_19 is to the right of tile_18, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, tile_14 is to the right of tile_13, tile_13 is to the right of tile_12, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_17 is to the right of tile_16, tile_12 is to the right of tile_11, and tile_4 is to the right of tile_3. Further, tile_15 is down from tile_20, tile_12 is down from tile_17, tile_1 is down from tile_6, tile_4 is down from tile_9, tile_14 is down from tile_19, tile_13 is down from tile_18, tile_9 is down from tile_14, tile_10 is down from tile_15, tile_5 is down from tile_10, tile_11 is down from tile_16, tile_6 is down from tile_11, tile_3 is down from tile_8, tile_2 is down from tile_7, tile_7 is down from tile_12, and tile_8 is down from tile_13 Currently, robot robot1 is at tile_15 and holding color black and robot robot2 is at tile_3 and holding color white; tile_13, tile_1, tile_4, tile_19, tile_11, tile_8, tile_2, tile_17, tile_10, tile_5, tile_12, tile_6, tile_7, tile_9, and tile_14 are clear; tile_20 is painted white, tile_18 is painted white, and tile_16 is painted white.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is at tile_18 location and Robot robot1 is at tile_3 location. B. Robot robot2 is at tile_1 location and Robot robot2 is at tile_5 location. C. Robot robot1 is holding white paint and Robot robot1 is holding black paint. D. Robot robot2 is at tile_4 location and tile_3 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is at tile_18 location and Robot robot1 is at tile_3 location", "Robot robot2 is at tile_1 location and Robot robot2 is at tile_5 location", "Robot robot1 is holding white paint and Robot robot1 is holding black paint", "Robot robot2 is at tile_4 location and tile_3 is clear"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -810933788724466081, "group": "reachable_atom_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 3 robots and 20 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_20 is to the right of tile_19, tile_8 is to the right of tile_7, tile_10 is to the right of tile_9, tile_2 is to the right of tile_1, tile_9 is to the right of tile_8, tile_19 is to the right of tile_18, tile_3 is to the right of tile_2, tile_5 is to the right of tile_4, tile_14 is to the right of tile_13, tile_13 is to the right of tile_12, tile_7 is to the right of tile_6, tile_15 is to the right of tile_14, tile_17 is to the right of tile_16, tile_12 is to the right of tile_11, and tile_4 is to the right of tile_3. Further, tile_15 is down from tile_20, tile_12 is down from tile_17, tile_1 is down from tile_6, tile_4 is down from tile_9, tile_14 is down from tile_19, tile_13 is down from tile_18, tile_9 is down from tile_14, tile_10 is down from tile_15, tile_5 is down from tile_10, tile_11 is down from tile_16, tile_6 is down from tile_11, tile_3 is down from tile_8, tile_2 is down from tile_7, tile_7 is down from tile_12, and tile_8 is down from tile_13 Currently, robot robot3 is at tile_10 and holding color black, robot robot1 is at tile_1 and holding color white, and robot robot2 is at tile_8 and holding color black; tile_13, tile_15, tile_4, tile_11, tile_2, tile_18, tile_5, tile_12, tile_6, tile_7, tile_9, tile_3, and tile_14 are clear; tile_20 is painted white, tile_19 is painted black, tile_17 is painted black, and tile_16 is painted white.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot2 is at tile_1 location and Robot robot2 is at tile_18 location. B. Robot robot1 is at tile_18 location and Robot robot3 is at tile_18 location. C. Robot robot1 is holding black paint and Robot robot1 is holding white paint. D. Robot robot1 is holding black paint and Robot robot2 is at tile_7 location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot2 is at tile_1 location and Robot robot2 is at tile_18 location", "Robot robot1 is at tile_18 location and Robot robot3 is at tile_18 location", "Robot robot1 is holding black paint and Robot robot1 is holding white paint", "Robot robot1 is holding black paint and Robot robot2 is at tile_7 location"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -5621261155514277887, "group": "reachable_atom_mc", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. There are 2 robots and 24 tiles. The tiles locations are: tile_18 is to the right of tile_17, tile_2 is to the right of tile_1, tile_24 is to the right of tile_23, tile_16 is to the right of tile_15, tile_6 is to the right of tile_5, tile_14 is to the right of tile_13, tile_15 is to the right of tile_14, tile_17 is to the right of tile_16, tile_4 is to the right of tile_3, tile_21 is to the right of tile_20, tile_20 is to the right of tile_19, tile_8 is to the right of tile_7, tile_23 is to the right of tile_22, tile_3 is to the right of tile_2, tile_11 is to the right of tile_10, tile_12 is to the right of tile_11, tile_10 is to the right of tile_9, tile_9 is to the right of tile_8, tile_5 is to the right of tile_4, and tile_22 is to the right of tile_21. Further, tile_2 is down from tile_8, tile_15 is down from tile_21, tile_10 is down from tile_16, tile_13 is down from tile_19, tile_17 is down from tile_23, tile_1 is down from tile_7, tile_18 is down from tile_24, tile_12 is down from tile_18, tile_3 is down from tile_9, tile_7 is down from tile_13, tile_4 is down from tile_10, tile_6 is down from tile_12, tile_8 is down from tile_14, tile_9 is down from tile_15, tile_14 is down from tile_20, tile_16 is down from tile_22, tile_5 is down from tile_11, and tile_11 is down from tile_17 Currently, robot robot1 is at tile_5 and holding color white and robot robot2 is at tile_1 and holding color black; tile_13, tile_16, tile_4, tile_2, tile_10, tile_6, tile_7, tile_9, and tile_3 are clear; tile_19 is painted white, tile_24 is painted black, tile_15 is painted black, tile_11 is painted white, tile_8 is painted black, tile_14 is painted white, tile_22 is painted black, tile_18 is painted white, tile_12 is painted black, tile_17 is painted black, tile_20 is painted black, tile_21 is painted white, and tile_23 is painted white.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot2 is at tile_6 location and tile_6 is clear. B. Tile tile_8 is painted in black color and Tile tile_8 is painted in white color. C. Robot robot2 is at tile_6 location and Robot robot1 is at tile_6 location. D. Tile tile_7 is painted in black color.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot2 is at tile_6 location and tile_6 is clear", "Tile tile_8 is painted in black color and Tile tile_8 is painted in white color", "Robot robot2 is at tile_6 location and Robot robot1 is at tile_6 location", "Tile tile_7 is painted in black color"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -7680773247890956307, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, left gripper is carrying the ball ball7, and right gripper is carrying the ball ball6. Additionally, ball1, ball2, ball4, and ball3 are at room1, ball5 is at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Ball left1 is in room room2. B. Robot robot1 is carrying the ball ball3 in the left gripper and The left gripper of robot robot1 is free. C. Robot robot1 is at room1 location. D. Ball right1 is in room room1 and Robot robot1 is carrying the ball ball7 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball left1 is in room room2", "Robot robot1 is carrying the ball ball3 in the left gripper and The left gripper of robot robot1 is free", "Robot robot1 is at room1 location", "Ball right1 is in room room1 and Robot robot1 is carrying the ball ball7 in the left gripper"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 76490776834059326, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room3, right gripper is free, and left gripper is carrying the ball ball10. Additionally, ball1, ball3, ball13, and ball12 are at room2, ball2, ball6, ball9, ball5, ball11, ball4, and ball15 are at room1, ball8, ball7, and ball14 are at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball ball3 in the left gripper and The left gripper of robot robot1 is free. B. Robot robot1 is in room room1. C. Robot robot1 is carrying the ball left1 in the left gripper and Ball ball2 is at room1 location. D. Robot robot1 is carrying the ball room2 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball3 in the left gripper and The left gripper of robot robot1 is free", "Robot robot1 is in room room1", "Robot robot1 is carrying the ball left1 in the left gripper and Ball ball2 is at room1 location", "Robot robot1 is carrying the ball room2 in the left gripper"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 9036893926056874407, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, right gripper is free, and left gripper is carrying the ball ball4. Additionally, ball3 is at room5, ball1 is at room1, ball2 is at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Ball robot1 is at room5 location. B. Robot robot1 is carrying the ball ball3 in the right gripper and The left gripper of robot robot1 is free. C. Robot robot1 is carrying the ball ball4 in the right gripper and Ball ball4 is at room4 location. D. Ball room3 is at room3 location and The right gripper of robot robot1 is free.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Ball robot1 is at room5 location", "Robot robot1 is carrying the ball ball3 in the right gripper and The left gripper of robot robot1 is free", "Robot robot1 is carrying the ball ball4 in the right gripper and Ball ball4 is at room4 location", "Ball room3 is at room3 location and The right gripper of robot robot1 is free"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4511247375484983380, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room3 and both grippers are free. Additionally, ball2, ball4, and ball3 are at room1, ball5, ball7, and ball1 are at room3, ball6 is at room2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball ball7 in the left gripper and Robot robot1 is at room2 location. B. Robot robot1 is in room room3 and Robot robot1 is at room1 location. C. Robot robot1 is carrying the ball room2 in the left gripper. D. Ball robot1 is in room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball7 in the left gripper and Robot robot1 is at room2 location", "Robot robot1 is in room room3 and Robot robot1 is at room1 location", "Robot robot1 is carrying the ball room2 in the left gripper", "Ball robot1 is in room room3"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 2588772264796788939, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 15 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is carrying the ball ball1, and right gripper is carrying the ball ball3. Additionally, ball15, ball13, ball12, and ball6 are at room2, ball2, ball9, ball8, ball14, ball10, ball11, and ball4 are at room1, ball5 and ball7 are at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is in room room3. B. Ball left1 is at room2 location. C. Ball ball2 is in room room2 and Robot robot1 is carrying the ball ball2 in the right gripper. D. Robot robot1 is carrying the ball robot1 in the left gripper and Ball ball8 is in room room1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is in room room3", "Ball left1 is at room2 location", "Ball ball2 is in room room2 and Robot robot1 is carrying the ball ball2 in the right gripper", "Robot robot1 is carrying the ball robot1 in the left gripper and Ball ball8 is in room room1"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -6458358088406060827, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is carrying the ball ball4, and right gripper is carrying the ball ball2. Additionally, ball3 and ball1 are at room4.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is at room4 location and The right gripper of robot robot1 is free. B. Ball ball1 is at room4 location and Ball robot1 is at room2 location. C. Robot robot1 is carrying the ball left1 in the right gripper. D. Robot robot1 is carrying the ball ball3 in the right gripper and Robot robot1 is carrying the ball ball1 in the right gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is at room4 location and The right gripper of robot robot1 is free", "Ball ball1 is at room4 location and Ball robot1 is at room2 location", "Robot robot1 is carrying the ball left1 in the right gripper", "Robot robot1 is carrying the ball ball3 in the right gripper and Robot robot1 is carrying the ball ball1 in the right gripper"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 453115776915613540, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room3, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball3 is at room5, ball1 and ball2 are at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is in room room2 and Robot robot1 is in room room5. B. Ball robot1 is at room4 location. C. The right gripper of robot robot1 is free and Robot robot1 is carrying the ball ball2 in the left gripper. D. Ball robot1 is in room room3 and The left gripper of robot robot1 is free.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is in room room2 and Robot robot1 is in room room5", "Ball robot1 is at room4 location", "The right gripper of robot robot1 is free and Robot robot1 is carrying the ball ball2 in the left gripper", "Ball robot1 is in room room3 and The left gripper of robot robot1 is free"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 3654626159221667911, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 2 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room1, left gripper is free, and right gripper is carrying the ball ball2. Additionally, ball3 is at room2, ball1 and ball4 are at room1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball robot1 in the right gripper. B. Robot robot1 is carrying the ball ball3 in the left gripper and Robot robot1 is in room room2. C. Ball ball1 is at room1 location and Robot robot1 is carrying the ball right1 in the left gripper. D. Ball ball4 is at room1 location and Robot robot1 is carrying the ball ball4 in the right gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball robot1 in the right gripper", "Robot robot1 is carrying the ball ball3 in the left gripper and Robot robot1 is in room room2", "Ball ball1 is at room1 location and Robot robot1 is carrying the ball right1 in the left gripper", "Ball ball4 is at room1 location and Robot robot1 is carrying the ball ball4 in the right gripper"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 5686286023996962349, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 3 rooms, and 7 balls, numbered consecutively. Currently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball5. Additionally, ball1, ball2, and ball4 are at room1, ball7 and ball6 are at room2, ball3 is at room3.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball ball5 in the right gripper and Robot robot1 is carrying the ball ball2 in the right gripper. B. Robot robot1 is carrying the ball ball2 in the right gripper and Robot robot1 is in room room1. C. Robot robot1 is carrying the ball room1 in the right gripper. D. Ball right1 is in room room3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball ball5 in the right gripper and Robot robot1 is carrying the ball ball2 in the right gripper", "Robot robot1 is carrying the ball ball2 in the right gripper and Robot robot1 is in room room1", "Robot robot1 is carrying the ball room1 in the right gripper", "Ball right1 is in room room3"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4679214027242730097, "group": "reachable_atom_mc", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. There are 1 robot, 5 rooms, and 4 balls, numbered consecutively. Currently, the robot robot1 is at room5, left gripper is carrying the ball ball2, and right gripper is carrying the ball ball4. Additionally, ball3 and ball1 are at room4.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Robot robot1 is carrying the ball room1 in the right gripper. B. Ball ball2 is in room room3 and Robot robot1 is in room room3. C. Robot robot1 is carrying the ball ball4 in the right gripper and The right gripper of robot robot1 is free. D. Robot robot1 is carrying the ball room3 in the left gripper.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Robot robot1 is carrying the ball room1 in the right gripper", "Ball ball2 is in room room3 and Robot robot1 is in room room3", "Robot robot1 is carrying the ball ball4 in the right gripper and The right gripper of robot robot1 is free", "Robot robot1 is carrying the ball room3 in the left gripper"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4497726815764405601, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint2. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode colour. Rover rover0 has its camera camera0 calibrated. Store(s) store1 and store0 are full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover0 is at waypoint1 and Rover rover0 is at waypoint2. B. Store(s) store0 is empty. C. Rocks can be sampled at the following location(s): waypoint0. D. Rock data was communicated from waypoint waypoint1;.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 is at waypoint1 and Rover rover0 is at waypoint2", "Store(s) store0 is empty", "Rocks can be sampled at the following location(s): waypoint0", "Rock data was communicated from waypoint waypoint1;"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 7861171636961752333, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint1 to waypoint0, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint1: waypoint0, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2, waypoint1, waypoint4, and waypoint3. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint4. Rovers rover0 and rover1 are available. Rock data were communicated from the following waypoints: waypoint1, waypoint0. Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode low_res. Image objective0 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode colour. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Soil can be sampled at the following location(s): waypoint1. B. Rover rover0 is at waypoint1. C. Rover rover0 has soil analyzed in waypoint waypoint0 and Rover rover1 has soil analyzed in waypoint waypoint0. D. Rocks can be sampled at the following location(s): waypoint3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Soil can be sampled at the following location(s): waypoint1", "Rover rover0 is at waypoint1", "Rover rover0 has soil analyzed in waypoint waypoint0 and Rover rover1 has soil analyzed in waypoint waypoint0", "Rocks can be sampled at the following location(s): waypoint3"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 2324362675760777823, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective1. Camera camera2 supports colour and low_res. Camera camera1 supports colour. Camera camera0 supports high_res and colour and low_res. Rover rover0 can traverse from waypoint5 to waypoint6, waypoint6 to waypoint5, waypoint1 to waypoint5, waypoint4 to waypoint5, waypoint5 to waypoint2, waypoint5 to waypoint0, waypoint0 to waypoint5, waypoint5 to waypoint4, waypoint0 to waypoint3, waypoint3 to waypoint0, waypoint5 to waypoint1, waypoint2 to waypoint5. Rover rover1 can traverse from waypoint1 to waypoint6, waypoint2 to waypoint0, waypoint6 to waypoint1, waypoint5 to waypoint0, waypoint2 to waypoint4, waypoint0 to waypoint5, waypoint0 to waypoint6, waypoint4 to waypoint2, waypoint0 to waypoint3, waypoint6 to waypoint0, waypoint3 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint4: waypoint5, waypoint2, waypoint1, waypoint6, and waypoint3. Waypoint(s) are visible from waypoint0: waypoint6, waypoint3, waypoint5, and waypoint2. Waypoint(s) are visible from waypoint2: waypoint3, waypoint0, waypoint5, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint1: waypoint5, waypoint2, waypoint4, waypoint6, and waypoint3. Waypoint(s) are visible from waypoint5: waypoint1, waypoint2, waypoint4, waypoint6, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint6: waypoint5, waypoint1, waypoint4, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint5, waypoint2, waypoint1, waypoint4, waypoint6, and waypoint0. Objective objective1 is visible from waypoint5, waypoint4, waypoint0, and waypoint2. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint4. Rocks can be sampled at the following location(s): waypoint2 and waypoint6. Soil can be sampled at the following location(s): waypoint6, waypoint3, waypoint1, and waypoint4. Rovers rover0 and rover1 are available. Rock data were communicated from the following waypoints: waypoint3, waypoint5, waypoint4. Image objective0 was communicated in mode high_res. Image objective1 was communicated in mode high_res. Image objective1 was communicated in mode colour. Rover rover0 has rock analyzed in waypoint waypoint4. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has rock analyzed in waypoint waypoint3. Rover rover1 has image objective1 in mode high_res. Rover rover1 has image objective1 in mode colour. Rover rover1 has image objective0 in mode high_res. Rover rover0 has its camera camera2 calibrated. Store(s) store0 and store1 are full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover1 is at waypoint2. B. Rover rover0 has image objective1 in mode high_res and Image objective1 was communicated in mode high_res. C. Rocks can be sampled at the following location(s): waypoint3. D. Rover rover1 is at waypoint5 and Rover rover1 is at waypoint3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 is at waypoint2", "Rover rover0 has image objective1 in mode high_res and Image objective1 was communicated in mode high_res", "Rocks can be sampled at the following location(s): waypoint3", "Rover rover1 is at waypoint5 and Rover rover1 is at waypoint3"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 7210913259460911577, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 7 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera1 can be calibrated on objective1. Camera camera2 can be calibrated on objective1. Camera camera0 can be calibrated on objective4. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective6 is visible from waypoint1 and waypoint0. Objective objective2 is visible from waypoint1. Objective objective4 is visible from waypoint1. Objective objective3 is visible from waypoint2. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective5 is visible from waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint0. Rocks can be sampled at the following location(s): waypoint1, waypoint2, and waypoint0. Soil can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint2; Image objective3 was communicated in mode high_res. Rover rover1 has soil analyzed in waypoint waypoint2. Rover rover1 has image objective0 in mode colour. Rover rover1 has image objective3 in mode high_res. Rover rover0 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Soil can be sampled at the following location(s): waypoint2 and Rocks can be sampled at the following location(s): waypoint1. B. Rover rover1 is at waypoint2 and Rover rover0 has image objective1 in mode low_res. C. Rover rover1 has rock analyzed in waypoint waypoint2 and Rocks can be sampled at the following location(s): waypoint2. D. Rover rover0 has soil analyzed in waypoint waypoint0 and Rover rover1 is available.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Soil can be sampled at the following location(s): waypoint2 and Rocks can be sampled at the following location(s): waypoint1", "Rover rover1 is at waypoint2 and Rover rover0 has image objective1 in mode low_res", "Rover rover1 has rock analyzed in waypoint waypoint2 and Rocks can be sampled at the following location(s): waypoint2", "Rover rover0 has soil analyzed in waypoint waypoint0 and Rover rover1 is available"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 2872903799677729435, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 is equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports low_res. Camera camera1 supports low_res and high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint0, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint1. Lander general is at waypoint waypoint1.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint2. Rocks can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Rover rover0 has soil analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode low_res. Store(s) store0 and store1 are empty. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover0 is at waypoint0 and Rover rover1 is at waypoint2. B. Rover rover1 has image objective1 in mode high_res. C. Rover rover0 is at waypoint2 and Rover rover0 is at waypoint0. D. Soil can be sampled at the following location(s): waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover0 is at waypoint0 and Rover rover1 is at waypoint2", "Rover rover1 has image objective1 in mode high_res", "Rover rover0 is at waypoint2 and Rover rover0 is at waypoint0", "Soil can be sampled at the following location(s): waypoint2"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -1235732764542744935, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint1 to waypoint0, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint1: waypoint0, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2, waypoint1, waypoint4, and waypoint3. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint2. Rover rover0 is at waypoint4. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Image objective0 was communicated in mode colour. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode colour. Rover rover1 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover1 is at waypoint1. B. Rover rover1 has its camera camera0 calibrated. C. Rover rover0 has soil analyzed in waypoint waypoint0 and Soil can be sampled at the following location(s): waypoint0. D. Rocks can be sampled at the following location(s): waypoint4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 is at waypoint1", "Rover rover1 has its camera camera0 calibrated", "Rover rover0 has soil analyzed in waypoint waypoint0 and Soil can be sampled at the following location(s): waypoint0", "Rocks can be sampled at the following location(s): waypoint4"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -628658658738634411, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera2 on board. Rover rover0 has camera0 and camera1 on board. Camera camera0 can be calibrated on objective1. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective0. Camera camera0 supports colour. Camera camera2 supports colour and high_res. Camera camera1 supports low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint0, waypoint2 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Waypoint(s) are visible from waypoint0: waypoint2 and waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  Currently, Rover rover0 is at waypoint1. Rover rover1 is at waypoint0. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has its camera camera2 calibrated. Store(s) store0 and store1 are empty. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover1 has rock analyzed in waypoint waypoint0 and Rocks can be sampled at the following location(s): waypoint0. B. Rover rover1 has image objective0 in mode colour. C. Rocks can be sampled at the following location(s): waypoint1 and Rover rover1 is available. D. Soil can be sampled at the following location(s): waypoint2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has rock analyzed in waypoint waypoint0 and Rocks can be sampled at the following location(s): waypoint0", "Rover rover1 has image objective0 in mode colour", "Rocks can be sampled at the following location(s): waypoint1 and Rover rover1 is available", "Soil can be sampled at the following location(s): waypoint2"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -755003146917583975, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint1 to waypoint0, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint1: waypoint0, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2, waypoint1, waypoint4, and waypoint3. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint4. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode colour. Rover rover1 has its camera camera1 calibrated. Store(s) store0 is empty. Store(s) store1 is full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover1 has soil analyzed in waypoint waypoint0 and Soil can be sampled at the following location(s): waypoint0. B. Rover rover0 is at waypoint1 and Rover rover1 is at waypoint2. C. Rover rover1 has soil analyzed in waypoint waypoint2. D. Rocks can be sampled at the following location(s): waypoint0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has soil analyzed in waypoint waypoint0 and Soil can be sampled at the following location(s): waypoint0", "Rover rover0 is at waypoint1 and Rover rover1 is at waypoint2", "Rover rover1 has soil analyzed in waypoint waypoint2", "Rocks can be sampled at the following location(s): waypoint0"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 3887422845016589811, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 7 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  No rovers are equipped for soil analysis. Rover(s) rover0 and rover1 are equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover0 has camera2 on board. Rover rover1 has camera1 and camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera2 can be calibrated on objective1. Camera camera2 supports colour and low_res. Camera camera1 supports colour. Camera camera0 supports high_res and colour and low_res. Rover rover0 can traverse from waypoint5 to waypoint6, waypoint6 to waypoint5, waypoint1 to waypoint5, waypoint4 to waypoint5, waypoint5 to waypoint2, waypoint5 to waypoint0, waypoint0 to waypoint5, waypoint5 to waypoint4, waypoint0 to waypoint3, waypoint3 to waypoint0, waypoint5 to waypoint1, waypoint2 to waypoint5. Rover rover1 can traverse from waypoint1 to waypoint6, waypoint2 to waypoint0, waypoint6 to waypoint1, waypoint5 to waypoint0, waypoint2 to waypoint4, waypoint0 to waypoint5, waypoint0 to waypoint6, waypoint4 to waypoint2, waypoint0 to waypoint3, waypoint6 to waypoint0, waypoint3 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint4: waypoint5, waypoint2, waypoint1, waypoint6, and waypoint3. Waypoint(s) are visible from waypoint0: waypoint6, waypoint3, waypoint5, and waypoint2. Waypoint(s) are visible from waypoint2: waypoint3, waypoint0, waypoint5, waypoint1, and waypoint4. Waypoint(s) are visible from waypoint1: waypoint5, waypoint2, waypoint4, waypoint6, and waypoint3. Waypoint(s) are visible from waypoint5: waypoint1, waypoint2, waypoint4, waypoint6, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint6: waypoint5, waypoint1, waypoint4, waypoint3, and waypoint0. Waypoint(s) are visible from waypoint3: waypoint5, waypoint2, waypoint1, waypoint4, waypoint6, and waypoint0. Objective objective1 is visible from waypoint5, waypoint4, waypoint0, and waypoint2. Objective objective0 is visible from waypoint0. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint4. Rocks can be sampled at the following location(s): waypoint2, waypoint6, and waypoint4. Soil can be sampled at the following location(s): waypoint6, waypoint3, waypoint1, and waypoint4. Rovers rover0 and rover1 are available. Rock data were communicated from the following waypoints: waypoint3, waypoint5. Image objective0 was communicated in mode high_res. Image objective1 was communicated in mode high_res. Image objective1 was communicated in mode colour. Rover rover0 has rock analyzed in waypoint waypoint5. Rover rover1 has rock analyzed in waypoint waypoint3. Rover rover1 has image objective1 in mode high_res. Rover rover1 has image objective0 in mode high_res. Rover rover0 has image objective1 in mode colour. Store(s) store0 and store1 are full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Rover rover1 has its camera camera1 calibrated and Store(s) store0 is empty. B. Rock data was communicated from waypoint waypoint1;. C. Rover rover1 has rock analyzed in waypoint waypoint4 and Rover rover0 has rock analyzed in waypoint waypoint4. D. Rocks can be sampled at the following location(s): waypoint5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Rover rover1 has its camera camera1 calibrated and Store(s) store0 is empty", "Rock data was communicated from waypoint waypoint1;", "Rover rover1 has rock analyzed in waypoint waypoint4 and Rover rover0 has rock analyzed in waypoint waypoint4", "Rocks can be sampled at the following location(s): waypoint5"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -5538864297682710631, "group": "reachable_atom_mc", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. There are 2 rovers, 5 waypoints, 2 stores, 2 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  Rover(s) rover0 and rover1 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover0 and rover1 are equipped for imaging. Rover rover0 has store store0. Rover rover1 has store store1. Rover rover1 has camera1 on board. Rover rover0 has camera0 on board. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective0. Camera camera0 supports colour and low_res. Camera camera1 supports colour and low_res. Rover rover1 can traverse from waypoint2 to waypoint0, waypoint2 to waypoint1, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover0 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint4, waypoint1 to waypoint0, waypoint4 to waypoint1. Waypoint(s) are visible from waypoint2: waypoint0, waypoint1, and waypoint3. Waypoint(s) are visible from waypoint1: waypoint0, waypoint2, and waypoint4. Waypoint(s) are visible from waypoint4: waypoint1 and waypoint0. Waypoint(s) are visible from waypoint0: waypoint2, waypoint1, waypoint4, and waypoint3. Waypoint(s) are visible from waypoint3: waypoint2 and waypoint0. Objective objective0 is visible from waypoint2 and waypoint1. Objective objective1 is visible from waypoint4. Lander general is at waypoint waypoint3.  Currently, Rover rover1 is at waypoint1. Rover rover0 is at waypoint4. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint0; Soil data was communicated from waypoint waypoint0; Image objective0 was communicated in mode colour. Rover rover0 has soil analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint0. Rover rover1 has rock analyzed in waypoint waypoint1. Rover rover1 has image objective0 in mode low_res. Rover rover0 has image objective1 in mode low_res. Rover rover1 has image objective0 in mode colour. Store(s) store1 and store0 are full. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Soil can be sampled at the following location(s): waypoint1 and Rover rover0 has image objective1 in mode low_res. B. Rover rover0 has soil analyzed in waypoint waypoint0 and Soil can be sampled at the following location(s): waypoint0. C. Rover rover1 is at waypoint2 and Image objective0 was communicated in mode low_res. D. Soil can be sampled at the following location(s): waypoint1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Soil can be sampled at the following location(s): waypoint1 and Rover rover0 has image objective1 in mode low_res", "Rover rover0 has soil analyzed in waypoint waypoint0 and Soil can be sampled at the following location(s): waypoint0", "Rover rover1 is at waypoint2 and Image objective0 was communicated in mode low_res", "Soil can be sampled at the following location(s): waypoint1"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 3878761865268448015, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x2-y3.The following places have been visited: loc-x3-y0, loc-x0-y3, loc-x3-y2, loc-x0-y2, loc-x2-y0, loc-x2-y3, loc-x1-y3, loc-x3-y3, loc-x2-y2, loc-x1-y2, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y0 and the robot is in place loc-x2-y3. B. the robot is in place loc-x1-y2. C. the robot is in place loc-x2-y2 and the robot is in place loc-x2-y1. D. the robot is in place loc-x2-y0 and the robot is in place loc-x1-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y0 and the robot is in place loc-x2-y3", "the robot is in place loc-x1-y2", "the robot is in place loc-x2-y2 and the robot is in place loc-x2-y1", "the robot is in place loc-x2-y0 and the robot is in place loc-x1-y1"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4420995701107034806, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x2-y3.The following places have been visited: loc-x3-y0, loc-x2-y0, loc-x2-y3, loc-x3-y3, loc-x1-y1, loc-x2-y2, loc-x1-y2, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y0 and the robot is in place loc-x0-y2. B. the robot is in place loc-x2-y3 and the robot is in place loc-x1-y1. C. Place loc-x3-y2 has been visited and the robot is in place loc-x3-y2. D. the robot is in place loc-x0-y2 and the robot is in place loc-x3-y2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y0 and the robot is in place loc-x0-y2", "the robot is in place loc-x2-y3 and the robot is in place loc-x1-y1", "Place loc-x3-y2 has been visited and the robot is in place loc-x3-y2", "the robot is in place loc-x0-y2 and the robot is in place loc-x3-y2"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 3211262035088721601, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x1-y4.The following places have been visited: loc-x3-y0, loc-x0-y4, loc-x0-y3, loc-x3-y2, loc-x0-y0, loc-x0-y2, loc-x2-y0, loc-x1-y3, loc-x3-y1, loc-x1-y1, loc-x1-y4, loc-x2-y2, loc-x1-y2, loc-x0-y1, loc-x1-y0, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x0-y4 and the robot is in place loc-x2-y1. B. the robot is in place loc-x2-y2 and the robot is in place loc-x3-y3. C. Place loc-x2-y4 has been visited. D. the robot is in place loc-x0-y2 and the robot is in place loc-x0-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x0-y4 and the robot is in place loc-x2-y1", "the robot is in place loc-x2-y2 and the robot is in place loc-x3-y3", "Place loc-x2-y4 has been visited", "the robot is in place loc-x0-y2 and the robot is in place loc-x0-y1"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 9117448111502219264, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x3-y2.The following places have been visited: loc-x3-y0, loc-x3-y2, loc-x2-y0, loc-x3-y3, loc-x2-y2, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Place loc-x2-y3 has been visited and the robot is in place loc-x2-y3. B. the robot is in place loc-x1-y2 and the robot is in place loc-x0-y2. C. the robot is in place loc-x0-y1 and the robot is in place loc-x0-y3. D. the robot is in place loc-x3-y2 and the robot is in place loc-x0-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Place loc-x2-y3 has been visited and the robot is in place loc-x2-y3", "the robot is in place loc-x1-y2 and the robot is in place loc-x0-y2", "the robot is in place loc-x0-y1 and the robot is in place loc-x0-y3", "the robot is in place loc-x3-y2 and the robot is in place loc-x0-y1"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -1158805352936933551, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The only unavailable cell is loc-x2-y3. Currently, the robot is in place loc-x1-y0.The following places have been visited: loc-x3-y0, loc-x0-y4, loc-x0-y3, loc-x3-y2, loc-x3-y4, loc-x0-y2, loc-x2-y0, loc-x3-y3, loc-x1-y4, loc-x2-y4, loc-x1-y0, and loc-x3-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y1 and the robot is in place loc-x3-y2. B. Place loc-x0-y0 has been visited. C. the robot is in place loc-x2-y1 and the robot is in place loc-x3-y2. D. the robot is in place loc-x2-y4 and the robot is in place loc-x3-y0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y1 and the robot is in place loc-x3-y2", "Place loc-x0-y0 has been visited", "the robot is in place loc-x2-y1 and the robot is in place loc-x3-y2", "the robot is in place loc-x2-y4 and the robot is in place loc-x3-y0"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -4221505914593037373, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1, loc-x0-y2, loc-x3-y3, and loc-x1-y0. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x0-y3, loc-x3-y2, loc-x0-y0, loc-x2-y0, loc-x2-y3, loc-x1-y3, loc-x1-y1, loc-x2-y2, loc-x2-y1, loc-x1-y2, and loc-x0-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y2 and the robot is in place loc-x0-y1. B. the robot is in place loc-x3-y2 and the robot is in place loc-x2-y2. C. the robot is in place loc-x2-y1 and the robot is in place loc-x2-y3. D. the robot is in place loc-x0-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y2 and the robot is in place loc-x0-y1", "the robot is in place loc-x3-y2 and the robot is in place loc-x2-y2", "the robot is in place loc-x2-y1 and the robot is in place loc-x2-y3", "the robot is in place loc-x0-y1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -1258566148040957714, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x3-y1 and loc-x1-y0. Currently, the robot is in place loc-x1-y3.The following places have been visited: loc-x3-y0, loc-x3-y2, loc-x2-y0, loc-x2-y3, loc-x1-y3, loc-x3-y3, loc-x2-y2, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y2 and the robot is in place loc-x1-y2. B. the robot is in place loc-x0-y2 and Place loc-x0-y2 has been visited. C. the robot is in place loc-x2-y3 and the robot is in place loc-x3-y3. D. the robot is in place loc-x2-y3 and the robot is in place loc-x1-y1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y2 and the robot is in place loc-x1-y2", "the robot is in place loc-x0-y2 and Place loc-x0-y2 has been visited", "the robot is in place loc-x2-y3 and the robot is in place loc-x3-y3", "the robot is in place loc-x2-y3 and the robot is in place loc-x1-y1"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -4292600062871153127, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x2-y3, and loc-x0-y4. Currently, the robot is in place loc-x1-y1.The following places have been visited: loc-x3-y4, loc-x0-y3, loc-x3-y2, loc-x0-y0, loc-x0-y2, loc-x1-y3, loc-x3-y3, loc-x1-y1, loc-x1-y4, loc-x2-y2, loc-x2-y4, loc-x0-y1, loc-x1-y0, and loc-x2-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x1-y1 and the robot is in place loc-x2-y4. B. the robot is in place loc-x0-y2 and the robot is in place loc-x3-y4. C. the robot is in place loc-x0-y0 and the robot is in place loc-x0-y3. D. the robot is in place loc-x2-y0 and Place loc-x2-y0 has been visited.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x1-y1 and the robot is in place loc-x2-y4", "the robot is in place loc-x0-y2 and the robot is in place loc-x3-y4", "the robot is in place loc-x0-y0 and the robot is in place loc-x0-y3", "the robot is in place loc-x2-y0 and Place loc-x2-y0 has been visited"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -6398941987123616115, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. There are no unavailable cells. Currently, the robot is in place loc-x2-y1.The following places have been visited: loc-x3-y0, loc-x0-y3, loc-x3-y2, loc-x0-y0, loc-x0-y2, loc-x2-y0, loc-x2-y3, loc-x1-y3, loc-x3-y1, loc-x3-y3, loc-x2-y2, loc-x2-y1, loc-x1-y2, loc-x0-y1, and loc-x1-y0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x3-y3 and the robot is in place loc-x3-y2. B. the robot is in place loc-x1-y2. C. the robot is in place loc-x0-y2 and the robot is in place loc-x1-y1. D. the robot is in place loc-x2-y0 and the robot is in place loc-x3-y3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x3-y3 and the robot is in place loc-x3-y2", "the robot is in place loc-x1-y2", "the robot is in place loc-x0-y2 and the robot is in place loc-x1-y1", "the robot is in place loc-x2-y0 and the robot is in place loc-x3-y3"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 8419673906829450802, "group": "reachable_atom_mc", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. The grid size is 4x5, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). The grid cells are connected to their available neighbors. The unavailable cells are loc-x1-y2, loc-x2-y3, and loc-x0-y4. Currently, the robot is in place loc-x3-y1.The following places have been visited: loc-x3-y0, loc-x0-y0, loc-x0-y2, loc-x2-y0, loc-x0-y1, loc-x1-y0, and loc-x3-y1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. the robot is in place loc-x2-y0. B. the robot is in place loc-x0-y2 and the robot is in place loc-x0-y3. C. the robot is in place loc-x1-y3 and the robot is in place loc-x3-y1. D. the robot is in place loc-x3-y4 and the robot is in place loc-x3-y3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["the robot is in place loc-x2-y0", "the robot is in place loc-x0-y2 and the robot is in place loc-x0-y3", "the robot is in place loc-x1-y3 and the robot is in place loc-x3-y1", "the robot is in place loc-x3-y4 and the robot is in place loc-x3-y3"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 8085264433634198793, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 9 hoists, 7 depots, 9 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet8, pallet7, pallet6, pallet4, pallet2, pallet5, pallet0, pallet1, and crate1 are clear; hoist8, hoist3, hoist7, hoist1, hoist0, hoist4, hoist6, and hoist2 are available; pallet2 is at depot2, pallet6 is at depot6, crate1 is at depot3, hoist5 is at depot5, truck0 is at depot5, hoist2 is at depot2, pallet5 is at depot5, pallet4 is at depot4, pallet8 is at distributor1, pallet0 is at depot0, pallet3 is at depot3, pallet1 is at depot1, hoist6 is at depot6, pallet7 is at distributor0, hoist3 is at depot3, hoist0 is at depot0, hoist8 is at distributor1, hoist4 is at depot4, hoist1 is at depot1, truck1 is at depot4, and hoist7 is at distributor0; crate1 is on pallet3; hoist5 is lifting crate0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. hoist7 is lifting crate0 and crate0 is on pallet3. B. pallet1 is at depot4. C. pallet4 is at depot0. D. truck0 is at depot1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["hoist7 is lifting crate0 and crate0 is on pallet3", "pallet1 is at depot4", "pallet4 is at depot0", "truck0 is at depot1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -836593609968258800, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 depots, 4 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet3, pallet2, pallet0, and pallet1 are clear; hoist3, hoist1, and hoist2 are available; hoist3 is at distributor1, hoist2 is at distributor0, truck0 is at depot0, pallet0 is at depot0, truck1 is at distributor1, hoist1 is at depot1, pallet1 is at depot1, hoist0 is at depot0, pallet3 is at distributor1, and pallet2 is at distributor0; crate0 is in truck0; hoist0 is lifting crate1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. truck1 is at distributor0 and truck0 is at depot1. B. crate0 is at depot1 and crate0 is at distributor1. C. pallet2 is at depot1. D. pallet1 is at depot1 and hoist2 is at depot0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["truck1 is at distributor0 and truck0 is at depot1", "crate0 is at depot1 and crate0 is at distributor1", "pallet2 is at depot1", "pallet1 is at depot1 and hoist2 is at depot0"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 7497973151672570125, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 5 hoists, 3 depots, 5 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet3, crate0, pallet4, pallet0, and crate1 are clear; hoist3, hoist1, hoist0, hoist4, and hoist2 are available; pallet2 is at depot2, hoist2 is at depot2, truck0 is at distributor1, pallet0 is at depot0, hoist3 is at distributor0, crate0 is at depot1, pallet1 is at depot1, crate1 is at depot2, pallet4 is at distributor1, hoist0 is at depot0, truck1 is at depot2, hoist4 is at distributor1, hoist1 is at depot1, and pallet3 is at distributor0; crate0 is on pallet1 and crate1 is on pallet2.", "question": "Which of the following options can hold in a state that can potentially be reached? A. pallet1 is clear and truck1 is at depot1. B. pallet4 is at depot1. C. hoist2 is at distributor0 and crate0 is clear. D. crate1 is in truck1 and crate1 is on crate0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pallet1 is clear and truck1 is at depot1", "pallet4 is at depot1", "hoist2 is at distributor0 and crate0 is clear", "crate1 is in truck1 and crate1 is on crate0"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 7818126190465323287, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 12 hoists, 10 depots, 12 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet11, pallet7, pallet8, pallet6, pallet3, pallet10, pallet4, pallet9, pallet2, pallet5, pallet0, and pallet1 are clear; hoist3, hoist7, hoist11, hoist1, hoist0, hoist10, hoist5, hoist4, hoist6, and hoist2 are available; pallet2 is at depot2, truck1 is at depot8, pallet8 is at depot8, pallet6 is at depot6, pallet10 is at distributor0, hoist5 is at depot5, hoist8 is at depot8, hoist2 is at depot2, pallet5 is at depot5, pallet4 is at depot4, hoist11 is at distributor1, pallet11 is at distributor1, pallet0 is at depot0, hoist9 is at depot9, pallet3 is at depot3, truck0 is at depot9, pallet1 is at depot1, hoist6 is at depot6, hoist7 is at depot7, hoist3 is at depot3, hoist10 is at distributor0, hoist0 is at depot0, pallet9 is at depot9, hoist4 is at depot4, pallet7 is at depot7, and hoist1 is at depot1; hoist8 is lifting crate0 and hoist9 is lifting crate1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. truck1 is at distributor1. B. pallet9 is at depot8. C. hoist1 is at distributor0. D. hoist8 is lifting crate0 and crate0 is at distributor1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["truck1 is at distributor1", "pallet9 is at depot8", "hoist1 is at distributor0", "hoist8 is lifting crate0 and crate0 is at distributor1"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -8428628700310186481, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 12 hoists, 10 depots, 12 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet11, pallet7, pallet8, pallet6, pallet3, pallet10, pallet4, pallet2, pallet5, pallet0, pallet1, and crate1 are clear; hoist8, hoist3, hoist7, hoist1, hoist0, hoist9, hoist10, hoist5, hoist4, hoist6, and hoist2 are available; pallet2 is at depot2, pallet8 is at depot8, pallet6 is at depot6, pallet10 is at distributor0, hoist5 is at depot5, hoist8 is at depot8, hoist2 is at depot2, pallet5 is at depot5, pallet4 is at depot4, hoist11 is at distributor1, truck1 is at depot6, crate1 is at depot9, pallet11 is at distributor1, pallet0 is at depot0, hoist9 is at depot9, pallet3 is at depot3, pallet1 is at depot1, hoist6 is at depot6, hoist7 is at depot7, hoist3 is at depot3, truck0 is at depot8, hoist10 is at distributor0, hoist0 is at depot0, pallet9 is at depot9, hoist4 is at depot4, pallet7 is at depot7, and hoist1 is at depot1; crate1 is on pallet9; hoist11 is lifting crate0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. pallet10 is at depot5. B. truck1 is at depot0. C. hoist3 is at depot6. D. crate0 is at depot2 and hoist2 is at depot2.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pallet10 is at depot5", "truck1 is at depot0", "hoist3 is at depot6", "crate0 is at depot2 and hoist2 is at depot2"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 8082057447230157368, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 12 hoists, 10 depots, 12 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet7, pallet8, pallet6, pallet3, crate0, pallet10, pallet4, pallet9, pallet2, pallet5, pallet0, and pallet1 are clear; hoist8, hoist3, hoist7, hoist11, hoist1, hoist0, hoist9, hoist10, hoist5, hoist4, and hoist6 are available; pallet2 is at depot2, truck1 is at depot0, pallet8 is at depot8, pallet6 is at depot6, pallet10 is at distributor0, hoist5 is at depot5, hoist8 is at depot8, hoist2 is at depot2, pallet5 is at depot5, pallet4 is at depot4, hoist11 is at distributor1, truck0 is at depot6, crate0 is at distributor1, pallet11 is at distributor1, pallet0 is at depot0, hoist9 is at depot9, pallet3 is at depot3, pallet1 is at depot1, hoist6 is at depot6, hoist7 is at depot7, hoist3 is at depot3, hoist10 is at distributor0, hoist0 is at depot0, pallet9 is at depot9, hoist4 is at depot4, pallet7 is at depot7, and hoist1 is at depot1; crate0 is on pallet11; hoist2 is lifting crate1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. truck1 is at depot5 and truck1 is at depot6. B. truck1 is at depot6 and truck0 is at depot3. C. crate0 is clear and hoist9 is at depot4. D. pallet10 is at depot8 and pallet4 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["truck1 is at depot5 and truck1 is at depot6", "truck1 is at depot6 and truck0 is at depot3", "crate0 is clear and hoist9 is at depot4", "pallet10 is at depot8 and pallet4 is clear"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -6791540920004025164, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 depots, 4 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet3, pallet2, pallet0, and pallet1 are clear; hoist3, hoist1, hoist0, and hoist2 are available; hoist3 is at distributor1, hoist2 is at distributor0, truck0 is at depot0, pallet0 is at depot0, hoist1 is at depot1, pallet1 is at depot1, truck1 is at distributor0, hoist0 is at depot0, pallet3 is at distributor1, and pallet2 is at distributor0; crate0 is in truck1 and crate1 is in truck1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. pallet3 is at distributor1 and hoist2 is at depot1. B. truck1 is at depot0 and truck0 is at depot1. C. hoist3 is at depot0. D. crate0 is on pallet1 and pallet1 is clear.", "choices": {"label": ["A", "B", "C", "D"], "text": ["pallet3 is at distributor1 and hoist2 is at depot1", "truck1 is at depot0 and truck0 is at depot1", "hoist3 is at depot0", "crate0 is on pallet1 and pallet1 is clear"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -2339578857696258511, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 depots, 4 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet3, pallet2, pallet0, and pallet1 are clear; hoist1, hoist0, and hoist2 are available; truck1 is at depot0, hoist3 is at distributor1, hoist2 is at distributor0, truck0 is at distributor1, pallet0 is at depot0, hoist1 is at depot1, pallet1 is at depot1, hoist0 is at depot0, pallet3 is at distributor1, and pallet2 is at distributor0; crate0 is in truck1; hoist3 is lifting crate1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. hoist3 is available and truck0 is at depot1. B. hoist3 is at depot1 and hoist2 is at distributor0. C. truck1 is at distributor1 and truck1 is at distributor0. D. hoist2 is at depot0.", "choices": {"label": ["A", "B", "C", "D"], "text": ["hoist3 is available and truck0 is at depot1", "hoist3 is at depot1 and hoist2 is at distributor0", "truck1 is at distributor1 and truck1 is at distributor0", "hoist2 is at depot0"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -2316809582027835589, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 6 hoists, 4 depots, 6 pallets, 3 crates, 2 distributors, numbered consecutively. Currently, pallet3, pallet4, pallet2, pallet5, pallet0, and pallet1 are clear; hoist0, hoist5, hoist4, and hoist2 are available; pallet2 is at depot2, hoist5 is at distributor1, truck1 is at depot0, hoist2 is at depot2, truck0 is at depot3, pallet0 is at depot0, pallet3 is at depot3, pallet4 is at distributor0, hoist1 is at depot1, hoist4 is at distributor0, pallet1 is at depot1, hoist3 is at depot3, hoist0 is at depot0, and pallet5 is at distributor1; crate1 is in truck0; hoist1 is lifting crate2 and hoist3 is lifting crate0.", "question": "Which of the following options can hold in a state that can potentially be reached? A. crate2 is clear and crate2 is at depot1. B. crate1 is in truck0 and pallet0 is at depot2. C. truck0 is at depot3 and hoist4 is at distributor1. D. crate0 is on pallet5 and crate2 is on pallet5.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate2 is clear and crate2 is at depot1", "crate1 is in truck0 and pallet0 is at depot2", "truck0 is at depot3 and hoist4 is at distributor1", "crate0 is on pallet5 and crate2 is on pallet5"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -9106507804115105684, "group": "reachable_atom_mc", "context": "This is a depot domain, a combination of blocks and logistics. In this domain, trucks can transport crates, the crates can be stacked onto pallets using hoists. There are 2 trucks, 4 hoists, 2 depots, 4 pallets, 2 crates, 2 distributors, numbered consecutively. Currently, pallet3, pallet2, pallet1, and crate1 are clear; hoist3, hoist1, hoist0, and hoist2 are available; hoist3 is at distributor1, hoist2 is at distributor0, truck0 is at depot0, crate1 is at depot0, truck1 is at depot1, pallet0 is at depot0, hoist1 is at depot1, pallet1 is at depot1, hoist0 is at depot0, pallet3 is at distributor1, and pallet2 is at distributor0; crate1 is on pallet0; crate0 is in truck1.", "question": "Which of the following options can hold in a state that can potentially be reached? A. crate0 is on pallet0 and pallet0 is clear. B. pallet0 is at distributor0. C. pallet0 is at distributor1. D. crate0 is clear and crate0 is on pallet1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["crate0 is on pallet0 and pallet0 is clear", "pallet0 is at distributor0", "pallet0 is at distributor1", "crate0 is clear and crate0 is on pallet1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 6860131839813234206, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and its arm is empty. The following locations have soft rock: f2-3f, f1-3f, f2-1f, f0-3f, and f2-2f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robot is holding a bomb and The robot is at position f0-0f. B. Hard rock at f2-3f. C. Hard rock at f2-3f. D. The robot is at position f0-1f and The robot is at position f1-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is holding a bomb and The robot is at position f0-0f", "Hard rock at f2-3f", "Hard rock at f2-3f", "The robot is at position f0-1f and The robot is at position f1-2f"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 785464957730132074, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-3f and its arm is empty. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f2-4f, f2-3f, f2-1f, f0-4f, and f2-2f. The gold is at f0-4f location. The laser is at f0-1f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robot is at position f2-0f and The robot is at position f0-4f. B. Soft rock at f2-4f and Soft rock at f1-0f. C. The robot is at position f1-2f. D. The gold is at f1-2f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f2-0f and The robot is at position f0-4f", "Soft rock at f2-4f and Soft rock at f1-0f", "The robot is at position f1-2f", "The gold is at f1-2f location"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 3894057849770424628, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding a bomb. The following locations have hard rock: f3-3f, f1-2f, f1-1f, f1-3f, and f3-1f. The following locations have soft rock: f2-3f, f2-1f, f3-2f, f0-1f, f0-2f, f0-3f, and f2-2f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robot is at position f2-0f. B. Location(s) f3-0f is clear and Hard rock at f2-0f. C. The robot is at position f0-2f and The robot is at position f2-3f. D. The gold is at f1-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f2-0f", "Location(s) f3-0f is clear and Hard rock at f2-0f", "The robot is at position f0-2f and The robot is at position f2-3f", "The gold is at f1-1f location"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 3627310480467772438, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and its arm is empty. The following locations have soft rock: f2-3f, f1-3f, f2-1f, f1-2f, and f2-2f. The gold is at f0-3f location. The laser is at f0-0f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robot is at position f0-2f and The robot is at position f1-3f. B. The gold is at f0-1f location and Location(s) f0-1f is clear. C. The robot is at position f1-0f. D. Location(s) f0-2f is clear and Soft rock at f0-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f0-2f and The robot is at position f1-3f", "The gold is at f0-1f location and Location(s) f0-1f is clear", "The robot is at position f1-0f", "Location(s) f0-2f is clear and Soft rock at f0-1f"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -7235201258142496833, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f1-2f. The following locations have soft rock: f0-2f, f1-1f, and f2-2f. The gold is at f0-2f location. The laser is at f0-0f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The gold is at f2-2f location and The robot is at position f0-0f. B. The robot is holding a laser. C. Hard rock at f1-1f. D. The robot is at position f0-0f and The robot is at position f0-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The gold is at f2-2f location and The robot is at position f0-0f", "The robot is holding a laser", "Hard rock at f1-1f", "The robot is at position f0-0f and The robot is at position f0-2f"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 4122394715649816648, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-1f and is holding a laser. The following locations have hard rock: f3-3f, f1-2f, f1-3f, and f3-1f. The following locations have soft rock: f2-3f, f2-1f, f3-2f, and f2-2f. The gold is at f0-3f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The gold is at f1-2f location. B. The laser is at f2-2f location and The laser is at f1-1f location. C. The robot is at position f1-0f. D. Hard rock at f3-3f and The gold is at f1-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The gold is at f1-2f location", "The laser is at f2-2f location and The laser is at f1-1f location", "The robot is at position f1-0f", "Hard rock at f3-3f and The gold is at f1-1f location"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -3916892767882262448, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x5 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and is holding gold. The following locations have hard rock: f1-4f, f1-1f, and f1-3f. The following locations have soft rock: f2-4f, f2-3f, f2-1f, f1-2f, and f2-2f. The gold is at f0-4f location. The laser is at f0-0f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Soft rock at f0-2f. B. Hard rock at f0-0f and The robot is holding gold. C. The robot is at position f2-0f and The robot is at position f1-1f. D. The robot is at position f0-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Soft rock at f0-2f", "Hard rock at f0-0f and The robot is holding gold", "The robot is at position f2-0f and The robot is at position f1-1f", "The robot is at position f0-2f"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -7730830926993374507, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-1f and is holding a laser. The following locations have soft rock: f2-3f, f1-3f, f2-1f, f0-3f, and f2-2f. The gold is at f0-3f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robot's arm is empty and The laser is at f0-1f location. B. Soft rock at f0-0f. C. The gold is at f0-0f location. D. The robot is at position f1-3f and The robot is at position f2-2f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot's arm is empty and The laser is at f0-1f location", "Soft rock at f0-0f", "The gold is at f0-0f location", "The robot is at position f1-3f and The robot is at position f2-2f"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -7792959348656605519, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 3x3 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f1-1f and its arm is empty. The following locations have hard rock: f1-2f. The following locations have soft rock: f2-1f and f2-2f. The gold is at f0-2f location. The laser is at f1-0f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. Soft rock at f0-1f. B. Soft rock at f0-0f. C. The robot is at position f0-0f. D. The laser is at f2-0f location and The laser is at f0-1f location.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Soft rock at f0-1f", "Soft rock at f0-0f", "The robot is at position f0-0f", "The laser is at f2-0f location and The laser is at f0-1f location"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 3828496417764887999, "group": "reachable_atom_mc", "context": "A robotic arm is in a grid and can only move to locations that are connected to its current location. The 4x4 grid locations may have gold, hard rocks, or soft rocks. Rocks cannot be moved. The robotic arm can pick up laser or bomb. Only one item can be picked at a time. There is one laser is the grid that can be used to clear rocks. Robotic arm can fire laser at a location from a connected location. The locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f).  If a bomb is picked, it cannot be placed back. It can only be detonated at connected location that have soft rock. Bomb supply is available at f0-0f location.  Currently, the robot is at position f0-0f and its arm is empty. The following locations have hard rock: f3-3f, f1-2f, f1-3f, and f3-1f. The following locations have soft rock: f2-3f, f2-1f, f3-2f, and f2-2f. The gold is at f0-3f location. The laser is at f0-1f location.", "question": "Which of the following options can hold in a state that can potentially be reached? A. The robot is at position f0-2f. B. The laser is at f2-2f location and The robot is holding a laser. C. The gold is at f3-1f location. D. Hard rock at f2-1f.", "choices": {"label": ["A", "B", "C", "D"], "text": ["The robot is at position f0-2f", "The laser is at f2-2f location and The robot is holding a laser", "The gold is at f3-1f location", "Hard rock at f2-1f"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 5232813661151894308, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation0, planet6, groundstation3, star4, planet5, star2. There are 3 image mode(s): thermograph0, image1, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite6 has following instruments onboard: instrument10, instrument9, instrument11. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite3 has following instruments onboard: instrument4, instrument5. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument2 supports image of mode infrared2 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument7 supports image of mode infrared2 and its calibration target is star2. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument4 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3.  Currently, Satellite satellite9 is pointing to groundstation1. Satellite satellite4 is pointing to star2. Satellite satellite7 is pointing to star2. Satellite satellite3 is pointing to planet6. Satellite satellite5 is pointing to groundstation3. Satellite satellite0 is pointing to groundstation1. Satellite satellite6 is pointing to groundstation1. Satellite satellite8 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite2 is pointing to planet6. Power is available on the following satellite(s): satellite7, satellite5, satellite0, satellite4, satellite6, satellite1, satellite3, satellite2, satellite9. Following instruments are powered on: instrument14. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite6 is pointing to groundstation3 and Satellite satellite6 is pointing to star2. B. Satellite satellite8 is pointing to star2 and Satellite satellite8 is pointing to planet6. C. Satellite satellite9 is pointing to star4 and Satellite satellite5 is pointing to planet5. D. Satellite satellite3 is pointing to groundstation0 and Satellite satellite3 is pointing to planet6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite6 is pointing to groundstation3 and Satellite satellite6 is pointing to star2", "Satellite satellite8 is pointing to star2 and Satellite satellite8 is pointing to planet6", "Satellite satellite9 is pointing to star4 and Satellite satellite5 is pointing to planet5", "Satellite satellite3 is pointing to groundstation0 and Satellite satellite3 is pointing to planet6"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 6188646718296709750, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation3, planet5, star2, star0, phenomenon6, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to groundstation1. Satellite satellite1 is pointing to groundstation4. Satellite satellite2 is pointing to groundstation1. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. A infrared1 mode image of target phenomenon6 is available. A spectrograph0 mode image of target phenomenon6 is available. A thermograph2 mode image of target phenomenon6 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite0 is pointing to star2 and Satellite satellite0 is pointing to planet5. B. Satellite satellite0 is pointing to phenomenon6 and Satellite satellite2 is pointing to star0. C. Satellite satellite0 is pointing to star2 and Satellite satellite0 is pointing to planet5. D. Satellite satellite0 is pointing to planet5 and Satellite satellite0 is pointing to groundstation1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite0 is pointing to star2 and Satellite satellite0 is pointing to planet5", "Satellite satellite0 is pointing to phenomenon6 and Satellite satellite2 is pointing to star0", "Satellite satellite0 is pointing to star2 and Satellite satellite0 is pointing to planet5", "Satellite satellite0 is pointing to planet5 and Satellite satellite0 is pointing to groundstation1"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -8745705955450891044, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation1, groundstation5, star10, groundstation3, planet7, star9, star2, planet8, groundstation6, star0, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite2 has following instruments onboard: instrument3, instrument4, instrument5. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument9 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument6 supports image of mode spectrograph0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite1 is pointing to star0. Satellite satellite3 is pointing to groundstation3. Satellite satellite0 is pointing to star10. Satellite satellite5 is pointing to groundstation3. Satellite satellite2 is pointing to star9. Satellite satellite4 is pointing to star10. Power is available on the following satellite(s): satellite0, satellite3, satellite5, satellite4, satellite1. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A infrared1 mode image of target star10 is available. A infrared1 mode image of target planet8 is available. A spectrograph0 mode image of target star9 is available. A thermograph2 mode image of target planet7 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite3 is pointing to star2 and Satellite satellite3 is pointing to groundstation3. B. Satellite satellite4 is pointing to groundstation3 and Satellite satellite4 is pointing to groundstation4. C. Satellite satellite0 is pointing to groundstation5. D. Satellite satellite1 is pointing to star2 and Satellite satellite1 is pointing to groundstation4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite3 is pointing to star2 and Satellite satellite3 is pointing to groundstation3", "Satellite satellite4 is pointing to groundstation3 and Satellite satellite4 is pointing to groundstation4", "Satellite satellite0 is pointing to groundstation5", "Satellite satellite1 is pointing to star2 and Satellite satellite1 is pointing to groundstation4"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -383194445830517676, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation3, planet5, star2, star0, phenomenon6, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to star2. Satellite satellite1 is pointing to star2. Satellite satellite2 is pointing to planet5. Power is available on the following satellite(s): satellite1. Following instruments are powered on: instrument4, instrument0. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. A spectrograph0 mode image of target phenomenon6 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite2 is pointing to planet5 and Satellite satellite2 is pointing to star0. B. Satellite satellite2 is pointing to groundstation1 and Satellite satellite2 is pointing to groundstation3. C. Satellite satellite2 is pointing to groundstation1. D. Satellite satellite2 is pointing to star0 and Satellite satellite2 is pointing to phenomenon6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite2 is pointing to planet5 and Satellite satellite2 is pointing to star0", "Satellite satellite2 is pointing to groundstation1 and Satellite satellite2 is pointing to groundstation3", "Satellite satellite2 is pointing to groundstation1", "Satellite satellite2 is pointing to star0 and Satellite satellite2 is pointing to phenomenon6"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 4369914393841354954, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation1, groundstation5, star10, groundstation3, planet7, star9, star2, planet8, groundstation6, star0, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite2 has following instruments onboard: instrument3, instrument4, instrument5. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument9 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument6 supports image of mode spectrograph0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite1 is pointing to star9. Satellite satellite5 is pointing to star2. Satellite satellite2 is pointing to star0. Satellite satellite3 is pointing to planet8. Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to star10. Power is available on the following satellite(s): satellite2, satellite5, satellite4, satellite1, satellite0. Following instruments are powered on: instrument6. Following instruments are calibrated: instrument6. A infrared1 mode image of target star10 is available. A infrared1 mode image of target planet8 is available. A thermograph2 mode image of target planet7 is available. A infrared1 mode image of target star9 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite1 is pointing to planet7 and Satellite satellite1 is pointing to star0. B. Satellite satellite3 is pointing to planet7 and Satellite satellite3 is pointing to groundstation5. C. Satellite satellite4 is pointing to groundstation1 and Satellite satellite4 is pointing to planet8. D. Satellite satellite3 is pointing to groundstation6 and Satellite satellite4 is pointing to groundstation3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite1 is pointing to planet7 and Satellite satellite1 is pointing to star0", "Satellite satellite3 is pointing to planet7 and Satellite satellite3 is pointing to groundstation5", "Satellite satellite4 is pointing to groundstation1 and Satellite satellite4 is pointing to planet8", "Satellite satellite3 is pointing to groundstation6 and Satellite satellite4 is pointing to groundstation3"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 4746969715210492395, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation1, groundstation5, star10, groundstation3, planet7, star9, star2, planet8, groundstation6, star0, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite2 has following instruments onboard: instrument3, instrument4, instrument5. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument9 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument6 supports image of mode spectrograph0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite3 is pointing to star9. Satellite satellite5 is pointing to star2. Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to star10. Satellite satellite2 is pointing to star0. Satellite satellite1 is pointing to groundstation6. Power is available on the following satellite(s): satellite3, satellite2, satellite5, satellite0, satellite4. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite5 is pointing to star0 and Satellite satellite0 is pointing to planet8. B. Satellite satellite5 is pointing to groundstation5 and Satellite satellite5 is pointing to groundstation6. C. Satellite satellite1 is pointing to groundstation5 and Satellite satellite1 is pointing to groundstation1. D. Satellite satellite1 is pointing to groundstation3 and Satellite satellite1 is pointing to planet7.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite5 is pointing to star0 and Satellite satellite0 is pointing to planet8", "Satellite satellite5 is pointing to groundstation5 and Satellite satellite5 is pointing to groundstation6", "Satellite satellite1 is pointing to groundstation5 and Satellite satellite1 is pointing to groundstation1", "Satellite satellite1 is pointing to groundstation3 and Satellite satellite1 is pointing to planet7"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -3580532247658025952, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 7 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation0, star1, star3, groundstation2, star6, phenomenon5, groundstation4. There are 3 image mode(s): image0, image2, image1. There are 18 instrument(s), numbered consecutively.  Satellite satellite2 has following instruments onboard: instrument6. Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3, instrument5, instrument4. Satellite satellite3 has following instruments onboard: instrument7, instrument8. Satellite satellite6 has following instruments onboard: instrument16, instrument17, instrument15. Satellite satellite4 has following instruments onboard: instrument10, instrument11, instrument9. Satellite satellite5 has following instruments onboard: instrument14, instrument12, instrument13. Instrument instrument15 supports image of mode image0 and its calibration target is groundstation2. Instrument instrument3 supports image of mode image0 and its calibration target is star3. Instrument instrument13 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument2 supports image of mode image1 and its calibration target is star1. Instrument instrument11 supports image of mode image1 and its calibration target is star3. Instrument instrument4 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument12 supports image of mode image0 and its calibration target is groundstation0. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation2. Instrument instrument17 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument10 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode image1 and its calibration target is star3. Instrument instrument5 supports image of mode image1 and its calibration target is star3. Instrument instrument8 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument14 supports image of mode image1 and its calibration target is groundstation4. Instrument instrument1 supports image of mode image0 and its calibration target is groundstation4. Instrument instrument0 supports image of mode image2 and its calibration target is groundstation0. Instrument instrument9 supports image of mode image1 and its calibration target is star3. Instrument instrument16 supports image of mode image1 and its calibration target is groundstation2.  Currently, Satellite satellite5 is pointing to groundstation0. Satellite satellite0 is pointing to phenomenon5. Satellite satellite1 is pointing to phenomenon5. Satellite satellite2 is pointing to groundstation2. Satellite satellite6 is pointing to groundstation4. Satellite satellite3 is pointing to star6. Satellite satellite4 is pointing to star1. Power is available on the following satellite(s): satellite3, satellite2, satellite5, satellite6, satellite0, satellite4. Following instruments are powered on: instrument4. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite4 is pointing to star3 and Satellite satellite4 is pointing to groundstation4. B. Satellite satellite5 is pointing to groundstation4 and Following instruments are powered on: instrument12. C. Satellite satellite3 is pointing to star3 and Satellite satellite3 is pointing to star1. D. Satellite satellite1 is pointing to groundstation2 and Satellite satellite1 is pointing to star6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite4 is pointing to star3 and Satellite satellite4 is pointing to groundstation4", "Satellite satellite5 is pointing to groundstation4 and Following instruments are powered on: instrument12", "Satellite satellite3 is pointing to star3 and Satellite satellite3 is pointing to star1", "Satellite satellite1 is pointing to groundstation2 and Satellite satellite1 is pointing to star6"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 3894537783739277292, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 10 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation0, planet6, groundstation3, star4, planet5, star2. There are 3 image mode(s): thermograph0, image1, infrared2. There are 16 instrument(s), numbered consecutively.  Satellite satellite8 has following instruments onboard: instrument13, instrument14. Satellite satellite6 has following instruments onboard: instrument10, instrument9, instrument11. Satellite satellite0 has following instruments onboard: instrument0. Satellite satellite3 has following instruments onboard: instrument4, instrument5. Satellite satellite9 has following instruments onboard: instrument15. Satellite satellite2 has following instruments onboard: instrument3. Satellite satellite5 has following instruments onboard: instrument8. Satellite satellite4 has following instruments onboard: instrument6, instrument7. Satellite satellite7 has following instruments onboard: instrument12. Satellite satellite1 has following instruments onboard: instrument1, instrument2. Instrument instrument15 supports image of mode image1 and its calibration target is groundstation3. Instrument instrument12 supports image of mode infrared2 and its calibration target is groundstation3. Instrument instrument3 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument6 supports image of mode image1 and its calibration target is groundstation0. Instrument instrument2 supports image of mode infrared2 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument7 supports image of mode infrared2 and its calibration target is star2. Instrument instrument5 supports image of mode thermograph0 and its calibration target is groundstation1. Instrument instrument4 supports image of mode infrared2 and its calibration target is groundstation1. Instrument instrument1 supports image of mode thermograph0 and its calibration target is groundstation3. Instrument instrument9 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument8 supports image of mode image1 and its calibration target is star2. Instrument instrument0 supports image of mode image1 and its calibration target is star4. Instrument instrument13 supports image of mode thermograph0 and its calibration target is star4. Instrument instrument14 supports image of mode image1 and its calibration target is star2. Instrument instrument11 supports image of mode infrared2 and its calibration target is groundstation3.  Currently, Satellite satellite9 is pointing to star4. Satellite satellite6 is pointing to planet6. Satellite satellite7 is pointing to star2. Satellite satellite3 is pointing to groundstation1. Satellite satellite0 is pointing to groundstation1. Satellite satellite8 is pointing to planet6. Satellite satellite1 is pointing to groundstation0. Satellite satellite5 is pointing to planet6. Satellite satellite4 is pointing to planet6. Satellite satellite2 is pointing to planet6. Power is available on the following satellite(s): satellite7, satellite5, satellite0, satellite4, satellite8, satellite1, satellite2, satellite9. Following instruments are powered on: instrument5, instrument10. Following instruments are calibrated: instrument10. A infrared2 mode image of target planet6 is available. A thermograph0 mode image of target planet5 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite9 is pointing to groundstation3 and Satellite satellite9 is pointing to groundstation0. B. Satellite satellite0 is pointing to groundstation1 and Satellite satellite0 is pointing to planet5. C. Satellite satellite0 is pointing to star2 and Satellite satellite0 is pointing to planet5. D. Satellite satellite6 is pointing to groundstation1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite9 is pointing to groundstation3 and Satellite satellite9 is pointing to groundstation0", "Satellite satellite0 is pointing to groundstation1 and Satellite satellite0 is pointing to planet5", "Satellite satellite0 is pointing to star2 and Satellite satellite0 is pointing to planet5", "Satellite satellite6 is pointing to groundstation1"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 7560355384301785610, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 6 satellite(s), numbered consecutively. There are 11 possible target object(s): groundstation1, groundstation5, star10, groundstation3, planet7, star9, star2, planet8, groundstation6, star0, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 11 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument1. Satellite satellite2 has following instruments onboard: instrument3, instrument4, instrument5. Satellite satellite5 has following instruments onboard: instrument10, instrument9. Satellite satellite4 has following instruments onboard: instrument7, instrument8. Satellite satellite3 has following instruments onboard: instrument6. Satellite satellite1 has following instruments onboard: instrument2. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument2 supports image of mode infrared1 and its calibration target is star2. Instrument instrument4 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is groundstation5. Instrument instrument9 supports image of mode spectrograph0 and its calibration target is groundstation1. Instrument instrument6 supports image of mode spectrograph0 and its calibration target is groundstation4. Instrument instrument7 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument5 supports image of mode infrared1 and its calibration target is star2. Instrument instrument10 supports image of mode thermograph2 and its calibration target is groundstation4. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument8 supports image of mode spectrograph0 and its calibration target is groundstation3.  Currently, Satellite satellite1 is pointing to star9. Satellite satellite3 is pointing to star9. Satellite satellite5 is pointing to star2. Satellite satellite2 is pointing to star0. Satellite satellite0 is pointing to star10. Satellite satellite4 is pointing to star10. Power is available on the following satellite(s): satellite3, satellite2, satellite5, satellite4, satellite0. Following instruments are powered on: instrument2. Following instruments are calibrated: instrument2. A infrared1 mode image of target star10 is available. A thermograph2 mode image of target planet7 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite1 is pointing to groundstation3 and Satellite satellite1 is pointing to groundstation6. B. Satellite satellite2 is pointing to star0 and Satellite satellite2 is pointing to planet8. C. Satellite satellite5 is pointing to star0 and Satellite satellite5 is pointing to groundstation4. D. Power is available on the following satellite(s): satellite1 and Satellite satellite5 is pointing to star9.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite1 is pointing to groundstation3 and Satellite satellite1 is pointing to groundstation6", "Satellite satellite2 is pointing to star0 and Satellite satellite2 is pointing to planet8", "Satellite satellite5 is pointing to star0 and Satellite satellite5 is pointing to groundstation4", "Power is available on the following satellite(s): satellite1 and Satellite satellite5 is pointing to star9"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 2117244684303341158, "group": "reachable_atom_mc", "context": "This domain consists of satellite(s) equipped with various instruments that can be switched on when the power is available. Each instrument has a calibration target object and supports taking images of objects in particular modes. When the instrument power is switched on, it is not calibrated. To calibrate an instrument, the satellite should point to the calibration target object and the instrument should be powered on. To take an image of an object, the satellite must point to that object and the instrument must be calibrated. There are 3 satellite(s), numbered consecutively. There are 7 possible target object(s): groundstation1, groundstation3, planet5, star2, star0, phenomenon6, groundstation4. There are 3 image mode(s): thermograph2, infrared1, spectrograph0. There are 5 instrument(s), numbered consecutively.  Satellite satellite0 has following instruments onboard: instrument0, instrument2, instrument1. Satellite satellite1 has following instruments onboard: instrument3. Satellite satellite2 has following instruments onboard: instrument4. Instrument instrument4 supports image of mode infrared1 and its calibration target is star2. Instrument instrument0 supports image of mode spectrograph0 and its calibration target is star0. Instrument instrument2 supports image of mode spectrograph0 and its calibration target is groundstation3. Instrument instrument3 supports image of mode infrared1 and its calibration target is groundstation3. Instrument instrument1 supports image of mode spectrograph0 and its calibration target is star2.  Currently, Satellite satellite0 is pointing to star0. Satellite satellite2 is pointing to groundstation1. Satellite satellite1 is pointing to groundstation1. Power is available on the following satellite(s): satellite1, satellite0. Following instruments are powered on: instrument4. Following instruments are calibrated: instrument4. A thermograph2 mode image of target planet5 is available. A spectrograph0 mode image of target phenomenon6 is available. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. Satellite satellite0 is pointing to groundstation4 and Satellite satellite0 is pointing to groundstation1. B. Satellite satellite2 is pointing to phenomenon6 and Satellite satellite2 is pointing to planet5. C. Satellite satellite2 is pointing to planet5 and Satellite satellite2 is pointing to phenomenon6. D. Following instruments are powered on: instrument0 and Satellite satellite2 is pointing to phenomenon6.", "choices": {"label": ["A", "B", "C", "D"], "text": ["Satellite satellite0 is pointing to groundstation4 and Satellite satellite0 is pointing to groundstation1", "Satellite satellite2 is pointing to phenomenon6 and Satellite satellite2 is pointing to planet5", "Satellite satellite2 is pointing to planet5 and Satellite satellite2 is pointing to phenomenon6", "Following instruments are powered on: instrument0 and Satellite satellite2 is pointing to phenomenon6"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 2508909918848501120, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, michelle, heidi, vic, zoe, carol, xena, and dave. There are 8 items/roles: slinky, zebra, iceskates, guitar, whale, frisbee, necklace, and quadcopter. Currently, zoe is assigned frisbee, xena is assigned zebra, alice is assigned iceskates, heidi is assigned necklace, carol is assigned slinky, dave is assigned whale, michelle is assigned quadcopter, and vic is assigned guitar.", "question": "Which of the following options can hold in a state that can potentially be reached? A. dave is assigned slinky and zoe is assigned slinky. B. zoe is assigned whale and alice is assigned quadcopter. C. carol is assigned guitar and carol is assigned slinky. D. zoe is assigned guitar and zoe is assigned iceskates.", "choices": {"label": ["A", "B", "C", "D"], "text": ["dave is assigned slinky and zoe is assigned slinky", "zoe is assigned whale and alice is assigned quadcopter", "carol is assigned guitar and carol is assigned slinky", "zoe is assigned guitar and zoe is assigned iceskates"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 7533023007811137076, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, michelle, heidi, vic, zoe, carol, xena, and dave. There are 8 items/roles: slinky, zebra, iceskates, guitar, whale, frisbee, necklace, and quadcopter. Currently, zoe is assigned whale, xena is assigned quadcopter, dave is assigned slinky, alice is assigned zebra, carol is assigned guitar, michelle is assigned iceskates, heidi is assigned frisbee, and vic is assigned necklace.", "question": "Which of the following options can hold in a state that can potentially be reached? A. michelle is assigned frisbee and michelle is assigned slinky. B. dave is assigned whale and dave is assigned zebra. C. heidi is assigned guitar and alice is assigned frisbee. D. carol is assigned iceskates and carol is assigned whale.", "choices": {"label": ["A", "B", "C", "D"], "text": ["michelle is assigned frisbee and michelle is assigned slinky", "dave is assigned whale and dave is assigned zebra", "heidi is assigned guitar and alice is assigned frisbee", "carol is assigned iceskates and carol is assigned whale"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 8268272490497195989, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, michelle, heidi, vic, zoe, carol, xena, and dave. There are 8 items/roles: slinky, zebra, iceskates, guitar, whale, frisbee, necklace, and quadcopter. Currently, zoe is assigned whale, michelle is assigned zebra, dave is assigned slinky, xena is assigned iceskates, alice is assigned quadcopter, heidi is assigned guitar, vic is assigned necklace, and carol is assigned frisbee.", "question": "Which of the following options can hold in a state that can potentially be reached? A. heidi is assigned iceskates and heidi is assigned zebra. B. alice is assigned slinky and alice is assigned necklace. C. dave is assigned iceskates and carol is assigned zebra. D. vic is assigned frisbee and xena is assigned frisbee.", "choices": {"label": ["A", "B", "C", "D"], "text": ["heidi is assigned iceskates and heidi is assigned zebra", "alice is assigned slinky and alice is assigned necklace", "dave is assigned iceskates and carol is assigned zebra", "vic is assigned frisbee and xena is assigned frisbee"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": -1293079638875164679, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, quentin, vic, liam, xena, and frank. There are 6 items/roles: ratchet, sander, nibbler, wrench, knead, and pliers. Currently, liam is assigned knead, quentin is assigned pliers, bob is assigned wrench, xena is assigned ratchet, frank is assigned nibbler, and vic is assigned sander.", "question": "Which of the following options can hold in a state that can potentially be reached? A. quentin is assigned wrench and quentin is assigned knead. B. vic is assigned pliers and liam is assigned ratchet. C. vic is assigned ratchet and xena is assigned ratchet. D. xena is assigned pliers and xena is assigned nibbler.", "choices": {"label": ["A", "B", "C", "D"], "text": ["quentin is assigned wrench and quentin is assigned knead", "vic is assigned pliers and liam is assigned ratchet", "vic is assigned ratchet and xena is assigned ratchet", "xena is assigned pliers and xena is assigned nibbler"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -9082535533151561432, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, michelle, heidi, vic, zoe, carol, xena, and dave. There are 8 items/roles: slinky, zebra, iceskates, guitar, whale, frisbee, necklace, and quadcopter. Currently, michelle is assigned zebra, zoe is assigned frisbee, xena is assigned iceskates, vic is assigned quadcopter, carol is assigned slinky, heidi is assigned guitar, alice is assigned whale, and dave is assigned necklace.", "question": "Which of the following options can hold in a state that can potentially be reached? A. vic is assigned quadcopter and vic is assigned guitar. B. alice is assigned iceskates and carol is assigned iceskates. C. xena is assigned necklace and xena is assigned zebra. D. carol is assigned whale and dave is assigned slinky.", "choices": {"label": ["A", "B", "C", "D"], "text": ["vic is assigned quadcopter and vic is assigned guitar", "alice is assigned iceskates and carol is assigned iceskates", "xena is assigned necklace and xena is assigned zebra", "carol is assigned whale and dave is assigned slinky"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 7950987861608913085, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, quentin, vic, liam, xena, and frank. There are 6 items/roles: ratchet, sander, nibbler, wrench, knead, and pliers. Currently, liam is assigned nibbler, xena is assigned pliers, bob is assigned wrench, frank is assigned knead, quentin is assigned ratchet, and vic is assigned sander.", "question": "Which of the following options can hold in a state that can potentially be reached? A. liam is assigned wrench and xena is assigned wrench. B. liam is assigned knead and frank is assigned ratchet. C. bob is assigned ratchet and vic is assigned ratchet. D. quentin is assigned nibbler and xena is assigned nibbler.", "choices": {"label": ["A", "B", "C", "D"], "text": ["liam is assigned wrench and xena is assigned wrench", "liam is assigned knead and frank is assigned ratchet", "bob is assigned ratchet and vic is assigned ratchet", "quentin is assigned nibbler and xena is assigned nibbler"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -661948525496087502, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, quentin, vic, liam, xena, and frank. There are 6 items/roles: ratchet, sander, nibbler, wrench, knead, and pliers. Currently, xena is assigned sander, vic is assigned ratchet, liam is assigned pliers, bob is assigned wrench, frank is assigned nibbler, and quentin is assigned knead.", "question": "Which of the following options can hold in a state that can potentially be reached? A. xena is assigned nibbler and xena is assigned sander. B. bob is assigned nibbler and quentin is assigned nibbler. C. xena is assigned knead and vic is assigned knead. D. vic is assigned nibbler and frank is assigned ratchet.", "choices": {"label": ["A", "B", "C", "D"], "text": ["xena is assigned nibbler and xena is assigned sander", "bob is assigned nibbler and quentin is assigned nibbler", "xena is assigned knead and vic is assigned knead", "vic is assigned nibbler and frank is assigned ratchet"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -3661908220997215533, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 8 agents: alice, michelle, heidi, vic, zoe, carol, xena, and dave. There are 8 items/roles: slinky, zebra, iceskates, guitar, whale, frisbee, necklace, and quadcopter. Currently, xena is assigned quadcopter, michelle is assigned zebra, dave is assigned slinky, zoe is assigned frisbee, alice is assigned iceskates, carol is assigned whale, heidi is assigned necklace, and vic is assigned guitar.", "question": "Which of the following options can hold in a state that can potentially be reached? A. michelle is assigned slinky and vic is assigned frisbee. B. dave is assigned whale and dave is assigned iceskates. C. alice is assigned necklace and carol is assigned necklace. D. carol is assigned frisbee and carol is assigned necklace.", "choices": {"label": ["A", "B", "C", "D"], "text": ["michelle is assigned slinky and vic is assigned frisbee", "dave is assigned whale and dave is assigned iceskates", "alice is assigned necklace and carol is assigned necklace", "carol is assigned frisbee and carol is assigned necklace"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": 6697203447928983700, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 6 agents: bob, quentin, vic, liam, xena, and frank. There are 6 items/roles: ratchet, sander, nibbler, wrench, knead, and pliers. Currently, vic is assigned ratchet, xena is assigned knead, frank is assigned pliers, liam is assigned wrench, quentin is assigned nibbler, and bob is assigned sander.", "question": "Which of the following options can hold in a state that can potentially be reached? A. vic is assigned nibbler and vic is assigned ratchet. B. quentin is assigned ratchet and frank is assigned ratchet. C. liam is assigned sander and bob is assigned wrench. D. bob is assigned sander and xena is assigned sander.", "choices": {"label": ["A", "B", "C", "D"], "text": ["vic is assigned nibbler and vic is assigned ratchet", "quentin is assigned ratchet and frank is assigned ratchet", "liam is assigned sander and bob is assigned wrench", "bob is assigned sander and xena is assigned sander"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": 6124153306201193586, "group": "reachable_atom_mc", "context": "This is a swap domain where agents are swapping items or roles. Each agent is always assigned a single item/role. The goal is to obtain desired items/roles assigned. There are 7 agents: bob, heidi, alice, ted, kevin, xena, and dave. There are 7 items/roles: quince, leek, valerian, parsnip, yam, ulluco, and mushroom. Currently, kevin is assigned mushroom, ted is assigned leek, bob is assigned parsnip, alice is assigned valerian, heidi is assigned quince, xena is assigned ulluco, and dave is assigned yam.", "question": "Which of the following options can hold in a state that can potentially be reached? A. bob is assigned leek and alice is assigned leek. B. bob is assigned ulluco and xena is assigned parsnip. C. xena is assigned quince and xena is assigned leek. D. bob is assigned quince and alice is assigned quince.", "choices": {"label": ["A", "B", "C", "D"], "text": ["bob is assigned leek and alice is assigned leek", "bob is assigned ulluco and xena is assigned parsnip", "xena is assigned quince and xena is assigned leek", "bob is assigned quince and alice is assigned quince"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 2376773130592837971, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf1 is at location20. bed1 is at location13. garbagecan1 is at location2. shelf5 is at location22. drawer5 and drawer4 are at location12. drawer2 is at location18. drawer6 is at location1. laundryhamper1 is at location8. shelf4 is at location23. shelf2 is at location25. desk1 is at location3. drawer1 is at location21. safe1 is at location6. drawer3 is at location17. shelf6 is at location24. shelf3 is at location11. desk2 is at location10.  Currently, the objects are at locations as follows. pillow1, cellphone1, laptop1, book1, pillow2, and laptop2 are at location13. pencil3, pen1, mug2, and cd3 are at location10. window2 is at location4. bowl1, alarmclock1, pencil1, cd1, and mug1 are at location3. creditcard1 and pencil2 are at location22. cellphone3 is at location12. keychain2 and keychain1 are at location6. desklamp1, bowl2, and alarmclock3 are at location23. chair2 is at location26. blinds2 is at location15. baseballbat1 is at location9. mirror1 is at location19. cd2 is at location2. laundryhamperlid1 is at location8. bowl3 is at location24. alarmclock2 is at location11. blinds1 is at location16. window1 is at location5. basketball1 is at location7. lightswitch1 is at location14. chair1 is at location21. agent agent1 is at location location10. The objects are in/on receptacle as follows. alarmclock3, bowl2, pencil3, desklamp1, cd3, pen1, and mug2 are on desk2. desklamp1, bowl2, and alarmclock3 are on shelf4. alarmclock2 is on shelf3. cd1, alarmclock1, pencil1, bowl1, and mug1 are on desk1. creditcard1 and pencil2 are on shelf5. keychain1 and keychain2 are in safe1. pillow2, laptop1, cellphone1, pillow1, laptop2, and book1 are in bed1. bowl3 is on shelf6. cellphone3 is in drawer5. cd2 is in garbagecan1. drawer6, safe1, drawer3, and drawer1 are closed. desklamp1 is off. Nothing has been validated. agent1 is holding object cellphone2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that an object of type windowtype is cool and is in a receptacle of type handtowelholdertype and pencil3 is on desk2. B. It has been validated that two objects of type curtainstype are in a receptacle of type fridgetype and cd2 is at location2. C. It has been validated that an object of type pentype is in a receptacle of type drawertype and Nothing has been validated. D. It has been validated that an object of type bowltype is in a receptacle of type desktype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type windowtype is cool and is in a receptacle of type handtowelholdertype and pencil3 is on desk2", "It has been validated that two objects of type curtainstype are in a receptacle of type fridgetype and cd2 is at location2", "It has been validated that an object of type pentype is in a receptacle of type drawertype and Nothing has been validated", "It has been validated that an object of type bowltype is in a receptacle of type desktype"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 2453498690548128359, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. spoon3, tomato1, saltshaker2, houseplant1, soapbottle1, butterknife1, glassbottle3, knife1, fork2, and lettuce3 are at location3. potato1, lettuce1, cup1, cup2, apple1, and potato2 are at location10. cellphone1, pan1, and peppershaker1 are at location11. spoon2, fork1, sink1, and spatula2 are at location6. spoon1, creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, and statue1 are at location2. saltshaker3 is at location26. egg2 and apple2 are at location8. glassbottle1 is at location19. stoveknob4 is at location7. window2 is at location28. dishsponge1 is at location15. peppershaker2 and cellphone2 are at location20. plate1 is at location4. papertowelroll1 and vase2 are at location29. chair1 is at location23. saltshaker1 is at location16. spatula1 is at location27. glassbottle2 and bowl1 are at location14. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. vase1 is at location17. stoveknob1 is at location18. agent agent1 is at location location24. The objects are in/on receptacle as follows. saltshaker3 is on shelf3. apple2 and egg2 are in garbagecan1. houseplant1, lettuce3, knife1, spoon3, fork2, butterknife1, soapbottle1, saltshaker2, glassbottle3, and tomato1 are on countertop3. cellphone3, creditcard1, bread1, mug1, creditcard2, statue1, spoon1, and lettuce2 are on countertop1. bowl1 and glassbottle2 are in cabinet4. potato1, apple1, cup2, lettuce1, cup1, and potato2 are in fridge1. cellphone2 and peppershaker2 are in drawer1. pan1, cellphone1, and peppershaker1 are on countertop2. fork1, spatula2, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. papertowelroll1 and vase2 are on shelf2. vase1 is on shelf1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. spatula1 is in drawer3. plate1 is in cabinet6. pan1 is on stoveburner2. glassbottle1 is in cabinet1. dishsponge1 is in drawer2. cabinet5, cabinet2, drawer3, drawer1, cabinet1, microwave1, drawer2, and fridge1 are closed. egg1 is hot. Nothing has been validated. agent1 is holding object egg1. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. spatula2 is hot. B. It has been validated that an object of type blindstype is examined under an object of type sinktype and pan1 is at location11. C. It has been validated that an object of type peppershakertype is in a receptacle of type shelftype and Nothing has been validated. D. It has been validated that an object of type spoontype is in a receptacle of type countertoptype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["spatula2 is hot", "It has been validated that an object of type blindstype is examined under an object of type sinktype and pan1 is at location11", "It has been validated that an object of type peppershakertype is in a receptacle of type shelftype and Nothing has been validated", "It has been validated that an object of type spoontype is in a receptacle of type countertoptype"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -685050466740604210, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. spoon3, tomato1, saltshaker2, houseplant1, soapbottle1, butterknife1, glassbottle3, knife1, fork2, and lettuce3 are at location3. potato1, lettuce1, cup1, cup2, apple1, and potato2 are at location10. cellphone1, pan1, and peppershaker1 are at location11. spoon2, fork1, sink1, and spatula2 are at location6. spoon1, creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, and statue1 are at location2. saltshaker3 is at location26. egg2 and apple2 are at location8. glassbottle1 is at location19. stoveknob4 is at location7. window2 is at location28. dishsponge1 is at location15. peppershaker2 and cellphone2 are at location20. plate1 is at location4. papertowelroll1 and vase2 are at location29. chair1 is at location23. saltshaker1 is at location16. spatula1 is at location27. glassbottle2 and bowl1 are at location14. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. vase1 is at location17. stoveknob1 is at location18. agent agent1 is at location location24. The objects are in/on receptacle as follows. saltshaker3 is on shelf3. apple2 and egg2 are in garbagecan1. houseplant1, lettuce3, knife1, spoon3, fork2, butterknife1, soapbottle1, saltshaker2, glassbottle3, and tomato1 are on countertop3. cellphone3, creditcard1, bread1, mug1, creditcard2, statue1, spoon1, and lettuce2 are on countertop1. bowl1 and glassbottle2 are in cabinet4. potato1, apple1, cup2, lettuce1, cup1, and potato2 are in fridge1. cellphone2 and peppershaker2 are in drawer1. pan1, cellphone1, and peppershaker1 are on countertop2. fork1, spatula2, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. papertowelroll1 and vase2 are on shelf2. vase1 is on shelf1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. spatula1 is in drawer3. plate1 is in cabinet6. pan1 is on stoveburner2. glassbottle1 is in cabinet1. dishsponge1 is in drawer2. cabinet5, cabinet2, drawer3, drawer1, cabinet1, drawer2, and fridge1 are closed. microwave1 is checked. egg1 is hot. microwave1 is open. Nothing has been validated. agent1 is holding object egg1. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that two objects of type cuptype are in a receptacle of type fridgetype. B. It has been validated that an object of type tissueboxtype is in a receptacle of type sofatype. C. It has been validated that an object of type platetype is hot and is in a receptacle of type cabinettype and It has been validated that an object of type cuptype is hot and is in a receptacle of type cabinettype. D. carttype is on stoveburner3.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that two objects of type cuptype are in a receptacle of type fridgetype", "It has been validated that an object of type tissueboxtype is in a receptacle of type sofatype", "It has been validated that an object of type platetype is hot and is in a receptacle of type cabinettype and It has been validated that an object of type cuptype is hot and is in a receptacle of type cabinettype", "carttype is on stoveburner3"]}, "query": "Which fact is reachable from this state?", "answer": "A"}
{"id": -1324058002387925924, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. spoon3, tomato1, saltshaker2, houseplant1, soapbottle1, butterknife1, glassbottle3, knife1, fork2, and lettuce3 are at location3. potato1, lettuce1, cup1, cup2, apple1, and potato2 are at location10. cellphone1, pan1, and peppershaker1 are at location11. spoon2, fork1, sink1, and spatula2 are at location6. spoon1, creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, and statue1 are at location2. saltshaker3 is at location26. glassbottle1 is at location19. stoveknob4 is at location7. window2 is at location28. dishsponge1 is at location15. peppershaker2 and cellphone2 are at location20. plate1 is at location4. papertowelroll1 and vase2 are at location29. chair1 is at location23. saltshaker1 is at location16. apple2 and egg1 are at location8. spatula1 is at location27. glassbottle2 and bowl1 are at location14. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. vase1 is at location17. stoveknob1 is at location18. agent agent1 is at location location16. The objects are in/on receptacle as follows. saltshaker3 is on shelf3. apple2 and egg1 are in garbagecan1. houseplant1, lettuce3, knife1, spoon3, fork2, butterknife1, soapbottle1, saltshaker2, glassbottle3, and tomato1 are on countertop3. cellphone3, creditcard1, bread1, mug1, creditcard2, statue1, spoon1, and lettuce2 are on countertop1. bowl1 and glassbottle2 are in cabinet4. potato1, apple1, cup2, lettuce1, cup1, and potato2 are in fridge1. cellphone2 and peppershaker2 are in drawer1. pan1, cellphone1, and peppershaker1 are on countertop2. fork1, spatula2, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. papertowelroll1 and vase2 are on shelf2. vase1 is on shelf1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. spatula1 is in drawer3. plate1 is in cabinet6. pan1 is on stoveburner2. glassbottle1 is in cabinet1. dishsponge1 is in drawer2. cabinet5, cabinet2, drawer3, drawer1, cabinet1, microwave1, drawer2, and fridge1 are closed. Nothing has been validated. agent1 is holding object egg2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. location21 is off and fridge1 is closed. B. egg1 is in garbagecan1 and It has been validated that an object of type saltshakertype is examined under an object of type desklamptype. C. It has been validated that an object of type cuptype is in a receptacle of type fridgetype. D. It has been validated that an object of type tomatotype is in a receptacle of type sinkbasintype and Nothing has been validated.", "choices": {"label": ["A", "B", "C", "D"], "text": ["location21 is off and fridge1 is closed", "egg1 is in garbagecan1 and It has been validated that an object of type saltshakertype is examined under an object of type desklamptype", "It has been validated that an object of type cuptype is in a receptacle of type fridgetype", "It has been validated that an object of type tomatotype is in a receptacle of type sinkbasintype and Nothing has been validated"]}, "query": "Which fact is reachable from this state?", "answer": "C"}
{"id": *******************, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. spoon3, tomato1, saltshaker2, houseplant1, soapbottle1, butterknife1, glassbottle3, knife1, fork2, and lettuce3 are at location3. potato1, lettuce1, cup1, cup2, apple1, and potato2 are at location10. cellphone1, pan1, and peppershaker1 are at location11. spoon2, fork1, sink1, and spatula2 are at location6. spoon1, creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, and statue1 are at location2. saltshaker3 is at location26. glassbottle1 is at location19. stoveknob4 is at location7. window2 is at location28. dishsponge1 is at location15. peppershaker2 and cellphone2 are at location20. plate1 is at location4. papertowelroll1 and vase2 are at location29. chair1 is at location23. saltshaker1 is at location16. apple2 and egg1 are at location8. spatula1 is at location27. glassbottle2 and bowl1 are at location14. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. vase1 is at location17. stoveknob1 is at location18. agent agent1 is at location location20. The objects are in/on receptacle as follows. saltshaker3 is on shelf3. apple2 and egg1 are in garbagecan1. houseplant1, lettuce3, knife1, spoon3, fork2, butterknife1, soapbottle1, saltshaker2, glassbottle3, and tomato1 are on countertop3. cellphone3, creditcard1, bread1, mug1, creditcard2, statue1, spoon1, and lettuce2 are on countertop1. bowl1 and glassbottle2 are in cabinet4. potato1, apple1, cup2, lettuce1, cup1, and potato2 are in fridge1. cellphone2 and peppershaker2 are in drawer1. pan1, cellphone1, and peppershaker1 are on countertop2. fork1, spatula2, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. papertowelroll1 and vase2 are on shelf2. vase1 is on shelf1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. spatula1 is in drawer3. plate1 is in cabinet6. pan1 is on stoveburner2. glassbottle1 is in cabinet1. dishsponge1 is in drawer2. cabinet5, cabinet2, drawer3, drawer1, cabinet1, microwave1, drawer2, and fridge1 are closed. egg2 is hot. Nothing has been validated. agent1 is holding object egg2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that an object of type teddybeartype is in a receptacle of type bedtype and lettuce3 is at location3. B. It has been validated that an object of type vasetype is cool and is in a receptacle of type tvstandtype. C. It has been validated that an object of type bowltype is cool and is in a receptacle of type microwavetype and It has been validated that an object of type spoontype is clean and is in a receptacle of type sinkbasintype. D. It has been validated that an object of type potatotype is in a receptacle of type fridgetype.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type teddybeartype is in a receptacle of type bedtype and lettuce3 is at location3", "It has been validated that an object of type vasetype is cool and is in a receptacle of type tvstandtype", "It has been validated that an object of type bowltype is cool and is in a receptacle of type microwavetype and It has been validated that an object of type spoontype is clean and is in a receptacle of type sinkbasintype", "It has been validated that an object of type potatotype is in a receptacle of type fridgetype"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 8195910789613811634, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 1 bowl, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 2 creditcards, 2 cups, 1 dishsponge, 2 eggs, 1 faucet, 2 forks, 3 glassbottles, 1 houseplant, 1 knife, 3 lettuces, 1 lightswitch, 1 mug, 1 pan, 1 papertowelroll, 2 peppershakers, 1 plate, 2 potatoes, 1 pot, 3 saltshakers, 1 sink, 1 soapbottle, 2 spatulas, 3 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 11 receptacle types: 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. spoon3, tomato1, saltshaker2, houseplant1, soapbottle1, butterknife1, glassbottle3, knife1, fork2, and lettuce3 are at location3. potato1, lettuce1, cup1, cup2, apple1, and potato2 are at location10. cellphone1, pan1, and peppershaker1 are at location11. spoon2, fork1, sink1, and spatula2 are at location6. spoon1, creditcard1, lettuce2, mug1, bread1, cellphone3, creditcard2, and statue1 are at location2. saltshaker3 is at location26. egg2 and apple2 are at location8. glassbottle1 is at location19. stoveknob4 is at location7. window2 is at location28. dishsponge1 is at location15. peppershaker2 and cellphone2 are at location20. plate1 is at location4. papertowelroll1 and vase2 are at location29. chair1 is at location23. saltshaker1 is at location16. spatula1 is at location27. glassbottle2 and bowl1 are at location14. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. vase1 is at location17. stoveknob1 is at location18. agent agent1 is at location location20. The objects are in/on receptacle as follows. saltshaker3 is on shelf3. apple2 and egg2 are in garbagecan1. houseplant1, lettuce3, knife1, spoon3, fork2, butterknife1, soapbottle1, saltshaker2, glassbottle3, and tomato1 are on countertop3. cellphone3, creditcard1, bread1, mug1, creditcard2, statue1, spoon1, and lettuce2 are on countertop1. bowl1 and glassbottle2 are in cabinet4. potato1, apple1, cup2, lettuce1, cup1, and potato2 are in fridge1. cellphone2 and peppershaker2 are in drawer1. pan1, cellphone1, and peppershaker1 are on countertop2. fork1, spatula2, and spoon2 are in sinkbasin1. saltshaker1 is in cabinet3. papertowelroll1 and vase2 are on shelf2. vase1 is on shelf1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. spatula1 is in drawer3. plate1 is in cabinet6. pan1 is on stoveburner2. glassbottle1 is in cabinet1. dishsponge1 is in drawer2. cabinet5, cabinet2, drawer3, cabinet1, microwave1, drawer2, and fridge1 are closed. drawer1 is checked. drawer1 is open. Nothing has been validated. agent1 is holding object egg1. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that an object of type platetype is cool and is in a receptacle of type ottomantype and glassbottle2 is at location14. B. agent1 is holding object faucet1 and creditcard2 is on countertop1. C. It has been validated that an object of type potatotype is clean and is in a receptacle of type fridgetype and It has been validated that an object of type pantype is in a receptacle of type sinkbasintype. D. agent agent1 is at location location14.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type platetype is cool and is in a receptacle of type ottomantype and glassbottle2 is at location14", "agent1 is holding object faucet1 and creditcard2 is on countertop1", "It has been validated that an object of type potatotype is clean and is in a receptacle of type fridgetype and It has been validated that an object of type pantype is in a receptacle of type sinkbasintype", "agent agent1 is at location location14"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": -8377900159947648989, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 21 object types: 3 alarmclocks, 1 baseballbat, 1 basketball, 2 blindss, 1 book, 3 bowls, 3 cds, 3 cellphones, 2 chairs, 1 creditcard, 1 desklamp, 2 keychains, 2 laptops, 1 laundryhamperlid, 1 lightswitch, 1 mirror, 2 mugs, 3 pencils, 1 pen, 2 pillows, 2 windows, 7 receptacle types: 1 bed, 2 desks, 6 drawers, 1 garbagecan, 1 laundryhamper, 1 safe, 6 shelves, and 27 locations all numbered consecutively.  The receptacles are at locations as follows. shelf1 is at location20. bed1 is at location13. garbagecan1 is at location2. shelf5 is at location22. drawer5 and drawer4 are at location12. drawer2 is at location18. drawer6 is at location1. laundryhamper1 is at location8. shelf4 is at location23. shelf2 is at location25. desk1 is at location3. drawer1 is at location21. safe1 is at location6. drawer3 is at location17. shelf6 is at location24. shelf3 is at location11. desk2 is at location10.  Currently, the objects are at locations as follows. pillow1, cellphone1, laptop1, book1, pillow2, and laptop2 are at location13. pencil3, cellphone2, pen1, and cd3 are at location10. window2 is at location4. bowl1, alarmclock1, pencil1, cd1, and mug1 are at location3. creditcard1 and pencil2 are at location22. cellphone3 is at location12. keychain2 and keychain1 are at location6. desklamp1, bowl2, and alarmclock3 are at location23. chair2 is at location26. window1 is at location5. blinds2 is at location15. baseballbat1 is at location9. mirror1 is at location19. cd2 is at location2. laundryhamperlid1 is at location8. bowl3 is at location24. alarmclock2 is at location11. blinds1 is at location16. basketball1 is at location7. lightswitch1 is at location14. chair1 is at location21. agent agent1 is at location location10. The objects are in/on receptacle as follows. alarmclock3, bowl2, pencil3, desklamp1, cellphone2, cd3, and pen1 are on desk2. desklamp1, bowl2, and alarmclock3 are on shelf4. alarmclock2 is on shelf3. cd1, alarmclock1, pencil1, bowl1, and mug1 are on desk1. creditcard1 and pencil2 are on shelf5. keychain1 and keychain2 are in safe1. pillow2, laptop1, cellphone1, pillow1, laptop2, and book1 are in bed1. bowl3 is on shelf6. cellphone3 is in drawer5. cd2 is in garbagecan1. drawer6, safe1, drawer3, and drawer1 are closed. desklamp1 is off. Nothing has been validated. agent1 is holding object mug2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. shelf1 is closed. B. showerdoortype is on desk2 and keychain1 is in safe1. C. blinds1 is at location16 and drawer2 is open. D. It has been validated that an object of type alarmclocktype is in a receptacle of type shelftype and agent agent1 is at location location12.", "choices": {"label": ["A", "B", "C", "D"], "text": ["shelf1 is closed", "showerdoortype is on desk2 and keychain1 is in safe1", "blinds1 is at location16 and drawer2 is open", "It has been validated that an object of type alarmclocktype is in a receptacle of type shelftype and agent agent1 is at location location12"]}, "query": "Which fact is reachable from this state?", "answer": "D"}
{"id": 371853594057797496, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. cellphone1, lettuce1, lettuce2, knife1, cellphone2, peppershaker1, plate1, and apple1 are at location2. saltshaker1 is at location15. fork1 and dishsponge1 are at location27. potato1, mug1, bowl1, egg1, tomato1, egg2, and plate2 are at location10. knife2, cellphone3, glassbottle1, papertowelroll1, houseplant1, spatula1, butterknife1, creditcard1, bread1, plate3, and spoon2 are at location3. soapbottle2 and statue1 are at location26. dishsponge2 and vase1 are at location13. peppershaker3 is at location16. stoveknob4 is at location7. glassbottle3, apple2, and potato3 are at location8. window2 is at location28. spatula3, glassbottle2, sink1, spatula2, and potato2 are at location6. bowl2 is at location29. peppershaker2 is at location20. cup1 is at location24. chair1 is at location23. pan1 and spoon1 are at location11. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. soapbottle1 is at location4. vase2 is at location17. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. stoveknob1 is at location18. agent agent1 is at location location26. The objects are in/on receptacle as follows. apple2, potato3, and glassbottle3 are in garbagecan1. houseplant1, glassbottle1, spoon2, cellphone3, knife2, papertowelroll1, bread1, creditcard1, spatula1, plate3, and butterknife1 are on countertop3. cellphone2, plate1, lettuce1, peppershaker1, knife1, apple1, cellphone1, and lettuce2 are on countertop1. egg2, egg1, potato1, plate2, tomato1, mug1, and bowl1 are in fridge1. cellphone2 is on plate1. spoon1 and pan1 are on countertop2. vase1 and dishsponge2 are in cabinet5. fork1 and dishsponge1 are in drawer3. potato2, glassbottle2, spatula2, and spatula3 are in sinkbasin1. statue1 and soapbottle2 are on shelf3. peppershaker2 is in drawer1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. peppershaker3 is in cabinet3. saltshaker1 is in drawer2. pan1 is on stoveburner2. bowl2 is on shelf2. cup1 is in microwave1. soapbottle1 is in cabinet6. vase2 is on shelf1. cabinet5, cabinet2, drawer3, drawer1, cabinet1, microwave1, drawer2, and fridge1 are closed. mug1 is cool. Nothing has been validated. agent1 is holding object mug2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that an object of type bowltype is cool and is in a receptacle of type microwavetype and It has been validated that an object of type breadtype is hot and is in a receptacle of type garbagecantype. B. It has been validated that an object of type bowltype is in a receptacle of type shelftype. C. basketball is cool. D. agent1 is holding object location26.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type bowltype is cool and is in a receptacle of type microwavetype and It has been validated that an object of type breadtype is hot and is in a receptacle of type garbagecantype", "It has been validated that an object of type bowltype is in a receptacle of type shelftype", "basketball is cool", "agent1 is holding object location26"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": 600376431830331291, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 2 apples, 2 bowls, 1 bread, 1 butterknife, 3 cellphones, 2 chairs, 1 creditcard, 1 cup, 2 dishsponges, 2 eggs, 1 faucet, 1 fork, 3 glassbottles, 1 houseplant, 2 knives, 2 lettuces, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 3 peppershakers, 3 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 2 soapbottles, 3 spatulas, 2 spoons, 1 statue, 4 stoveknobs, 1 tomato, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 31 locations all numbered consecutively.  The receptacles are at locations as follows. countertop3 is at location3. garbagecan1 is at location8. coffeemachine1 and countertop2 are at location11. cabinet3 is at location16. toaster1 is at location9. shelf1 is at location17. countertop1 is at location2. stoveburner1 and stoveburner3 are at location5. cabinet1 is at location19. cabinet4 is at location14. fridge1 is at location10. microwave1 is at location24. shelf3 is at location26. shelf2 is at location29. cabinet2 is at location22. cabinet5 is at location13. stoveburner2 and stoveburner4 are at location21. drawer1 is at location20. drawer3 is at location27. cabinet6 is at location4. sinkbasin1 is at location6. drawer2 is at location15.  Currently, the objects are at locations as follows. cellphone1, lettuce1, lettuce2, knife1, cellphone2, peppershaker1, plate1, and apple1 are at location2. saltshaker1 is at location15. fork1 and dishsponge1 are at location27. potato1, mug1, bowl1, egg1, tomato1, egg2, and plate2 are at location10. knife2, cellphone3, glassbottle1, papertowelroll1, houseplant1, spatula1, butterknife1, creditcard1, bread1, plate3, and spoon2 are at location3. soapbottle2 and statue1 are at location26. dishsponge2 and vase1 are at location13. peppershaker3 is at location16. stoveknob4 is at location7. glassbottle3, apple2, and potato3 are at location8. window2 is at location28. spatula3, glassbottle2, sink1, spatula2, and potato2 are at location6. bowl2 is at location29. peppershaker2 is at location20. cup1 is at location24. chair1 is at location23. pan1 and spoon1 are at location11. pot1 is at location5. stoveknob2 and stoveknob3 are at location12. soapbottle1 is at location4. vase2 is at location17. lightswitch1 is at location25. window1 is at location30. chair2 is at location1. stoveknob1 is at location18. agent agent1 is at location location27. The objects are in/on receptacle as follows. apple2, potato3, and glassbottle3 are in garbagecan1. houseplant1, glassbottle1, spoon2, cellphone3, knife2, papertowelroll1, bread1, creditcard1, spatula1, plate3, and butterknife1 are on countertop3. cellphone2, plate1, lettuce1, peppershaker1, knife1, apple1, cellphone1, and lettuce2 are on countertop1. egg2, egg1, potato1, plate2, tomato1, mug1, and bowl1 are in fridge1. cellphone2 is on plate1. spoon1 and pan1 are on countertop2. vase1 and dishsponge2 are in cabinet5. fork1 and dishsponge1 are in drawer3. potato2, glassbottle2, spatula2, and spatula3 are in sinkbasin1. statue1 and soapbottle2 are on shelf3. peppershaker2 is in drawer1. pot1 is on stoveburner3. pot1 is on stoveburner1. pan1 is on stoveburner4. peppershaker3 is in cabinet3. saltshaker1 is in drawer2. pan1 is on stoveburner2. bowl2 is on shelf2. cup1 is in microwave1. soapbottle1 is in cabinet6. vase2 is on shelf1. cabinet5, cabinet2, drawer3, drawer1, cabinet1, microwave1, drawer2, and fridge1 are closed. mug1 is cool. Nothing has been validated. agent1 is holding object mug2. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that an object of type cuptype is hot and is in a receptacle of type cabinettype and It has been validated that two objects of type eggtype are in a receptacle of type garbagecantype. B. It has been validated that an object of type cuptype is in a receptacle of type microwavetype and agent agent1 is at location location20. C. pottype is hot. D. lightswitch is at location4 and potato3 is in garbagecan1.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type cuptype is hot and is in a receptacle of type cabinettype and It has been validated that two objects of type eggtype are in a receptacle of type garbagecantype", "It has been validated that an object of type cuptype is in a receptacle of type microwavetype and agent agent1 is at location location20", "pottype is hot", "lightswitch is at location4 and potato3 is in garbagecan1"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
{"id": -******************, "group": "reachable_atom_mc", "context": "This is an alfworld domain where an agent is asked to carry different tasks such as: picking up objects, opening or closing receptacles, warming up an object in a microwave, cleaning an object in a sink, or toggling an object.  There are 34 object types: 3 apples, 1 bowl, 1 bread, 2 butterknives, 3 cellphones, 2 chairs, 3 creditcards, 3 cups, 3 dishsponges, 2 eggs, 1 faucet, 2 forks, 2 glassbottles, 1 houseplant, 1 knife, 1 lettuce, 1 lightswitch, 2 mugs, 1 pan, 1 papertowelroll, 2 peppershakers, 2 plates, 3 potatoes, 1 pot, 1 saltshaker, 1 sink, 3 soapbottles, 3 spatulas, 2 spoons, 2 statues, 4 stoveknobs, 3 tomatoes, 2 vases, 2 windows, 12 receptacle types: 1 plate, 6 cabinets, 1 coffeemachine, 3 countertops, 3 drawers, 1 fridge, 1 garbagecan, 1 microwave, 3 shelves, 1 sinkbasin, 4 stoveburners, 1 toaster, and 32 locations all numbered consecutively.  The receptacles are at locations as follows. stoveburner2 and stoveburner4 are at location22. drawer3 is at location28. countertop3 is at location4. cabinet4 is at location15. stoveburner1 and stoveburner3 are at location6. fridge1 is at location11. shelf3 is at location27. countertop1 is at location3. cabinet5 is at location14. cabinet1 is at location20. coffeemachine1 and countertop2 are at location12. microwave1 is at location25. drawer1 is at location21. shelf2 is at location30. sinkbasin1 is at location7. drawer2 is at location16. shelf1 is at location18. garbagecan1 is at location9. toaster1 is at location10. cabinet2 is at location23. cabinet3 is at location17. cabinet6 is at location5.  Currently, the objects are at locations as follows. peppershaker2, cellphone3, butterknife1, butterknife2, fork2, spatula3, knife1, soapbottle3, spoon2, houseplant1, and statue2 are at location4. papertowelroll1, soapbottle2, and glassbottle1 are at location9. apple3, cellphone1, spatula2, statue1, creditcard1, apple2, bread1, apple1, and dishsponge3 are at location3. stoveknob2 and stoveknob3 are at location13. chair1 is at location24. cellphone2 is at location21. plate2 and plate1 are at location14. spoon1 and dishsponge1 are at location16. vase2 is at location18. stoveknob4 is at location8. window2 is at location29. tomato1, cup1, lettuce1, potato1, potato2, egg1, tomato2, and cup2 are at location11. pot1 is at location22. mug2, bowl1, creditcard2, and glassbottle2 are at location30. vase1 and soapbottle1 are at location5. tomato3, egg2, sink1, fork1, potato3, and cup3 are at location7. window1 is at location31. pan1 is at location6. mug1 is at location25. lightswitch1 is at location26. creditcard3 is at location27. dishsponge2 is at location2. peppershaker1 is at location20. stoveknob1 is at location19. chair2 is at location1. spatula1 is at location28. agent agent1 is at location location27. The objects are in/on receptacle as follows. creditcard2, glassbottle2, mug2, and bowl1 are on shelf2. houseplant1, spoon2, spatula3, cellphone3, butterknife2, knife1, fork2, soapbottle3, statue2, peppershaker2, and butterknife1 are on countertop3. creditcard3 is on shelf3. spoon1 and dishsponge1 are in drawer2. apple2, creditcard1, bread1, statue1, dishsponge3, apple3, apple1, spatula2, and cellphone1 are on countertop1. egg1, potato1, cup2, tomato1, lettuce1, cup1, tomato2, and potato2 are in fridge1. cellphone2 is in drawer1. pot1 is on stoveburner4. plate1, plate2, and dishsponge2 are in cabinet5. fork1, tomato3, potato3, egg2, and cup3 are in sinkbasin1. mug1 is in microwave1. dishsponge2 is on plate1. papertowelroll1, glassbottle1, and soapbottle2 are in garbagecan1. peppershaker1 is in cabinet1. pot1 is on stoveburner2. spatula1 is in drawer3. pan1 is on stoveburner1. vase1 and soapbottle1 are in cabinet6. pan1 is on stoveburner3. vase2 is on shelf1. cabinet5, cabinet2, drawer3, drawer1, cabinet1, microwave1, drawer2, and fridge1 are closed. Nothing has been validated. agent1 is holding object saltshaker1. ", "question": "Which of the following options can hold in a state that can potentially be reached? A. It has been validated that an object of type newspapertype is examined under an object of type bathtubtype. B. agent agent1 is at location location21. C. It has been validated that an object of type bowltype is clean and is in a receptacle of type fridgetype and It has been validated that two objects of type cuptype are in a receptacle of type cabinettype. D. It has been validated that two objects of type lettucetype are in a receptacle of type microwavetype and knife1 is at location4.", "choices": {"label": ["A", "B", "C", "D"], "text": ["It has been validated that an object of type newspapertype is examined under an object of type bathtubtype", "agent agent1 is at location location21", "It has been validated that an object of type bowltype is clean and is in a receptacle of type fridgetype and It has been validated that two objects of type cuptype are in a receptacle of type cabinettype", "It has been validated that two objects of type lettucetype are in a receptacle of type microwavetype and knife1 is at location4"]}, "query": "Which fact is reachable from this state?", "answer": "B"}
