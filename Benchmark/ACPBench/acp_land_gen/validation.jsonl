{"id": 5196274110229243987, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l1, with the car c6 on board. The cars are at locations as follows: c9, c4, and c2 are at l1; c1, c7, c3, c0, c5, and c8 are at l0. The goal is to reach a state where the following facts hold: Car c9 is at location l1, Car c4 is at location l0, Car c6 is at location l0, Car c1 is at location l0, Car c7 is at location l0, Car c2 is at location l1, Car c3 is at location l1, Car c0 is at location l0, Car c5 is at location l0, and Car c8 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on board the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c2 l0)", "(at c5 l1)", "(at c1 l1)", "(at c8 l1)", "(at c6 l1)", "(on c1)", "(at c0 l1)", "(on c5)", "(on c9)", "(on c2)", "(on c7)", "(at c7 l1)", "(on c0)", "(on c8)", "(at c9 l0)"], "yes": ["(on c3)", "(on c4)", "(at-ferry l0)", "(empty-ferry)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l1) (at c5 l0) (at c7 l0) (at c8 l0) (at c9 l1) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c6))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": 4772210962720336132, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 5 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c4, c3, and c0 are at l0; c1 and c2 are at l1. The goal is to reach a state where the following facts hold: Car c4 is at location l0, Car c1 is at location l0, Car c0 is at location l1, Car c2 is at location l1, and Car c3 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - There are no cars on the ferry, and (on ?c) - Ferry has car ?c on board.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c2 l0)", "(on c4)", "(at c4 l1)", "(at c3 l1)", "(on c2)", "(on c3)"], "yes": ["(on c0)", "(at-ferry l1)", "(on c1)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c5)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l1) (at c2 l1) (at c3 l0) (at c4 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l0)))\n)"}
{"id": 4040494672687253167, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l0 location and it is empty. The cars are at locations as follows: c6, c1, c3, c0, c5, c8, and c9 are at l0; c4, c2, and c7 are at l1. The goal is to reach a state where the following facts hold: Car c9 is at location l1, Car c4 is at location l0, Car c6 is at location l0, Car c1 is at location l0, Car c7 is at location l0, Car c2 is at location l1, Car c3 is at location l1, Car c0 is at location l0, Car c5 is at location l0, and Car c8 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c2 l0)", "(at c5 l1)", "(at c1 l1)", "(at c8 l1)", "(at c6 l1)", "(on c1)", "(at c0 l1)", "(on c5)", "(on c2)", "(on c0)", "(on c8)", "(on c6)"], "yes": ["(on c3)", "(at-ferry l1)", "(on c4)", "(on c7)", "(on c9)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l1) (at c5 l0) (at c6 l0) (at c7 l1) (at c8 l0) (at c9 l0) (at-ferry l0) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": 8140728862242584052, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l1 location and it is empty. The cars are at locations as follows: c6, c1, c7, c5, c8, and c9 are at l0; c4, c0, c2, and c3 are at l1. The goal is to reach a state where the following facts hold: Car c9 is at location l1, Car c4 is at location l0, Car c6 is at location l0, Car c1 is at location l0, Car c7 is at location l0, Car c2 is at location l1, Car c3 is at location l1, Car c0 is at location l0, Car c5 is at location l0, and Car c8 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on board the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c2 l0)", "(at c5 l1)", "(at c1 l1)", "(at c8 l1)", "(at c6 l1)", "(on c1)", "(on c5)", "(at c3 l0)", "(on c2)", "(on c7)", "(at c7 l1)", "(on c8)", "(on c6)", "(on c3)"], "yes": ["(on c0)", "(at-ferry l0)", "(on c4)", "(on c9)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l1) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l1) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l0) (at-ferry l1) (empty-ferry) (not-eq l0 l1) (not-eq l1 l0))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": -7265803470829972654, "group": "landmarks_gen", "context": "This is a ferry domain, where the task is to transport cars from their start to their goal locations, using a ferry. Each location is accessible by ferry from each other location. The cars can be debarked or boarded, and the ferry can carry only one car at a time. \nThere are 2 locations and 10 cars, numbered consecutively. \nCurrently, the ferry is at l1, with the car c5 on board. The cars are at locations as follows: c9, c4, and c2 are at l1; c6, c1, c7, c3, c0, and c8 are at l0. The goal is to reach a state where the following facts hold: Car c9 is at location l1, Car c4 is at location l0, Car c6 is at location l0, Car c1 is at location l0, Car c7 is at location l0, Car c2 is at location l1, Car c3 is at location l1, Car c0 is at location l0, Car c5 is at location l0, and Car c8 is at location l0. The available propositions are: (at-ferry ?l) - The ferry is at ?l location, (at ?c ?l) - Car ?c is at location ?l, (empty-ferry) - The ferry is empty, and (on ?c) - Car ?c is on the ferry.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at c2 l0)", "(at c5 l1)", "(at c1 l1)", "(at c8 l1)", "(at c6 l1)", "(on c1)", "(at c0 l1)", "(on c9)", "(on c2)", "(on c7)", "(at c7 l1)", "(on c0)", "(on c8)", "(on c6)", "(at c9 l0)"], "yes": ["(on c3)", "(on c4)", "(at-ferry l0)", "(empty-ferry)"]}, "PDDL_domain": "(define (domain ferry)\n    (:requirements :strips :typing)\n    (:types car location - object)\n    (:predicates (at ?c - car ?l - location)  (at-ferry ?l - location)  (empty-ferry) (not-eq ?x ?y)  (on ?c - car))\n    (:action board\n        :parameters (?car - car ?loc - location)\n        :precondition (and (at ?car ?loc) (at-ferry ?loc) (empty-ferry))\n        :effect (and (on ?car) (not (at ?car ?loc)) (not (empty-ferry)))\n    )\n     (:action debark\n        :parameters (?car - car ?loc - location)\n        :precondition (and (on ?car) (at-ferry ?loc))\n        :effect (and (at ?car ?loc) (empty-ferry) (not (on ?car)))\n    )\n     (:action sail\n        :parameters (?from - location ?to - location)\n        :precondition (and (not-eq ?from ?to) (at-ferry ?from))\n        :effect (and (at-ferry ?to) (not (at-ferry ?from)))\n    )\n)", "PDDL_problem": "(define (problem ferry-l2-c10)\n    (:domain ferry)\n    (:requirements :strips :typing)\n    (:objects c0 c1 c2 c3 c4 c5 c6 c7 c8 c9 - car l0 l1 - location)\n    (:init (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l0) (at c4 l1) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1) (at-ferry l1) (not-eq l0 l1) (not-eq l1 l0) (on c5))\n    (:goal (and (at c0 l0) (at c1 l0) (at c2 l1) (at c3 l1) (at c4 l0) (at c5 l0) (at c6 l0) (at c7 l0) (at c8 l0) (at c9 l1)))\n)"}
{"id": -6896839021435593768, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1. \nCurrently, a0 is at l1-0, t0 is at l0-0, p2 is at l0-1, t1 is at l1-2, p1 is at l1-1, p3 is in t0, p0 is in a0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p3 is at l1-2, p2 is at l1-0, and p0 is at l1-1. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(at p1 l0-0)", "(in p1 t0)", "(at p2 l1-2)", "(at p3 l0-2)", "(at p1 l1-2)", "(in p1 a0)", "(at p0 l0-1)", "(at p1 l1-0)", "(at p0 l0-0)", "(in p2 t1)", "(at p1 l0-2)", "(at p3 l0-1)", "(at p1 l0-1)", "(at p2 l1-1)", "(at p0 l1-2)", "(at p2 l0-2)", "(in p0 t0)", "(at t0 l0-2)", "(at p0 l0-2)", "(at p3 l1-1)"], "yes": ["(in p3 t1)", "(at p3 l0-0)", "(at p3 l1-0)", "(in p3 a0)", "(in p2 a0)", "(at p2 l0-0)", "(in p2 t0)", "(in p0 t1)", "(at t1 l1-1)", "(at p0 l1-0)", "(at t1 l1-0)", "(at a0 l0-0)", "(at t0 l0-1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p1 l1-1) (at p2 l0-1) (at t0 l0-0) (at t1 l1-2) (in p0 a0) (in p3 t0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2)))\n)"}
{"id": 1550100744096115788, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1. \nCurrently, a0 and t0 are at l0-0, t1 is at l1-0, p1 is at l1-1, p3 and p0 are in a0, p2 is in t0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p3 is at l1-2, p2 is at l1-0, and p0 is at l1-1. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(at p1 l0-0)", "(in p1 t0)", "(at p2 l1-2)", "(at p3 l0-2)", "(at t0 l0-1)", "(at p1 l1-2)", "(in p1 a0)", "(at p3 l0-0)", "(at p0 l0-1)", "(at p1 l1-0)", "(at p0 l0-0)", "(in p2 t1)", "(at p1 l0-2)", "(at p3 l0-1)", "(at p2 l0-1)", "(at p1 l0-1)", "(at p2 l1-1)", "(at p0 l1-2)", "(at p2 l0-2)", "(in p3 t0)", "(in p0 t0)", "(at t0 l0-2)", "(at p0 l0-2)", "(at p3 l1-1)"], "yes": ["(in p3 t1)", "(at t1 l1-2)", "(at p3 l1-0)", "(in p2 a0)", "(at a0 l1-0)", "(at p2 l0-0)", "(in p0 t1)", "(at t1 l1-1)", "(at p0 l1-0)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p1 l1-1) (at t0 l0-0) (at t1 l1-0) (in p0 a0) (in p2 t0) (in p3 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2)))\n)"}
{"id": 7447525980482188540, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 3 trucks and 1 airplane, as well as 4 packages. There are 9 locations across 3 cities. \nThe locations are in cities as follows: l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1; l2-1, l2-0, and l2-2 are in c2. \nCurrently, a0, p1, and t2 are at l2-0, t0 is at l0-0, t1 and p3 are at l1-0, p0 is in t2, p2 is in a0. The goal is to reach a state where the following facts hold: p2 is at l2-2, p1 is at l2-0, p0 is at l2-1, and p3 is at l1-2. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(at p0 l2-2)", "(at p3 l2-1)", "(at p1 l0-0)", "(in p1 t0)", "(at p2 l1-2)", "(at p3 l0-2)", "(at t0 l0-1)", "(at p1 l1-2)", "(in p2 t0)", "(in p1 a0)", "(at p3 l2-0)", "(at p0 l1-1)", "(at t1 l1-1)", "(at p1 l2-1)", "(at p0 l1-0)", "(at p3 l0-0)", "(in p0 t1)", "(at p0 l0-1)", "(at p1 l2-2)", "(at p1 l1-0)", "(in p2 t1)", "(at p0 l0-0)", "(at p3 l0-1)", "(at p1 l0-2)", "(in p3 t2)", "(in p3 a0)", "(at a0 l0-0)", "(at p2 l0-1)", "(at p1 l0-1)", "(at p1 l1-1)", "(at p0 l1-2)", "(at p2 l1-1)", "(at a0 l1-0)", "(at p2 l0-2)", "(at p0 l2-0)", "(at p2 l1-0)", "(in p0 t0)", "(in p3 t0)", "(in p1 t2)", "(at p2 l2-1)", "(at t0 l0-2)", "(at p0 l0-2)", "(at p3 l1-1)", "(in p0 a0)", "(at p2 l0-0)", "(at p3 l2-2)"], "yes": ["(in p3 t1)", "(at t1 l1-2)", "(in p2 t2)", "(at t2 l2-2)", "(at p2 l2-0)", "(at t2 l2-1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c3-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 l2-0 - airport c0 c1 c2 - city l0-1 l0-2 l1-1 l1-2 l2-1 l2-2 - location p0 p1 p2 p3 - package t0 t1 t2 - truck)\n    (:init (at a0 l2-0) (at p1 l2-0) (at p3 l1-0) (at t0 l0-0) (at t1 l1-0) (at t2 l2-0) (in p0 t2) (in p2 a0) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1) (in-city l2-0 c2) (in-city l2-1 c2) (in-city l2-2 c2))\n    (:goal (and (at p0 l2-1) (at p1 l2-0) (at p2 l2-2) (at p3 l1-2)))\n)"}
{"id": -3000776064842292921, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1. \nCurrently, a0 is at l1-0, p3 and t0 are at l0-2, p2 is at l0-1, t1 is at l1-2, p1 is at l1-1, p0 is at l0-0. The goal is to reach a state where the following facts hold: p1 is at l1-1, p3 is at l1-2, p2 is at l1-0, and p0 is at l1-1. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(in p1 t1)", "(at p2 l0-2)", "(at p1 l1-0)", "(in p0 t0)", "(at p1 l0-0)", "(in p1 t0)", "(at p2 l1-2)", "(at p2 l1-1)", "(at p1 l1-2)", "(at p1 l0-2)", "(at p0 l0-2)", "(at p0 l0-1)", "(at p1 l0-1)", "(at p3 l1-1)", "(in p1 a0)", "(in p2 t1)", "(at p0 l1-2)", "(at p3 l0-1)"], "yes": ["(in p3 t1)", "(at p3 l0-0)", "(at p3 l1-0)", "(in p3 a0)", "(in p3 t0)", "(in p2 a0)", "(at p2 l0-0)", "(in p2 t0)", "(in p0 t1)", "(at t1 l1-1)", "(at p0 l1-0)", "(in p0 a0)", "(at t1 l1-0)", "(at t0 l0-0)", "(at a0 l0-0)", "(at t0 l0-1)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l1-0) (at p0 l0-0) (at p1 l1-1) (at p2 l0-1) (at p3 l0-2) (at t0 l0-2) (at t1 l1-2) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l1-1) (at p1 l1-1) (at p2 l1-0) (at p3 l1-2)))\n)"}
{"id": 4264614740131260175, "group": "landmarks_gen", "context": "There are several cities, each containing several locations, some of which are airports. There are also trucks, which can drive within a single city, and airplanes, which can fly between airports. The goal is to get some packages from various locations to various new locations. \nThere are 2 trucks and 1 airplane, as well as 4 packages. There are 6 locations across 2 cities. \nThe locations are in cities as follows: l0-2, l0-0, and l0-1 are in c0; l1-0, l1-1, and l1-2 are in c1. \nCurrently, a0 is at l0-0, t0 and p3 are at l0-1, p0 is at l0-2, t1 is at l1-0, p1 and p2 are in t1. The goal is to reach a state where the following facts hold: p1 is at l1-2, p0 is at l0-2, p2 is at l1-2, and p3 is at l0-1. The available propositions are: (at ?obj ?loc) - ?obj is at ?loc and (in ?obj1 ?obj2) - ?obj1 is in ?obj2.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at p1 l0-0)", "(in p1 t0)", "(at p3 l0-2)", "(in p2 t0)", "(in p1 a0)", "(at p0 l1-1)", "(at t1 l1-1)", "(at p0 l1-0)", "(at t0 l0-0)", "(at p3 l0-0)", "(in p0 t1)", "(at p0 l0-1)", "(in p3 t1)", "(at p1 l1-0)", "(at p0 l0-0)", "(at p1 l0-2)", "(at p3 l1-2)", "(in p3 a0)", "(at p2 l0-1)", "(at p1 l0-1)", "(at p1 l1-1)", "(at p0 l1-2)", "(at p2 l1-1)", "(at p3 l1-0)", "(at a0 l1-0)", "(at p2 l0-2)", "(at p2 l1-0)", "(in p0 t0)", "(in p3 t0)", "(at t0 l0-2)", "(at p3 l1-1)", "(in p0 a0)", "(in p2 a0)", "(at p2 l0-0)"], "yes": ["(at t1 l1-2)"]}, "PDDL_domain": "(define (domain logistics-strips)\n  (:requirements :strips :typing) \n\n  (:types \n    location locatable city - object \n    package movable - locatable\n    airport - location\n    airplane truck - movable    \n  )\t\t\n  \n  (:predicates \t\n\t\t(at ?obj - locatable ?loc - location)\n\t\t(in ?obj1 - package ?obj2 - movable)\n\t\t(in-city ?obj - location ?city - city))\n\n\n(:action LOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n   (at ?truck ?loc) (at ?obj ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?truck)))\n\n(:action LOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n   (at ?obj ?loc) (at ?airplane ?loc))\n  :effect\n   (and (not (at ?obj ?loc)) (in ?obj ?airplane)))\n\n\n\n(:action UNLOAD-TRUCK\n  :parameters\n   (?obj - package\n    ?truck - truck\n    ?loc - location)\n  :precondition\n   (and \n        (at ?truck ?loc) (in ?obj ?truck))\n  :effect\n   (and (not (in ?obj ?truck)) (at ?obj ?loc)))\n\n(:action UNLOAD-AIRPLANE\n  :parameters\n   (?obj - package\n    ?airplane - airplane\n    ?loc - location)\n  :precondition\n   (and \n        (in ?obj ?airplane) (at ?airplane ?loc))\n  :effect\n   (and (not (in ?obj ?airplane)) (at ?obj ?loc)))\n\n(:action DRIVE-TRUCK\n  :parameters\n   (?truck - truck\n    ?loc-from - location\n    ?loc-to - location\n    ?city - city)\n  :precondition\n   (and \n   (at ?truck ?loc-from)\n   (in-city ?loc-from ?city)\n   (in-city ?loc-to ?city))\n  :effect\n   (and (not (at ?truck ?loc-from)) (at ?truck ?loc-to)))\n\n(:action FLY-AIRPLANE\n  :parameters\n   (?airplane - airplane\n    ?loc-from - airport\n    ?loc-to - airport)\n  :precondition\n   (and \n\t(at ?airplane ?loc-from))\n  :effect\n   (and (not (at ?airplane ?loc-from)) (at ?airplane ?loc-to)))\n)", "PDDL_problem": "(define (problem logistics-c2-s3-p4-a1)\n    (:domain logistics-strips)\n    (:requirements :strips :typing)\n    (:objects a0 - airplane l0-0 l1-0 - airport c0 c1 - city l0-1 l0-2 l1-1 l1-2 - location p0 p1 p2 p3 - package t0 t1 - truck)\n    (:init (at a0 l0-0) (at p0 l0-2) (at p3 l0-1) (at t0 l0-1) (at t1 l1-0) (in p1 t1) (in p2 t1) (in-city l0-0 c0) (in-city l0-1 c0) (in-city l0-2 c0) (in-city l1-0 c1) (in-city l1-1 c1) (in-city l1-2 c1))\n    (:goal (and (at p0 l0-2) (at p1 l1-2) (at p2 l1-2) (at p3 l0-1)))\n)"}
{"id": 5792478122982408763, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_1 and block_5. The following block(s) are stacked on top of another block: block_2 is on block_4, block_3 is on block_1, and block_4 is on block_5. The goal is to reach a state where the following facts hold: The block block_4 is currently situated under the block block_2, The block block_3 is currently situated under the block block_1, and The block block_4 is on top of block block_5. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?y is currently situated under the block ?x.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_3 block_2)", "(clear block_5)", "(on block_2 block_2)", "(on block_5 block_4)", "(clear block_4)", "(on block_1 block_5)", "(on block_5 block_2)", "(on block_3 block_3)", "(on block_2 block_5)", "(on block_5 block_3)", "(ontable block_2)", "(on block_4 block_2)", "(on block_3 block_4)", "(on block_2 block_3)", "(on block_2 block_1)", "(holding block_2)", "(on block_4 block_3)", "(on block_3 block_5)", "(on block_5 block_5)", "(holding block_3)", "(on block_4 block_1)", "(on block_1 block_1)", "(on block_1 block_4)", "(ontable block_4)", "(holding block_4)", "(on block_5 block_1)", "(on block_1 block_2)", "(holding block_5)", "(on block_4 block_4)", "(ontable block_3)"], "yes": ["(holding block_1)", "(clear block_1)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_3) (handempty) (on block_2 block_4) (on block_3 block_1) (on block_4 block_5) (ontable block_1) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -7241724153464317212, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_2. The following block(s) is on the table: block_5. The following block(s) are stacked on top of another block: block_1 is on block_4, block_4 is on block_5, and block_3 is on block_1. The goal is to reach a state where the following facts hold: The block block_4 is currently situated under the block block_2, The block block_1 is on top of block block_3, and The block block_5 is currently situated under the block block_4. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is empty, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_3 block_2)", "(clear block_5)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_1 block_5)", "(on block_5 block_2)", "(on block_3 block_3)", "(on block_2 block_5)", "(on block_5 block_3)", "(ontable block_2)", "(on block_4 block_2)", "(on block_3 block_4)", "(ontable block_1)", "(on block_2 block_3)", "(on block_2 block_1)", "(on block_4 block_3)", "(on block_3 block_5)", "(on block_5 block_5)", "(holding block_3)", "(on block_4 block_1)", "(on block_1 block_1)", "(ontable block_4)", "(clear block_2)", "(holding block_4)", "(on block_5 block_1)", "(on block_1 block_2)", "(holding block_5)", "(on block_4 block_4)", "(ontable block_3)"], "yes": ["(holding block_1)", "(clear block_4)", "(handempty)", "(clear block_1)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_3) (holding block_2) (on block_1 block_4) (on block_3 block_1) (on block_4 block_5) (ontable block_5))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 4350095935636809747, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 3 blocks. \nCurrently, the robotic arm is holding block_2. The following block(s) is on the table: block_1. The following block(s) is stacked on top of another block: block_3 is on block_1. The goal is to reach a state where the following facts hold: The block block_2 is currently situated under the block block_1 and The block block_3 is currently situated above the block block_1. The available propositions are: (clear ?x) - Block ?x is clear, (ontable ?x) - Block ?x is on the table, (handempty) - The robotic arm is not holding anything, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_1 block_1)", "(on block_2 block_3)", "(on block_2 block_1)", "(on block_3 block_3)", "(on block_3 block_2)", "(on block_1 block_3)", "(on block_2 block_2)", "(ontable block_2)", "(holding block_3)", "(ontable block_3)"], "yes": ["(holding block_1)", "(clear block_2)", "(handempty)", "(clear block_1)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-3)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3)\n    (:init (clear block_3) (holding block_2) (on block_3 block_1) (ontable block_1))\n    (:goal (and (on block_1 block_2) (on block_3 block_1)))\n)"}
{"id": -65212949148186136, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is empty. The following block(s) are on the table: block_1, block_4, and block_3. The following block(s) are stacked on top of another block: block_2 is on block_3 and block_5 is on block_1. The goal is to reach a state where the following facts hold: The block block_4 is currently situated under the block block_2, The block block_1 is currently situated above the block block_3, and The block block_5 is currently situated under the block block_4. The available propositions are: (clear ?x) - Block ?x is clear, (ontable ?x) - Block ?x is placed on the table, (handempty) - The robotic arm is empty, (holding ?x) - The block ?x is currently being held by the robotic arm, and (on ?x ?y) - The block ?x is currently situated above the block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_3 block_2)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_1 block_5)", "(on block_5 block_2)", "(on block_3 block_3)", "(on block_2 block_5)", "(on block_3 block_1)", "(on block_5 block_3)", "(ontable block_2)", "(on block_4 block_2)", "(on block_3 block_4)", "(on block_2 block_1)", "(on block_4 block_3)", "(on block_3 block_5)", "(on block_5 block_5)", "(holding block_3)", "(ontable block_5)", "(on block_4 block_1)", "(on block_1 block_1)", "(on block_1 block_4)", "(on block_1 block_2)", "(holding block_5)", "(on block_4 block_4)"], "yes": ["(holding block_1)", "(clear block_3)", "(holding block_2)", "(holding block_4)", "(clear block_1)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_2) (clear block_4) (clear block_5) (handempty) (on block_2 block_3) (on block_5 block_1) (ontable block_1) (ontable block_3) (ontable block_4))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": 8459605840310505775, "group": "landmarks_gen", "context": "This is a blocksworld domain where blocks can be placed on top of each other or on the table. There is one robotic arm that can move the block. \nThere are 5 blocks. \nCurrently, the robotic arm is holding block_1. The following block(s) is on the table: block_2. The following block(s) are stacked on top of another block: block_5 is on block_2, block_4 is on block_3, and block_3 is on block_5. The goal is to reach a state where the following facts hold: The block block_2 is on top of block block_4, The block block_3 is currently situated under the block block_1, and The block block_4 is currently situated above the block block_5. The available propositions are: (clear ?x) - No blocks are placed on top of ?x, (ontable ?x) - Block ?x is located on the table, (handempty) - The robotic arm is empty, (holding ?x) - The robotic arm is holding ?x, and (on ?x ?y) - The block ?x is on top of block ?y.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(on block_3 block_2)", "(on block_2 block_2)", "(on block_5 block_4)", "(on block_1 block_5)", "(on block_3 block_3)", "(on block_2 block_5)", "(on block_3 block_1)", "(on block_5 block_3)", "(on block_4 block_2)", "(on block_3 block_4)", "(on block_2 block_3)", "(ontable block_1)", "(on block_2 block_1)", "(on block_5 block_5)", "(holding block_3)", "(ontable block_5)", "(on block_4 block_1)", "(on block_1 block_1)", "(on block_1 block_4)", "(ontable block_4)", "(on block_5 block_1)", "(on block_1 block_2)", "(holding block_5)", "(on block_4 block_4)", "(ontable block_3)", "(clear block_1)"], "yes": ["(clear block_3)", "(holding block_2)", "(holding block_4)", "(clear block_5)", "(handempty)", "(clear block_2)"]}, "PDDL_domain": "(define (domain blocksworld-4ops)\n    (:requirements :strips)\n    (:predicates (clear ?x)  (handempty) (holding ?x)  (on ?x ?y)  (ontable ?x))\n    (:action pick-up\n        :parameters (?ob)\n        :precondition (and (clear ?ob) (ontable ?ob) (handempty))\n        :effect (and (holding ?ob) (not (clear ?ob)) (not (ontable ?ob)) (not (handempty)))\n    )\n     (:action put-down\n        :parameters (?ob)\n        :precondition (holding ?ob)\n        :effect (and (clear ?ob) (handempty) (ontable ?ob) (not (holding ?ob)))\n    )\n     (:action stack\n        :parameters (?ob ?underob)\n        :precondition (and (clear ?underob) (holding ?ob))\n        :effect (and (handempty) (clear ?ob) (on ?ob ?underob) (not (clear ?underob)) (not (holding ?ob)))\n    )\n     (:action unstack\n        :parameters (?ob ?underob)\n        :precondition (and (on ?ob ?underob) (clear ?ob) (handempty))\n        :effect (and (holding ?ob) (clear ?underob) (not (on ?ob ?underob)) (not (clear ?ob)) (not (handempty)))\n    )\n)", "PDDL_problem": "(define (problem bw-rand-5)\n    (:domain blocksworld-4ops)\n    (:requirements :strips)\n    (:objects block_1 block_2 block_3 block_4 block_5)\n    (:init (clear block_4) (holding block_1) (on block_3 block_5) (on block_4 block_3) (on block_5 block_2) (ontable block_2))\n    (:goal (and (on block_1 block_3) (on block_2 block_4) (on block_4 block_5)))\n)"}
{"id": -5718162202945933497, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 3 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0, Key key0-2 is of shape shape0.  \nCurrently, the robot is at position f1-2f and its arm is empty. All the positions are open except the following: f0-1f has shape0 shaped lock, f2-1f has shape0 shaped lock, f2-2f has shape0 shaped lock. Key key0-2 is at position f1-2f. Key key0-1 is at position f3-3f. Key key0-0 is at position f4-1f. The goal is to reach a state where the following facts hold: Key key0-2 is at f1-1f location, Key key0-0 is at f1-0f location, and Key key0-1 is at f2-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot is not holding anything.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(open f2-1f)", "(at key0-2 f2-0f)", "(at key0-1 f1-0f)", "(at key0-0 f3-2f)", "(at-robot f3-0f)", "(at-robot f0-0f)", "(at key0-2 f1-4f)", "(at key0-1 f0-0f)", "(at key0-2 f0-3f)", "(at key0-2 f4-1f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at key0-1 f3-1f)", "(at-robot f3-4f)", "(at-robot f4-3f)", "(at-robot f4-0f)", "(at key0-1 f0-1f)", "(at-robot f2-2f)", "(open f0-1f)", "(at key0-0 f0-0f)", "(at key0-2 f1-3f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-2 f4-0f)", "(at key0-0 f0-2f)", "(at key0-1 f2-0f)", "(at key0-1 f2-1f)", "(at key0-2 f1-0f)", "(at-robot f3-2f)", "(at key0-2 f2-4f)", "(at key0-1 f4-4f)", "(at key0-0 f2-3f)", "(at key0-1 f0-3f)", "(at-robot f1-3f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at key0-0 f1-3f)", "(at key0-0 f3-1f)", "(at key0-2 f0-1f)", "(at key0-2 f3-4f)", "(at key0-0 f2-1f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-2 f4-4f)", "(at key0-2 f4-3f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(open f2-2f)", "(at-robot f0-4f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-0 f1-4f)", "(at-robot f3-1f)", "(at key0-2 f3-3f)", "(at key0-1 f1-3f)", "(at-robot f4-4f)", "(at key0-2 f0-4f)", "(at key0-0 f4-2f)", "(at key0-1 f4-1f)", "(at-robot f1-4f)", "(at key0-0 f3-4f)", "(at key0-1 f1-1f)", "(at key0-0 f0-4f)", "(at key0-0 f4-4f)", "(at key0-2 f2-3f)", "(at key0-2 f2-2f)", "(at key0-2 f3-0f)", "(at key0-2 f3-1f)", "(at-robot f0-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(at key0-2 f4-2f)", "(at key0-2 f0-2f)", "(at key0-2 f0-0f)", "(at key0-1 f0-4f)", "(at key0-1 f0-2f)", "(at-robot f4-2f)", "(at key0-1 f4-3f)", "(at key0-2 f2-1f)", "(at key0-0 f4-3f)", "(at key0-2 f3-2f)", "(at key0-1 f2-2f)", "(at key0-1 f4-0f)", "(at-robot f2-0f)", "(at key0-0 f1-2f)", "(at-robot f2-3f)", "(at-robot f0-3f)", "(at key0-0 f3-3f)", "(at key0-1 f3-0f)"], "yes": ["(holding key0-0)", "(at-robot f1-0f)", "(holding key0-1)", "(at-robot f2-4f)", "(holding key0-2)", "(at-robot f1-1f)", "(at-robot f4-1f)", "(at-robot f3-3f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k3-l3-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 key0-2 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f4-1f) (at key0-1 f3-3f) (at key0-2 f1-2f) (at-robot f1-2f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (key-shape key0-2 shape0) (lock-shape f0-1f shape0) (lock-shape f2-1f shape0) (lock-shape f2-2f shape0) (locked f0-1f) (locked f2-1f) (locked f2-2f) (open f0-0f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f1-0f) (at key0-1 f2-4f) (at key0-2 f1-1f)))\n)"}
{"id": -4162812061202576052, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  \nCurrently, the robot is at position f2-3f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f1-2f. Key key0-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot is not holding anything.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-0 f2-1f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-1 f1-0f)", "(at-robot f0-2f)", "(at key0-0 f3-2f)", "(at key0-1 f4-4f)", "(at-robot f2-4f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(open f0-0f)", "(holding key0-1)", "(at-robot f3-0f)", "(at-robot f3-3f)", "(at-robot f0-0f)", "(at key0-0 f2-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-1 f0-0f)", "(at key0-1 f0-4f)", "(at key0-0 f1-4f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at-robot f3-1f)", "(at key0-1 f3-1f)", "(at key0-1 f0-3f)", "(at-robot f3-4f)", "(at key0-1 f0-2f)", "(at key0-1 f1-3f)", "(at-robot f1-3f)", "(at-robot f4-3f)", "(at-robot f4-4f)", "(at-robot f4-2f)", "(at-robot f4-0f)", "(at key0-1 f4-3f)", "(at key0-0 f4-3f)", "(at-robot f1-1f)", "(at key0-1 f0-1f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at-robot f4-1f)", "(at-robot f2-2f)", "(at key0-1 f2-2f)", "(at key0-0 f4-2f)", "(at key0-0 f0-0f)", "(at key0-0 f1-3f)", "(at key0-1 f4-1f)", "(at key0-1 f4-0f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-0 f3-1f)", "(at-robot f2-0f)", "(at-robot f1-4f)", "(at key0-0 f0-2f)", "(at key0-1 f3-3f)", "(at key0-0 f3-4f)", "(at key0-0 f1-0f)", "(at key0-1 f1-1f)", "(at key0-1 f2-0f)", "(at-robot f0-3f)", "(at key0-0 f4-4f)", "(at key0-1 f2-1f)", "(at key0-0 f3-3f)", "(at key0-1 f2-4f)", "(at-robot f1-0f)", "(open f0-3f)", "(at key0-0 f4-1f)", "(at-robot f3-2f)"], "yes": ["(at-robot f0-4f)", "(holding key0-0)", "(at-robot f1-2f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f1-2f) (at key0-1 f3-0f) (at-robot f2-3f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-0f shape0) (lock-shape f0-3f shape0) (locked f0-0f) (locked f0-3f) (open f0-1f) (open f0-2f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f0-4f) (at key0-1 f3-0f)))\n)"}
{"id": -7273913249539910170, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  \nCurrently, the robot is at position f0-0f and its arm is empty. All the positions are open except the following: f0-3f has shape0 shaped lock, f4-0f has shape0 shaped lock. Key key0-1 is at position f0-0f. Key key0-0 is at position f4-4f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-2f location and Key key0-0 is at f2-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot's arm is empty.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-0 f2-1f)", "(at-robot f1-2f)", "(at key0-1 f1-0f)", "(at-robot f0-2f)", "(at key0-0 f3-2f)", "(at key0-1 f4-4f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(at-robot f3-0f)", "(at-robot f0-4f)", "(at-robot f3-3f)", "(at key0-0 f2-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-1 f0-4f)", "(at key0-0 f1-4f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at-robot f3-1f)", "(at key0-1 f3-1f)", "(at key0-1 f0-3f)", "(at key0-1 f0-2f)", "(at-robot f3-4f)", "(at key0-1 f1-3f)", "(at-robot f1-3f)", "(at-robot f4-3f)", "(at key0-1 f4-3f)", "(at-robot f4-0f)", "(at key0-0 f4-3f)", "(open f4-0f)", "(at-robot f1-1f)", "(at key0-1 f0-1f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at-robot f4-1f)", "(at key0-1 f3-0f)", "(at-robot f2-2f)", "(at key0-1 f2-2f)", "(at key0-0 f4-2f)", "(at key0-0 f0-0f)", "(at key0-0 f1-3f)", "(at key0-1 f4-1f)", "(at key0-1 f4-0f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-0 f3-1f)", "(at-robot f1-4f)", "(at-robot f2-0f)", "(at key0-0 f0-2f)", "(at key0-1 f3-3f)", "(at key0-0 f3-4f)", "(at key0-0 f1-2f)", "(at key0-0 f1-0f)", "(at key0-1 f1-1f)", "(at-robot f2-3f)", "(at key0-1 f2-0f)", "(at key0-0 f0-4f)", "(at-robot f0-3f)", "(at key0-1 f2-1f)", "(at key0-0 f3-3f)", "(at key0-1 f2-4f)", "(at-robot f1-0f)", "(open f0-3f)", "(at key0-0 f4-1f)", "(at-robot f3-2f)"], "yes": ["(at-robot f2-4f)", "(holding key0-0)", "(at-robot f4-2f)", "(holding key0-1)", "(at-robot f4-4f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f4-4f) (at key0-1 f0-0f) (at-robot f0-0f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-3f shape0) (lock-shape f4-0f shape0) (locked f0-3f) (locked f4-0f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f2-4f) (at key0-1 f4-2f)))\n)"}
{"id": 8452927514841302640, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  \nCurrently, the robot is at position f1-4f and its arm is empty. All the positions are open except the following: f0-0f has shape0 shaped lock, f0-3f has shape0 shaped lock. Key key0-0 is at position f1-2f. Key key0-1 is at position f3-0f. The goal is to reach a state where the following facts hold: Key key0-1 is at f3-0f location and Key key0-0 is at f0-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot's arm is empty.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-0 f2-1f)", "(at key0-1 f4-2f)", "(at key0-0 f2-4f)", "(at key0-1 f1-0f)", "(at-robot f0-2f)", "(at key0-0 f3-2f)", "(at key0-1 f4-4f)", "(at-robot f2-4f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(open f0-0f)", "(holding key0-1)", "(at-robot f3-0f)", "(at-robot f3-3f)", "(at-robot f0-0f)", "(at key0-0 f2-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-1 f0-0f)", "(at key0-1 f0-4f)", "(at key0-0 f1-4f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(at-robot f3-1f)", "(at key0-1 f3-1f)", "(at key0-1 f0-3f)", "(at-robot f3-4f)", "(at key0-1 f0-2f)", "(at key0-1 f1-3f)", "(at-robot f1-3f)", "(at-robot f4-3f)", "(at-robot f4-4f)", "(at-robot f4-2f)", "(at-robot f4-0f)", "(at key0-1 f4-3f)", "(at key0-0 f4-3f)", "(at-robot f1-1f)", "(at key0-1 f0-1f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at-robot f4-1f)", "(at-robot f2-2f)", "(at key0-1 f2-2f)", "(at key0-0 f4-2f)", "(at key0-0 f0-0f)", "(at key0-0 f1-3f)", "(at key0-1 f4-1f)", "(at key0-1 f4-0f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-0 f3-1f)", "(at-robot f2-0f)", "(at key0-0 f0-2f)", "(at key0-1 f3-3f)", "(at key0-0 f3-4f)", "(at key0-0 f1-0f)", "(at key0-1 f1-1f)", "(at-robot f2-3f)", "(at key0-1 f2-0f)", "(at-robot f0-3f)", "(at key0-0 f4-4f)", "(at key0-1 f2-1f)", "(at key0-0 f3-3f)", "(at key0-1 f2-4f)", "(at-robot f1-0f)", "(open f0-3f)", "(at key0-0 f4-1f)", "(at-robot f3-2f)"], "yes": ["(at-robot f0-4f)", "(holding key0-0)", "(at-robot f1-2f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (arm-empty) (at key0-0 f1-2f) (at key0-1 f3-0f) (at-robot f1-4f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-0f shape0) (lock-shape f0-3f shape0) (locked f0-0f) (locked f0-3f) (open f0-1f) (open f0-2f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-0f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f0-4f) (at key0-1 f3-0f)))\n)"}
{"id": -1206225347505945318, "group": "landmarks_gen", "context": "A robot is in a grid and can only move to places that are connected to its current position. \nThe grid size is 5x5, and the locations are of the form fi-jf (e.g., f3-2f or f0-1f). The grid cells are connected to their neighbors (e.g., f1-2f is connected to the four neighbors f0-2f, f2-2f, f1-1f, and f1-3f). Some positions on the grid are locked and can be opened with a key of a matching shape. The robot has an arm that can pick up a key when the key is in same location as the robot and the arm is empty.  \nThere are 2 keys in 0 different shapes: Key key0-0 is of shape shape0, Key key0-1 is of shape shape0.  \nCurrently, the robot is at position f3-2f and is holding key0-1. All the positions are open except the following: f4-0f has shape0 shaped lock. Key key0-0 is at position f4-4f. The goal is to reach a state where the following facts hold: Key key0-1 is at f4-2f location and Key key0-0 is at f2-4f location. The available propositions are: (at ?r ?x) - Key ?r is at ?x location, (at-robot ?x) - Robot is at ?x location, (locked ?x) - Location ?x is locked, (holding ?k) - Robot is holding ?k, (open ?x) - Location ?x is open, and (arm-empty) - Robot's arm is empty.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at key0-0 f2-1f)", "(at-robot f1-2f)", "(at key0-1 f1-0f)", "(at-robot f0-2f)", "(at key0-0 f3-2f)", "(at key0-1 f4-4f)", "(at-robot f0-1f)", "(at key0-1 f3-2f)", "(at key0-0 f4-0f)", "(at key0-0 f2-2f)", "(at-robot f3-0f)", "(at-robot f0-4f)", "(at-robot f0-0f)", "(at key0-0 f2-3f)", "(at-robot f3-3f)", "(at key0-0 f0-1f)", "(at key0-0 f3-0f)", "(at key0-0 f1-1f)", "(at key0-1 f2-3f)", "(at key0-1 f0-0f)", "(at key0-1 f0-4f)", "(at key0-0 f1-4f)", "(at key0-0 f2-0f)", "(at key0-1 f3-4f)", "(locked f0-3f)", "(at-robot f3-1f)", "(at key0-1 f3-1f)", "(at key0-1 f0-3f)", "(at key0-1 f0-2f)", "(at-robot f3-4f)", "(at key0-1 f1-3f)", "(at-robot f1-3f)", "(at-robot f4-3f)", "(at key0-1 f4-3f)", "(at-robot f4-0f)", "(at key0-0 f4-3f)", "(open f4-0f)", "(at-robot f1-1f)", "(at key0-1 f0-1f)", "(at key0-1 f1-2f)", "(at key0-1 f1-4f)", "(at-robot f4-1f)", "(at key0-1 f3-0f)", "(at-robot f2-2f)", "(at key0-1 f2-2f)", "(at key0-0 f4-2f)", "(at key0-0 f0-0f)", "(at key0-0 f1-3f)", "(at key0-1 f4-1f)", "(at key0-1 f4-0f)", "(at-robot f2-1f)", "(at key0-0 f0-3f)", "(at key0-0 f3-1f)", "(at-robot f1-4f)", "(at-robot f2-0f)", "(at key0-0 f0-2f)", "(at key0-1 f3-3f)", "(at key0-0 f3-4f)", "(at key0-0 f1-2f)", "(at key0-0 f1-0f)", "(at key0-1 f1-1f)", "(at-robot f2-3f)", "(at key0-1 f2-0f)", "(at key0-0 f0-4f)", "(at-robot f0-3f)", "(at key0-1 f2-1f)", "(at key0-0 f3-3f)", "(at key0-1 f2-4f)", "(at-robot f1-0f)", "(arm-empty)", "(at key0-0 f4-1f)"], "yes": ["(at-robot f2-4f)", "(holding key0-0)", "(at-robot f4-2f)", "(at-robot f4-4f)"]}, "PDDL_domain": "(define (domain grid)\n    (:requirements :strips :typing)\n    (:types key place shape - object)\n    (:predicates (arm-empty) (at ?r - key ?x - place)  (at-robot ?x - place)  (conn ?x - place ?y - place)  (holding ?k - key)  (key-shape ?k - key ?s - shape)  (lock-shape ?x - place ?s - shape)  (locked ?x - place)  (open ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (conn ?curpos ?nextpos) (open ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)))\n    )\n     (:action pickup\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (at ?key ?curpos) (arm-empty))\n        :effect (and (holding ?key) (not (at ?key ?curpos)) (not (arm-empty)))\n    )\n     (:action pickup-and-loose\n        :parameters (?curpos - place ?newkey - key ?oldkey - key)\n        :precondition (and (at-robot ?curpos) (holding ?oldkey) (at ?newkey ?curpos))\n        :effect (and (holding ?newkey) (at ?oldkey ?curpos) (not (holding ?oldkey)) (not (at ?newkey ?curpos)))\n    )\n     (:action putdown\n        :parameters (?curpos - place ?key - key)\n        :precondition (and (at-robot ?curpos) (holding ?key))\n        :effect (and (arm-empty) (at ?key ?curpos) (not (holding ?key)))\n    )\n     (:action unlock\n        :parameters (?curpos - place ?lockpos - place ?key - key ?shape - shape)\n        :precondition (and (conn ?curpos ?lockpos) (key-shape ?key ?shape) (lock-shape ?lockpos ?shape) (at-robot ?curpos) (locked ?lockpos) (holding ?key))\n        :effect (and (open ?lockpos) (not (locked ?lockpos)))\n    )\n)", "PDDL_problem": "(define (problem grid-x5-y5-t1-k2-l2-p100)\n    (:domain grid)\n    (:requirements :strips :typing)\n    (:objects key0-0 key0-1 - key f0-0f f0-1f f0-2f f0-3f f0-4f f1-0f f1-1f f1-2f f1-3f f1-4f f2-0f f2-1f f2-2f f2-3f f2-4f f3-0f f3-1f f3-2f f3-3f f3-4f f4-0f f4-1f f4-2f f4-3f f4-4f - place shape0 - shape)\n    (:init (at key0-0 f4-4f) (at-robot f3-2f) (conn f0-0f f0-1f) (conn f0-0f f1-0f) (conn f0-1f f0-0f) (conn f0-1f f0-2f) (conn f0-1f f1-1f) (conn f0-2f f0-1f) (conn f0-2f f0-3f) (conn f0-2f f1-2f) (conn f0-3f f0-2f) (conn f0-3f f0-4f) (conn f0-3f f1-3f) (conn f0-4f f0-3f) (conn f0-4f f1-4f) (conn f1-0f f0-0f) (conn f1-0f f1-1f) (conn f1-0f f2-0f) (conn f1-1f f0-1f) (conn f1-1f f1-0f) (conn f1-1f f1-2f) (conn f1-1f f2-1f) (conn f1-2f f0-2f) (conn f1-2f f1-1f) (conn f1-2f f1-3f) (conn f1-2f f2-2f) (conn f1-3f f0-3f) (conn f1-3f f1-2f) (conn f1-3f f1-4f) (conn f1-3f f2-3f) (conn f1-4f f0-4f) (conn f1-4f f1-3f) (conn f1-4f f2-4f) (conn f2-0f f1-0f) (conn f2-0f f2-1f) (conn f2-0f f3-0f) (conn f2-1f f1-1f) (conn f2-1f f2-0f) (conn f2-1f f2-2f) (conn f2-1f f3-1f) (conn f2-2f f1-2f) (conn f2-2f f2-1f) (conn f2-2f f2-3f) (conn f2-2f f3-2f) (conn f2-3f f1-3f) (conn f2-3f f2-2f) (conn f2-3f f2-4f) (conn f2-3f f3-3f) (conn f2-4f f1-4f) (conn f2-4f f2-3f) (conn f2-4f f3-4f) (conn f3-0f f2-0f) (conn f3-0f f3-1f) (conn f3-0f f4-0f) (conn f3-1f f2-1f) (conn f3-1f f3-0f) (conn f3-1f f3-2f) (conn f3-1f f4-1f) (conn f3-2f f2-2f) (conn f3-2f f3-1f) (conn f3-2f f3-3f) (conn f3-2f f4-2f) (conn f3-3f f2-3f) (conn f3-3f f3-2f) (conn f3-3f f3-4f) (conn f3-3f f4-3f) (conn f3-4f f2-4f) (conn f3-4f f3-3f) (conn f3-4f f4-4f) (conn f4-0f f3-0f) (conn f4-0f f4-1f) (conn f4-1f f3-1f) (conn f4-1f f4-0f) (conn f4-1f f4-2f) (conn f4-2f f3-2f) (conn f4-2f f4-1f) (conn f4-2f f4-3f) (conn f4-3f f3-3f) (conn f4-3f f4-2f) (conn f4-3f f4-4f) (conn f4-4f f3-4f) (conn f4-4f f4-3f) (holding key0-1) (key-shape key0-0 shape0) (key-shape key0-1 shape0) (lock-shape f0-3f shape0) (lock-shape f4-0f shape0) (locked f4-0f) (open f0-0f) (open f0-1f) (open f0-2f) (open f0-3f) (open f0-4f) (open f1-0f) (open f1-1f) (open f1-2f) (open f1-3f) (open f1-4f) (open f2-0f) (open f2-1f) (open f2-2f) (open f2-3f) (open f2-4f) (open f3-0f) (open f3-1f) (open f3-2f) (open f3-3f) (open f3-4f) (open f4-1f) (open f4-2f) (open f4-3f) (open f4-4f))\n    (:goal (and (at key0-0 f2-4f) (at key0-1 f4-2f)))\n)"}
{"id": 9160386177509913200, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 12 tiles. \nThe tiles locations are: tile_12 is to the right of tile_11, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_7 is down from tile_10, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_2 is down from tile_5, and tile_9 is down from tile_12 \nCurrently, robot robot1 is at tile_1 and holding color white and robot robot2 is at tile_8 and holding color black; tile_4, tile_9, tile_5, tile_7, tile_6, tile_3, and tile_2 are clear; tile_12 is painted white, tile_11 is painted black, and tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_12 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, and Tile tile_10 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_2)", "(painted tile_11 white)", "(robot-at robot2 tile_4)", "(robot-has robot2 white)", "(robot-at robot1 tile_2)", "(painted tile_1 white)", "(robot-has robot1 black)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(clear tile_1)", "(painted tile_3 black)", "(clear tile_12)", "(clear tile_11)", "(painted tile_12 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(painted tile_2 black)", "(robot-at robot1 tile_10)", "(painted tile_4 black)", "(painted tile_10 black)", "(robot-at robot1 tile_9)", "(painted tile_6 black)", "(robot-at robot2 tile_12)", "(painted tile_8 black)", "(painted tile_2 white)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_11)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_12)", "(robot-at robot1 tile_8)", "(robot-at robot2 tile_11)", "(robot-at robot2 tile_10)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_5)", "(robot-at robot1 tile_6)", "(painted tile_5 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)", "(clear tile_10)"], "yes": ["(clear tile_8)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_6) (clear tile_7) (clear tile_9) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_8) (robot-has robot1 white) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 8388948775127950653, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 9 tiles. \nThe tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_4 is down from tile_7, tile_5 is down from tile_8, and tile_2 is down from tile_5 \nCurrently, robot robot2 is at tile_2 and holding color white and robot robot1 is at tile_1 and holding color white; tile_4, tile_6, and tile_3 are clear; tile_7 is painted black, tile_8 is painted white, tile_9 is painted black, and tile_5 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, and Tile tile_6 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_4)", "(robot-has robot2 black)", "(robot-at robot1 tile_2)", "(clear tile_8)", "(clear tile_5)", "(clear tile_7)", "(painted tile_1 white)", "(robot-has robot1 black)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(clear tile_1)", "(painted tile_3 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(painted tile_2 black)", "(clear tile_9)", "(painted tile_4 black)", "(robot-at robot2 tile_8)", "(robot-at robot1 tile_9)", "(painted tile_6 black)", "(painted tile_8 black)", "(painted tile_2 white)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_8)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_5)", "(robot-at robot1 tile_6)", "(painted tile_5 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)"], "yes": ["(clear tile_2)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_3) (clear tile_4) (clear tile_6) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_5 black) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 white) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 5534083814025355815, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 9 tiles. \nThe tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_4 is down from tile_7, tile_5 is down from tile_8, and tile_2 is down from tile_5 \nCurrently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_4 and holding color white; tile_1, tile_5, tile_6, and tile_3 are clear; tile_7 is painted black, tile_8 is painted white, and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, and Tile tile_6 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_4)", "(robot-has robot2 white)", "(robot-at robot1 tile_2)", "(clear tile_8)", "(clear tile_7)", "(painted tile_1 white)", "(robot-has robot1 black)", "(robot-at robot2 tile_7)", "(robot-at robot2 tile_6)", "(painted tile_3 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(painted tile_2 black)", "(clear tile_9)", "(painted tile_4 black)", "(robot-at robot1 tile_9)", "(robot-at robot2 tile_8)", "(painted tile_6 black)", "(painted tile_2 white)", "(painted tile_8 black)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_8)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_5)", "(robot-at robot1 tile_6)", "(painted tile_5 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)"], "yes": ["(clear tile_4)", "(clear tile_2)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_5) (clear tile_6) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_4) (robot-at robot2 tile_2) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 8441436456817991537, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 12 tiles. \nThe tiles locations are: tile_12 is to the right of tile_11, tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, tile_11 is to the right of tile_10, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_7 is down from tile_10, tile_4 is down from tile_7, tile_8 is down from tile_11, tile_5 is down from tile_8, tile_2 is down from tile_5, and tile_9 is down from tile_12 \nCurrently, robot robot2 is at tile_2 and holding color black and robot robot1 is at tile_6 and holding color black; tile_4, tile_1, tile_8, tile_5, and tile_3 are clear; tile_7 is painted black, tile_12 is painted white, tile_11 is painted black, tile_9 is painted black, and tile_10 is painted white. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_12 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, Tile tile_6 is painted in white color, Tile tile_11 is painted in black color, and Tile tile_10 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(painted tile_11 white)", "(robot-at robot2 tile_4)", "(robot-has robot2 white)", "(robot-at robot1 tile_2)", "(clear tile_7)", "(painted tile_1 white)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(painted tile_3 black)", "(clear tile_12)", "(clear tile_11)", "(painted tile_12 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(robot-at robot1 tile_1)", "(painted tile_2 black)", "(robot-at robot1 tile_10)", "(clear tile_9)", "(painted tile_4 black)", "(painted tile_10 black)", "(robot-at robot2 tile_8)", "(robot-at robot1 tile_9)", "(painted tile_6 black)", "(robot-at robot2 tile_12)", "(painted tile_8 black)", "(painted tile_2 white)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_11)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_12)", "(robot-at robot1 tile_8)", "(robot-at robot2 tile_11)", "(robot-at robot2 tile_10)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot2 tile_5)", "(painted tile_5 white)", "(robot-has robot1 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)", "(clear tile_10)"], "yes": ["(clear tile_6)", "(clear tile_2)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-3-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_10 tile_11 tile_12 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_1) (clear tile_3) (clear tile_4) (clear tile_5) (clear tile_8) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (down tile_7 tile_10) (down tile_8 tile_11) (down tile_9 tile_12) (left tile_1 tile_2) (left tile_10 tile_11) (left tile_11 tile_12) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white) (painted tile_7 black) (painted tile_9 black) (right tile_11 tile_10) (right tile_12 tile_11) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_6) (robot-at robot2 tile_2) (robot-has robot1 black) (robot-has robot2 black) (up tile_10 tile_7) (up tile_11 tile_8) (up tile_12 tile_9) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black) (painted tile_10 white) (painted tile_11 black) (painted tile_12 white)))\n)"}
{"id": 1556332777485676351, "group": "landmarks_gen", "context": "A set of robots use different colors to paint patterns in floor tiles. The robots can move around the floor tiles in four directions (up, down, left and right). Robots paint with one color at a time, but can change their spray guns to any available color. However, robots can only paint the tile that is in front (up) and behind (down) them, and once a tile has been painted no robot can stand on it. Robots need to paint a grid with black and white, where the cell color is alternated always. \nThere are 2 robots and 9 tiles. \nThe tiles locations are: tile_9 is to the right of tile_8, tile_6 is to the right of tile_5, tile_8 is to the right of tile_7, tile_2 is to the right of tile_1, tile_5 is to the right of tile_4, and tile_3 is to the right of tile_2. Further, tile_3 is down from tile_6, tile_6 is down from tile_9, tile_1 is down from tile_4, tile_4 is down from tile_7, tile_5 is down from tile_8, and tile_2 is down from tile_5 \nCurrently, robot robot1 is at tile_1 and holding color white and robot robot2 is at tile_5 and holding color black; tile_4, tile_7, tile_6, tile_3, and tile_2 are clear; tile_8 is painted white and tile_9 is painted black. The goal is to reach a state where the following facts hold: Tile tile_7 is painted in black color, Tile tile_4 is painted in white color, Tile tile_8 is painted in white color, Tile tile_9 is painted in black color, Tile tile_5 is painted in black color, and Tile tile_6 is painted in white color. The available propositions are: (robot-at ?r ?x) - Robot ?r is at ?x location, (clear ?x) - ?x is clear, (painted ?x ?c) - Tile ?x is painted in ?c color, and (robot-has ?r ?c) - Robot ?r is holding ?c paint.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(robot-at robot2 tile_2)", "(robot-at robot2 tile_4)", "(robot-has robot2 white)", "(robot-at robot1 tile_2)", "(clear tile_8)", "(painted tile_1 white)", "(robot-has robot1 black)", "(robot-at robot2 tile_7)", "(robot-at robot1 tile_4)", "(robot-at robot2 tile_6)", "(clear tile_1)", "(painted tile_3 black)", "(robot-at robot2 tile_9)", "(painted tile_3 white)", "(painted tile_2 black)", "(clear tile_9)", "(painted tile_4 black)", "(robot-at robot1 tile_9)", "(robot-at robot2 tile_8)", "(painted tile_6 black)", "(painted tile_8 black)", "(painted tile_2 white)", "(robot-at robot1 tile_5)", "(robot-at robot1 tile_7)", "(robot-at robot1 tile_8)", "(robot-at robot1 tile_3)", "(robot-at robot2 tile_3)", "(robot-at robot1 tile_6)", "(painted tile_5 white)", "(painted tile_7 white)", "(painted tile_9 white)", "(robot-at robot2 tile_1)", "(painted tile_1 black)"], "yes": ["(clear tile_5)"]}, "PDDL_domain": "(define (domain floor-tile)\n    (:requirements :typing)\n    (:types color robot tile - object)\n    (:predicates (available-color ?c - color)  (clear ?x - tile)  (down ?x - tile ?y - tile)  (free-color ?r - robot)  (left ?x - tile ?y - tile)  (painted ?x - tile ?c - color)  (right ?x - tile ?y - tile)  (robot-at ?r - robot ?x - tile)  (robot-has ?r - robot ?c - color)  (up ?x - tile ?y - tile))\n    (:action change-color\n        :parameters (?r - robot ?c - color ?c2 - color)\n        :precondition (and (robot-has ?r ?c) (available-color ?c2))\n        :effect (and (not (robot-has ?r ?c)) (robot-has ?r ?c2))\n    )\n     (:action down\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action left\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (left ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action paint-down\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (down ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action paint-up\n        :parameters (?r - robot ?y - tile ?x - tile ?c - color)\n        :precondition (and (robot-has ?r ?c) (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (not (clear ?y)) (painted ?y ?c))\n    )\n     (:action right\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (right ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n     (:action up\n        :parameters (?r - robot ?x - tile ?y - tile)\n        :precondition (and (robot-at ?r ?x) (up ?y ?x) (clear ?y))\n        :effect (and (robot-at ?r ?y) (not (robot-at ?r ?x)) (clear ?x) (not (clear ?y)))\n    )\n)", "PDDL_problem": "(define (problem floortile-2-3-2)\n    (:domain floor-tile)\n    (:requirements :typing)\n    (:objects black white - color robot1 robot2 - robot tile_1 tile_2 tile_3 tile_4 tile_5 tile_6 tile_7 tile_8 tile_9 - tile)\n    (:init (available-color black) (available-color white) (clear tile_2) (clear tile_3) (clear tile_4) (clear tile_6) (clear tile_7) (down tile_1 tile_4) (down tile_2 tile_5) (down tile_3 tile_6) (down tile_4 tile_7) (down tile_5 tile_8) (down tile_6 tile_9) (left tile_1 tile_2) (left tile_2 tile_3) (left tile_4 tile_5) (left tile_5 tile_6) (left tile_7 tile_8) (left tile_8 tile_9) (painted tile_8 white) (painted tile_9 black) (right tile_2 tile_1) (right tile_3 tile_2) (right tile_5 tile_4) (right tile_6 tile_5) (right tile_8 tile_7) (right tile_9 tile_8) (robot-at robot1 tile_1) (robot-at robot2 tile_5) (robot-has robot1 white) (robot-has robot2 black) (up tile_4 tile_1) (up tile_5 tile_2) (up tile_6 tile_3) (up tile_7 tile_4) (up tile_8 tile_5) (up tile_9 tile_6))\n    (:goal (and (painted tile_4 white) (painted tile_5 black) (painted tile_6 white) (painted tile_7 black) (painted tile_8 white) (painted tile_9 black)))\n)"}
{"id": 4676800007335087243, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball2 is at room1, ball3 is at room2, ball1 is at room3. The goal is to reach a state where the following facts hold: Ball ball3 is in room room2, Ball ball1 is at room1 location, Ball ball4 is at room1 location, and Ball ball2 is in room room1. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(carry robot1 ball4 left1)", "(at ball4 room2)", "(at ball3 room1)", "(at ball2 room3)", "(carry robot1 ball2 left1)", "(carry robot1 ball1 right1)", "(at ball3 room3)", "(carry robot1 ball3 left1)", "(at ball1 room2)", "(carry robot1 ball2 right1)", "(at ball2 room2)", "(carry robot1 ball3 right1)", "(at ball4 room3)", "(carry robot1 ball1 left1)"], "yes": ["(at-robby robot1 room1)", "(at-robby robot1 room3)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room3) (at ball2 room1) (at ball3 room2) (at-robby robot1 room2) (carry robot1 ball4 right1) (free robot1 left1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1)))\n)"}
{"id": -8680031870502993317, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 7 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room5 and both grippers are free. Additionally, ball3 is at room5, ball2 is at room3, ball1 and ball4 are at room6. The goal is to reach a state where the following facts hold: Ball ball3 is at room3 location, Ball ball4 is in room room1, Ball ball1 is in room room1, and Ball ball2 is in room room2. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at ball2 room6)", "(at ball4 room4)", "(at ball3 room4)", "(carry robot1 ball3 left1)", "(at ball4 room5)", "(carry robot1 ball3 right1)", "(at ball4 room3)", "(at ball3 room6)", "(at ball4 room2)", "(at ball2 room5)", "(at ball2 room1)", "(at ball1 room2)", "(at-robby robot1 room7)", "(at ball2 room7)", "(carry robot1 ball2 right1)", "(at ball4 room7)", "(carry robot1 ball4 right1)", "(at ball1 room4)", "(carry robot1 ball1 left1)", "(at-robby robot1 room4)", "(carry robot1 ball4 left1)", "(at ball2 room4)", "(at ball3 room7)", "(at ball3 room1)", "(carry robot1 ball2 left1)", "(at ball1 room7)", "(at ball1 room5)", "(carry robot1 ball1 right1)", "(at ball3 room2)", "(at ball1 room3)"], "yes": ["(at-robby robot1 room1)", "(at-robby robot1 room2)", "(at-robby robot1 room3)", "(at-robby robot1 room6)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-7-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 room6 room7 - room)\n    (:init (at ball1 room6) (at ball2 room3) (at ball3 room5) (at ball4 room6) (at-robby robot1 room5) (free robot1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": 8561521649268952999, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 3 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2, left gripper is free, and right gripper is carrying the ball ball4. Additionally, ball2 is at room3, ball3 is at room2, ball1 is at room1. The goal is to reach a state where the following facts hold: Ball ball3 is in room room2, Ball ball1 is in room room1, Ball ball4 is in room room1, and Ball ball2 is at room1 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is at ?x location, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(carry robot1 ball4 left1)", "(at ball4 room2)", "(at ball3 room1)", "(carry robot1 ball2 left1)", "(carry robot1 ball1 right1)", "(at ball1 room3)", "(at ball3 room3)", "(carry robot1 ball3 left1)", "(at ball1 room2)", "(carry robot1 ball2 right1)", "(at ball2 room2)", "(carry robot1 ball3 right1)", "(at ball4 room3)", "(carry robot1 ball1 left1)"], "yes": ["(at-robby robot1 room1)", "(at-robby robot1 room3)", "(free robot1 right1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-3-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 - room)\n    (:init (at ball1 room1) (at ball2 room3) (at ball3 room2) (at-robby robot1 room2) (carry robot1 ball4 right1) (free robot1 left1))\n    (:goal (and (at ball1 room1) (at ball2 room1) (at ball3 room2) (at ball4 room1)))\n)"}
{"id": 6151988036911325259, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 5 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room5, right gripper is free, and left gripper is carrying the ball ball1. Additionally, ball4 is at room4, ball3 is at room3, ball2 is at room2. The goal is to reach a state where the following facts hold: Ball ball3 is at room3 location, Ball ball4 is at room1 location, Ball ball1 is at room1 location, and Ball ball2 is at room2 location. The available propositions are: (at-robby ?r ?x) - Robot ?r is in room ?x, (at ?o ?x) - Ball ?o is in room ?x, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at ball3 room4)", "(carry robot1 ball3 left1)", "(at ball4 room5)", "(at-robby robot1 room3)", "(carry robot1 ball3 right1)", "(at ball4 room3)", "(at-robby robot1 room2)", "(at ball4 room2)", "(at ball2 room5)", "(at ball2 room3)", "(at ball2 room1)", "(at ball1 room2)", "(carry robot1 ball2 right1)", "(at ball1 room4)", "(carry robot1 ball4 right1)", "(carry robot1 ball4 left1)", "(at ball2 room4)", "(at ball3 room1)", "(at ball3 room5)", "(carry robot1 ball2 left1)", "(at ball1 room5)", "(carry robot1 ball1 right1)", "(at ball3 room2)", "(at ball1 room3)"], "yes": ["(at-robby robot1 room1)", "(at-robby robot1 room4)", "(free robot1 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball2 room2) (at ball3 room3) (at ball4 room4) (at-robby robot1 room5) (carry robot1 ball1 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": -744620347215734029, "group": "landmarks_gen", "context": "This is a grippers domain, where there is a robot with two grippers. The robot can carry a ball in each. The goal is to take the balls from one room to another. \nThere are 1 robot, 5 rooms, and 4 balls, numbered consecutively. \nCurrently, the robot robot1 is at room2, right gripper is free, and left gripper is carrying the ball ball2. Additionally, ball4 is at room4, ball3 is at room3, ball1 is at room2. The goal is to reach a state where the following facts hold: Ball ball3 is at room3 location, Ball ball4 is at room1 location, Ball ball1 is in room room1, and Ball ball2 is in room room2. The available propositions are: (at-robby ?r ?x) - Robot ?r is at ?x location, (at ?o ?x) - Ball ?o is in room ?x, (free ?r ?g) - The gripper ?g of robot ?r is free, and (carry ?r ?o ?g) - Robot ?r is carrying the ball ?o in gripper ?g.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at ball3 room4)", "(carry robot1 ball3 left1)", "(at ball4 room5)", "(at-robby robot1 room5)", "(at-robby robot1 room3)", "(carry robot1 ball3 right1)", "(at ball4 room3)", "(at ball4 room2)", "(at ball2 room5)", "(at ball2 room3)", "(at ball2 room1)", "(carry robot1 ball2 right1)", "(carry robot1 ball4 right1)", "(at ball1 room4)", "(carry robot1 ball1 left1)", "(carry robot1 ball4 left1)", "(at ball2 room4)", "(at ball3 room1)", "(at ball3 room5)", "(at ball1 room5)", "(carry robot1 ball1 right1)", "(at ball3 room2)", "(at ball1 room3)"], "yes": ["(at-robby robot1 room1)", "(at-robby robot1 room4)", "(free robot1 left1)"]}, "PDDL_domain": "(define (domain gripper-strips)\n    (:requirements :strips :typing)\n    (:types ball gripper robot room - object)\n    (:predicates (at ?o - ball ?x - room)  (at-robby ?r - robot ?x - room)  (carry ?r - robot ?o - ball ?g - gripper)  (free ?r - robot ?g - gripper))\n    (:action drop\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (carry ?r ?obj ?g) (at-robby ?r ?room))\n        :effect (and (at ?obj ?room) (free ?r ?g) (not (carry ?r ?obj ?g)))\n    )\n     (:action move\n        :parameters (?r - robot ?from - room ?to - room)\n        :precondition (at-robby ?r ?from)\n        :effect (and (at-robby ?r ?to) (not (at-robby ?r ?from)))\n    )\n     (:action pick\n        :parameters (?r - robot ?obj - ball ?room - room ?g - gripper)\n        :precondition (and (at ?obj ?room) (at-robby ?r ?room) (free ?r ?g))\n        :effect (and (carry ?r ?obj ?g) (not (at ?obj ?room)) (not (free ?r ?g)))\n    )\n)", "PDDL_problem": "(define (problem gripper-1-5-4)\n    (:domain gripper-strips)\n    (:requirements :strips :typing)\n    (:objects ball1 ball2 ball3 ball4 - ball left1 right1 - gripper robot1 - robot room1 room2 room3 room4 room5 - room)\n    (:init (at ball1 room2) (at ball3 room3) (at ball4 room4) (at-robby robot1 room2) (carry robot1 ball2 left1) (free robot1 right1))\n    (:goal (and (at ball1 room1) (at ball2 room2) (at ball3 room3) (at ball4 room1)))\n)"}
{"id": -3510776553025422854, "group": "landmarks_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera2 can be calibrated on objective2. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera1 supports high_res. Camera camera0 supports low_res and colour. Camera camera2 supports high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint0. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover0 is at waypoint0. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Soil data was communicated from waypoint waypoint0; Image objective1 was communicated in mode high_res. Rover rover1 has soil analyzed in waypoint waypoint0. Rover rover1 has image objective1 in mode low_res. Rover rover1 has image objective1 in mode high_res. Store(s) store0 is empty. Store(s) store1 is full.  The goal is to reach a state where the following facts hold: Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, and Image objective1 was communicated in mode high_res. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(communicated_image_data objective2 high_res)", "(communicated_image_data objective2 colour)", "(calibrated camera0 rover1)", "(have_image rover0 objective0 high_res)", "(communicated_image_data objective0 low_res)", "(at rover1 waypoint2)", "(calibrated camera2 rover0)", "(have_soil_analysis rover0 waypoint0)", "(have_image rover1 objective2 low_res)", "(at rover0 waypoint2)", "(at_soil_sample waypoint0)", "(have_image rover1 objective2 high_res)", "(communicated_image_data objective2 low_res)", "(calibrated camera1 rover1)", "(communicated_image_data objective1 colour)", "(empty store1)", "(have_image rover1 objective2 colour)", "(at rover1 waypoint0)", "(have_image rover1 objective0 low_res)", "(at rover0 waypoint1)", "(have_image rover0 objective1 high_res)", "(communicated_image_data objective0 high_res)", "(communicated_image_data objective0 colour)", "(have_image rover1 objective1 colour)", "(have_image rover1 objective0 high_res)", "(have_image rover1 objective0 colour)", "(have_image rover0 objective2 high_res)"], "yes": ["(have_rock_analysis rover0 waypoint0)", "(full store0)"]}, "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-3-2-3-2020)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint0) (at rover1 waypoint1) (at_lander general waypoint1) (at_rock_sample waypoint0) (available rover0) (available rover1) (calibration_target camera0 objective0) (calibration_target camera1 objective0) (calibration_target camera2 objective2) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint1) (can_traverse rover1 waypoint1 waypoint0) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_image_data objective1 high_res) (communicated_soil_data waypoint0) (empty store0) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store1) (have_image rover1 objective1 high_res) (have_image rover1 objective1 low_res) (have_soil_analysis rover1 waypoint0) (on_board camera0 rover1) (on_board camera1 rover1) (on_board camera2 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera0 low_res) (supports camera1 high_res) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint2) (visible_from objective1 waypoint0) (visible_from objective1 waypoint1) (visible_from objective2 waypoint0))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_rock_data waypoint0) (communicated_image_data objective1 high_res) (communicated_image_data objective1 low_res)))\n)"}
{"id": 7295021335728573280, "group": "landmarks_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 5 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective4. Camera camera1 supports high_res. Camera camera0 supports low_res and colour. Camera camera2 supports high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Objective objective4 is visible from waypoint1. Objective objective3 is visible from waypoint0. Objective objective2 is visible from waypoint0. Objective objective1 is visible from waypoint1 and waypoint0. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint1; Soil data was communicated from waypoint waypoint0; Image objective2 was communicated in mode high_res. Image objective0 was communicated in mode high_res. Rover rover1 has soil analyzed in waypoint waypoint0. Rover rover0 has soil analyzed in waypoint waypoint2. Rover rover0 has rock analyzed in waypoint waypoint1. Rover rover0 has rock analyzed in waypoint waypoint2. Rover rover0 has image objective0 in mode high_res. Rover rover0 has image objective2 in mode high_res. Store(s) store1 and store0 are full.  The goal is to reach a state where the following facts hold: Image objective2 was communicated in mode high_res, Soil data was communicated from waypoint waypoint2;, Soil data was communicated from waypoint waypoint0;, Rock data was communicated from waypoint waypoint1;, Image objective0 was communicated in mode high_res, and Rock data was communicated from waypoint waypoint2;. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(communicated_image_data objective1 low_res)", "(communicated_image_data objective2 colour)", "(calibrated camera0 rover1)", "(at_rock_sample waypoint1)", "(have_image rover1 objective4 low_res)", "(communicated_image_data objective0 low_res)", "(at_rock_sample waypoint2)", "(at rover1 waypoint2)", "(have_image rover1 objective1 low_res)", "(calibrated camera2 rover0)", "(have_soil_analysis rover0 waypoint0)", "(have_image rover1 objective2 low_res)", "(communicated_image_data objective3 high_res)", "(communicated_image_data objective4 colour)", "(at rover0 waypoint0)", "(at_soil_sample waypoint0)", "(have_image rover1 objective3 low_res)", "(have_image rover1 objective2 high_res)", "(communicated_image_data objective2 low_res)", "(calibrated camera1 rover1)", "(at_soil_sample waypoint2)", "(communicated_image_data objective1 colour)", "(empty store1)", "(have_image rover1 objective2 colour)", "(have_image rover0 objective4 high_res)", "(empty store0)", "(have_image rover1 objective0 low_res)", "(at rover0 waypoint1)", "(communicated_image_data objective4 high_res)", "(communicated_image_data objective3 colour)", "(have_image rover1 objective4 high_res)", "(communicated_image_data objective4 low_res)", "(have_image rover0 objective1 high_res)", "(communicated_image_data objective0 colour)", "(communicated_image_data objective3 low_res)", "(have_image rover1 objective1 colour)", "(have_image rover1 objective0 high_res)", "(communicated_image_data objective1 high_res)", "(have_image rover1 objective3 high_res)", "(have_image rover1 objective3 colour)", "(have_image rover1 objective0 colour)", "(have_image rover0 objective3 high_res)", "(have_image rover1 objective1 high_res)", "(at rover1 waypoint1)", "(have_image rover1 objective4 colour)", "(have_soil_analysis rover1 waypoint2)"], "yes": []}, "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-5-2-5-2020)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 objective3 objective4 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint2) (at rover1 waypoint0) (at_lander general waypoint1) (available rover0) (available rover1) (calibration_target camera0 objective0) (calibration_target camera1 objective0) (calibration_target camera2 objective4) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint1) (can_traverse rover1 waypoint1 waypoint0) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_image_data objective0 high_res) (communicated_image_data objective2 high_res) (communicated_rock_data waypoint1) (communicated_soil_data waypoint0) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store0) (full store1) (have_image rover0 objective0 high_res) (have_image rover0 objective2 high_res) (have_rock_analysis rover0 waypoint1) (have_rock_analysis rover0 waypoint2) (have_soil_analysis rover0 waypoint2) (have_soil_analysis rover1 waypoint0) (on_board camera0 rover1) (on_board camera1 rover1) (on_board camera2 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera0 low_res) (supports camera1 high_res) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint2) (visible_from objective1 waypoint0) (visible_from objective1 waypoint1) (visible_from objective2 waypoint0) (visible_from objective3 waypoint0) (visible_from objective4 waypoint1))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_soil_data waypoint2) (communicated_rock_data waypoint2) (communicated_rock_data waypoint1) (communicated_image_data objective2 high_res) (communicated_image_data objective0 high_res)))\n)"}
{"id": 2633179121917618633, "group": "landmarks_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover1 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 and camera0 on board. Rover rover1 has camera2 on board. Camera camera2 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera0 can be calibrated on objective1. Camera camera0 supports colour. Camera camera1 supports low_res. Camera camera2 supports colour and high_res. Rover rover0 can traverse from waypoint2 to waypoint0, waypoint0 to waypoint1, waypoint0 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective1 is visible from waypoint2. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint1. Rovers rover0 and rover1 are available. Image objective0 was communicated in mode low_res. Rover rover1 has soil analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode low_res. Rover rover1 has its camera camera2 calibrated. Store(s) store0 and store1 are empty.  The goal is to reach a state where the following facts hold: Image objective0 was communicated in mode low_res, Soil data was communicated from waypoint waypoint1;, Rock data was communicated from waypoint waypoint1;, and Image objective1 was communicated in mode colour. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(calibrated camera1 rover0)", "(at_soil_sample waypoint1)", "(have_soil_analysis rover0 waypoint1)", "(at rover1 waypoint2)", "(at rover0 waypoint0)", "(calibrated camera0 rover0)", "(have_image rover0 objective1 low_res)", "(have_image rover0 objective0 colour)", "(at rover1 waypoint0)", "(at rover0 waypoint1)", "(communicated_image_data objective0 high_res)", "(communicated_image_data objective0 colour)", "(have_image rover1 objective1 colour)", "(have_image rover1 objective0 high_res)", "(communicated_image_data objective1 high_res)", "(have_image rover1 objective0 colour)", "(have_image rover1 objective1 high_res)", "(communicated_image_data objective1 low_res)", "(have_image rover0 objective1 colour)", "(full store0)"], "yes": ["(have_rock_analysis rover1 waypoint1)", "(full store1)"]}, "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-2-2-3-2021)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint2) (at rover1 waypoint1) (at_lander general waypoint1) (at_rock_sample waypoint1) (available rover0) (available rover1) (calibrated camera2 rover1) (calibration_target camera0 objective1) (calibration_target camera1 objective0) (calibration_target camera2 objective0) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_image_data objective0 low_res) (empty store0) (empty store1) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (have_image rover0 objective0 low_res) (have_soil_analysis rover1 waypoint1) (on_board camera0 rover0) (on_board camera1 rover0) (on_board camera2 rover1) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera1 low_res) (supports camera2 colour) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint2))\n    (:goal (and (communicated_soil_data waypoint1) (communicated_rock_data waypoint1) (communicated_image_data objective0 low_res) (communicated_image_data objective1 colour)))\n)"}
{"id": 1190464430800099784, "group": "landmarks_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 2 cameras, 3 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover0 is equipped for soil analysis. Rover(s) rover1 and rover0 are equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover0 has camera1 on board. Rover rover1 has camera0 on board. Camera camera1 can be calibrated on objective1. Camera camera0 can be calibrated on objective2. Camera camera1 supports high_res and low_res. Camera camera0 supports low_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint2 to waypoint0, waypoint1 to waypoint2, waypoint0 to waypoint2. Rover rover1 can traverse from waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint2 to waypoint0, waypoint0 to waypoint2. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Objective objective1 is visible from waypoint1. Objective objective0 is visible from waypoint0 and waypoint1. Objective objective2 is visible from waypoint1. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover1 is at waypoint0. Rover rover0 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0. Soil can be sampled at the following location(s): waypoint0 and waypoint2. Rovers rover0 and rover1 are available. Rock data was communicated from waypoint waypoint2; Rover rover1 has rock analyzed in waypoint waypoint2. Rover rover0 has image objective1 in mode low_res. Rover rover0 has image objective1 in mode high_res. Store(s) store0 is empty. Store(s) store1 is full.  The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Image objective0 was communicated in mode low_res, Soil data was communicated from waypoint waypoint2;, Soil data was communicated from waypoint waypoint0;, Image objective1 was communicated in mode low_res, Rock data was communicated from waypoint waypoint2;, and Image objective1 was communicated in mode high_res. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(communicated_image_data objective2 high_res)", "(calibrated camera1 rover0)", "(calibrated camera0 rover1)", "(have_image rover0 objective0 high_res)", "(at_rock_sample waypoint2)", "(at rover1 waypoint2)", "(have_image rover1 objective1 low_res)", "(have_rock_analysis rover0 waypoint0)", "(have_image rover1 objective2 low_res)", "(communicated_image_data objective2 low_res)", "(empty store1)", "(have_image rover0 objective2 low_res)", "(have_image rover0 objective0 low_res)", "(have_image rover1 objective0 low_res)", "(communicated_image_data objective0 high_res)", "(have_rock_analysis rover0 waypoint2)", "(have_rock_analysis rover1 waypoint0)", "(at rover1 waypoint1)", "(have_image rover0 objective2 high_res)"], "yes": ["(have_soil_analysis rover0 waypoint2)", "(have_soil_analysis rover0 waypoint0)", "(at rover0 waypoint2)", "(at rover0 waypoint0)", "(full store0)"]}, "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-3-2-3-2022)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 - camera general - lander colour high_res low_res - mode objective0 objective1 objective2 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint1) (at rover1 waypoint0) (at_lander general waypoint1) (at_rock_sample waypoint0) (at_soil_sample waypoint0) (at_soil_sample waypoint2) (available rover0) (available rover1) (calibration_target camera0 objective2) (calibration_target camera1 objective1) (can_traverse rover0 waypoint0 waypoint2) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint0) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint2) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint0) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (communicated_rock_data waypoint2) (empty store0) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_rock_analysis rover1) (equipped_for_soil_analysis rover0) (full store1) (have_image rover0 objective1 high_res) (have_image rover0 objective1 low_res) (have_rock_analysis rover1 waypoint2) (on_board camera0 rover1) (on_board camera1 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 low_res) (supports camera1 high_res) (supports camera1 low_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint0) (visible_from objective0 waypoint1) (visible_from objective1 waypoint1) (visible_from objective2 waypoint1))\n    (:goal (and (communicated_soil_data waypoint2) (communicated_soil_data waypoint0) (communicated_rock_data waypoint0) (communicated_rock_data waypoint2) (communicated_image_data objective0 low_res) (communicated_image_data objective1 high_res) (communicated_image_data objective1 low_res)))\n)"}
{"id": 5241510841169372778, "group": "landmarks_gen", "context": "This is a Rovers domain where rovers must navigate between waypoints gathering data and transmitting it back to a lander. Rovers cannot navigate to all waypoints and this makes particular routes impassable to some of the rovers. Data transmission is also constrained by the visibility of the lander from the waypoints. \nThere are 2 rovers, 3 waypoints, 2 stores, 3 cameras, 2 objectives numbered consecutively. Further, there is 1 lander and 3 modes for the camera namely colour, high resolution, and low resolution.  \nRover(s) rover1 and rover0 are equipped for soil analysis. Rover(s) rover0 is equipped for rock analysis. Rover(s) rover1 and rover0 are equipped for imaging. Rover rover1 has store store1. Rover rover0 has store store0. Rover rover1 has camera0 and camera1 on board. Rover rover0 has camera2 on board. Camera camera0 can be calibrated on objective0. Camera camera1 can be calibrated on objective0. Camera camera2 can be calibrated on objective1. Camera camera1 supports high_res. Camera camera0 supports low_res and colour. Camera camera2 supports high_res. Rover rover0 can traverse from waypoint2 to waypoint1, waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint1 to waypoint0. Rover rover1 can traverse from waypoint0 to waypoint1, waypoint1 to waypoint2, waypoint2 to waypoint1, waypoint1 to waypoint0. Waypoint(s) are visible from waypoint2: waypoint0 and waypoint1. Waypoint(s) are visible from waypoint0: waypoint1 and waypoint2. Waypoint(s) are visible from waypoint1: waypoint0 and waypoint2. Objective objective1 is visible from waypoint0 and waypoint1. Objective objective0 is visible from waypoint2. Lander general is at waypoint waypoint1.  \nCurrently, Rover rover0 is at waypoint2. Rover rover1 is at waypoint1. Rocks can be sampled at the following location(s): waypoint0 and waypoint2. Soil can be sampled at the following location(s): waypoint0. Rovers rover0 and rover1 are available. Rover rover1 has soil analyzed in waypoint waypoint1. Rover rover0 has image objective0 in mode high_res. Store(s) store0 is empty. Store(s) store1 is full.  The goal is to reach a state where the following facts hold: Rock data was communicated from waypoint waypoint0;, Soil data was communicated from waypoint waypoint0;, Image objective0 was communicated in mode high_res, Soil data was communicated from waypoint waypoint1;, Rock data was communicated from waypoint waypoint2;, and Image objective1 was communicated in mode high_res. The available propositions are: (at ?x ?y) - Rover ?x is at ?y, (empty ?s) - Store(s) ?s is empty, (have_rock_analysis ?r ?w) - Rover ?r has rock analyzed in waypoint ?w, (have_soil_analysis ?r ?w) - Rover ?r has soil analyzed in waypoint ?w, (full ?s) - Store(s) ?s is full, (calibrated ?c ?r) - Rover ?r has its camera ?c calibrated, (available ?r) - Rover ?r is available, (have_image ?r ?o ?m) - Rover ?r has image ?o in mode ?m, (communicated_soil_data ?w) - Soil data was communicated from waypoint ?w; , (communicated_rock_data ?w) - Rock data was communicated from waypoint ?w; , (communicated_image_data ?o ?m) - Image ?o was communicated in mode ?m, (at_soil_sample ?w) - Soil can be sampled at the following location(s): ?w, (at_rock_sample ?w) - Rocks can be sampled at the following location(s): ?w, and (channel_free ?l) - Channel ?l is free.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(calibrated camera0 rover1)", "(at_soil_sample waypoint1)", "(communicated_image_data objective0 low_res)", "(have_soil_analysis rover0 waypoint1)", "(at rover1 waypoint2)", "(have_image rover1 objective1 low_res)", "(calibrated camera2 rover0)", "(have_soil_analysis rover0 waypoint0)", "(calibrated camera1 rover1)", "(communicated_image_data objective1 colour)", "(empty store1)", "(have_soil_analysis rover1 waypoint0)", "(at rover1 waypoint0)", "(have_image rover1 objective0 low_res)", "(have_image rover0 objective1 high_res)", "(communicated_image_data objective0 colour)", "(have_image rover1 objective1 colour)", "(have_image rover1 objective0 high_res)", "(have_image rover1 objective0 colour)", "(have_image rover1 objective1 high_res)", "(communicated_image_data objective1 low_res)"], "yes": ["(have_rock_analysis rover0 waypoint2)", "(have_rock_analysis rover0 waypoint0)", "(at rover0 waypoint0)", "(at rover0 waypoint1)", "(full store0)"]}, "PDDL_domain": "(define (domain rover)\n    (:requirements :strips :typing)\n    (:types camera lander mode objective rover store waypoint)\n    (:predicates (at ?x - rover ?y - waypoint)  (at_lander ?x - lander ?y - waypoint)  (at_rock_sample ?w - waypoint)  (at_soil_sample ?w - waypoint)  (available ?r - rover)  (calibrated ?c - camera ?r - rover)  (calibration_target ?i - camera ?o - objective)  (can_traverse ?r - rover ?x - waypoint ?y - waypoint)  (channel_free ?l - lander)  (communicated_image_data ?o - objective ?m - mode)  (communicated_rock_data ?w - waypoint)  (communicated_soil_data ?w - waypoint)  (empty ?s - store)  (equipped_for_imaging ?r - rover)  (equipped_for_rock_analysis ?r - rover)  (equipped_for_soil_analysis ?r - rover)  (full ?s - store)  (have_image ?r - rover ?o - objective ?m - mode)  (have_rock_analysis ?r - rover ?w - waypoint)  (have_soil_analysis ?r - rover ?w - waypoint)  (on_board ?i - camera ?r - rover)  (store_of ?s - store ?r - rover)  (supports ?c - camera ?m - mode)  (visible ?w - waypoint ?p - waypoint)  (visible_from ?o - objective ?w - waypoint))\n    (:action calibrate\n        :parameters (?r - rover ?i - camera ?t - objective ?w - waypoint)\n        :precondition (and (equipped_for_imaging ?r) (calibration_target ?i ?t) (at ?r ?w) (visible_from ?t ?w) (on_board ?i ?r))\n        :effect (calibrated ?i ?r)\n    )\n     (:action communicate_image_data\n        :parameters (?r - rover ?l - lander ?o - objective ?m - mode ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_image ?r ?o ?m) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_image_data ?o ?m) (available ?r))\n    )\n     (:action communicate_rock_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_rock_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_rock_data ?p) (available ?r))\n    )\n     (:action communicate_soil_data\n        :parameters (?r - rover ?l - lander ?p - waypoint ?x - waypoint ?y - waypoint)\n        :precondition (and (at ?r ?x) (at_lander ?l ?y) (have_soil_analysis ?r ?p) (visible ?x ?y) (available ?r) (channel_free ?l))\n        :effect (and (not (available ?r)) (not (channel_free ?l)) (channel_free ?l) (communicated_soil_data ?p) (available ?r))\n    )\n     (:action drop\n        :parameters (?x - rover ?y - store)\n        :precondition (and (store_of ?y ?x) (full ?y))\n        :effect (and (not (full ?y)) (empty ?y))\n    )\n     (:action navigate\n        :parameters (?x - rover ?y - waypoint ?z - waypoint)\n        :precondition (and (can_traverse ?x ?y ?z) (available ?x) (at ?x ?y) (visible ?y ?z))\n        :effect (and (not (at ?x ?y)) (at ?x ?z))\n    )\n     (:action sample_rock\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_rock_sample ?p) (equipped_for_rock_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_rock_analysis ?x ?p) (not (at_rock_sample ?p)))\n    )\n     (:action sample_soil\n        :parameters (?x - rover ?s - store ?p - waypoint)\n        :precondition (and (at ?x ?p) (at_soil_sample ?p) (equipped_for_soil_analysis ?x) (store_of ?s ?x) (empty ?s))\n        :effect (and (not (empty ?s)) (full ?s) (have_soil_analysis ?x ?p) (not (at_soil_sample ?p)))\n    )\n     (:action take_image\n        :parameters (?r - rover ?p - waypoint ?o - objective ?i - camera ?m - mode)\n        :precondition (and (calibrated ?i ?r) (on_board ?i ?r) (equipped_for_imaging ?r) (supports ?i ?m) (visible_from ?o ?p) (at ?r ?p))\n        :effect (and (have_image ?r ?o ?m) (not (calibrated ?i ?r)))\n    )\n)", "PDDL_problem": "(define (problem prob-2-3-2-2-3-2020)\n    (:domain rover)\n    (:requirements :strips :typing)\n    (:objects camera0 camera1 camera2 - camera general - lander colour high_res low_res - mode objective0 objective1 - objective rover0 rover1 - rover store0 store1 - store waypoint0 waypoint1 waypoint2 - waypoint)\n    (:init (at rover0 waypoint2) (at rover1 waypoint1) (at_lander general waypoint1) (at_rock_sample waypoint0) (at_rock_sample waypoint2) (at_soil_sample waypoint0) (available rover0) (available rover1) (calibration_target camera0 objective0) (calibration_target camera1 objective0) (calibration_target camera2 objective1) (can_traverse rover0 waypoint0 waypoint1) (can_traverse rover0 waypoint1 waypoint0) (can_traverse rover0 waypoint1 waypoint2) (can_traverse rover0 waypoint2 waypoint1) (can_traverse rover1 waypoint0 waypoint1) (can_traverse rover1 waypoint1 waypoint0) (can_traverse rover1 waypoint1 waypoint2) (can_traverse rover1 waypoint2 waypoint1) (channel_free general) (empty store0) (equipped_for_imaging rover0) (equipped_for_imaging rover1) (equipped_for_rock_analysis rover0) (equipped_for_soil_analysis rover0) (equipped_for_soil_analysis rover1) (full store1) (have_image rover0 objective0 high_res) (have_soil_analysis rover1 waypoint1) (on_board camera0 rover1) (on_board camera1 rover1) (on_board camera2 rover0) (store_of store0 rover0) (store_of store1 rover1) (supports camera0 colour) (supports camera0 low_res) (supports camera1 high_res) (supports camera2 high_res) (visible waypoint0 waypoint1) (visible waypoint0 waypoint2) (visible waypoint1 waypoint0) (visible waypoint1 waypoint2) (visible waypoint2 waypoint0) (visible waypoint2 waypoint1) (visible_from objective0 waypoint2) (visible_from objective1 waypoint0) (visible_from objective1 waypoint1))\n    (:goal (and (communicated_soil_data waypoint0) (communicated_soil_data waypoint1) (communicated_rock_data waypoint0) (communicated_rock_data waypoint2) (communicated_image_data objective1 high_res) (communicated_image_data objective0 high_res)))\n)"}
{"id": 374220601309851534, "group": "landmarks_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3, loc-x3-y3, and loc-x0-y0. \nCurrently, the robot is in place loc-x1-y0.The following places have been visited: loc-x3-y2, loc-x1-y1, loc-x2-y0, loc-x1-y2, loc-x2-y2, loc-x0-y1, loc-x1-y0, loc-x2-y3, loc-x0-y2, and loc-x1-y3. The goal is to reach a state where the following facts hold: Place loc-x3-y0 is visited, Place loc-x3-y2 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x1-y2 is visited, Place loc-x2-y1 is visited, Place loc-x2-y2 is visited, Place loc-x0-y1 is visited, Place loc-x1-y0 is visited, Place loc-x2-y3 is visited, Place loc-x3-y1 is visited, Place loc-x0-y2 is visited, and Place loc-x1-y3 is visited. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at-robot loc-x1-y2)", "(at-robot loc-x3-y0)", "(at-robot loc-x2-y3)", "(at-robot loc-x1-y3)", "(at-robot loc-x3-y1)", "(at-robot loc-x0-y1)", "(at-robot loc-x2-y0)", "(at-robot loc-x3-y2)", "(at-robot loc-x2-y1)", "(at-robot loc-x0-y2)", "(at-robot loc-x2-y2)", "(at-robot loc-x1-y1)"], "yes": []}, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-3-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 - place)\n    (:init (at-robot loc-x1-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x1-y2) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y2))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2)))\n)"}
{"id": -3519325945012220404, "group": "landmarks_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The unavailable cells are loc-x0-y3 and loc-x0-y0. \nCurrently, the robot is in place loc-x1-y1.The following places have been visited: loc-x3-y0, loc-x3-y2, loc-x1-y1, loc-x2-y0, loc-x1-y2, loc-x2-y1, loc-x2-y2, loc-x0-y1, loc-x3-y3, loc-x2-y3, loc-x3-y1, loc-x0-y2, and loc-x1-y3. The goal is to reach a state where the following facts hold: Place loc-x3-y0 is visited, Place loc-x3-y2 is visited, Place loc-x1-y1 is visited, Place loc-x2-y0 is visited, Place loc-x1-y2 is visited, Place loc-x2-y1 is visited, Place loc-x2-y2 is visited, Place loc-x0-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y0 is visited, Place loc-x2-y3 is visited, Place loc-x3-y1 is visited, Place loc-x0-y2 is visited, and Place loc-x1-y3 is visited. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at-robot loc-x3-y0)", "(at-robot loc-x1-y2)", "(at-robot loc-x2-y3)", "(at-robot loc-x1-y3)", "(at-robot loc-x1-y0)", "(at-robot loc-x3-y3)", "(at-robot loc-x3-y1)", "(at-robot loc-x0-y1)", "(at-robot loc-x2-y0)", "(at-robot loc-x3-y2)", "(at-robot loc-x2-y1)", "(at-robot loc-x0-y2)", "(at-robot loc-x2-y2)"], "yes": []}, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-2-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x1-y1) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x1-y2) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": -6363950698238859515, "group": "landmarks_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. There are no unavailable cells. \nCurrently, the robot is in place loc-x3-y1.The following places have been visited: loc-x3-y2, loc-x1-y1, loc-x0-y3, loc-x1-y2, loc-x2-y1, loc-x2-y2, loc-x0-y1, loc-x3-y3, loc-x2-y3, loc-x3-y1, loc-x0-y2, and loc-x1-y3. The goal is to reach a state where the following facts hold: Place loc-x3-y0 is visited, Place loc-x3-y2 is visited, Place loc-x1-y1 is visited, Place loc-x0-y3 is visited, Place loc-x2-y0 is visited, Place loc-x1-y2 is visited, Place loc-x2-y1 is visited, Place loc-x2-y2 is visited, Place loc-x0-y1 is visited, Place loc-x0-y0 is visited, Place loc-x3-y3 is visited, Place loc-x1-y0 is visited, Place loc-x2-y3 is visited, Place loc-x3-y1 is visited, Place loc-x0-y2 is visited, and Place loc-x1-y3 is visited. The available propositions are: (at-robot ?x) - The robot is in place ?x and (visited ?x) - Place ?x is visited.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at-robot loc-x1-y2)", "(at-robot loc-x3-y0)", "(at-robot loc-x2-y3)", "(at-robot loc-x1-y3)", "(at-robot loc-x1-y0)", "(at-robot loc-x3-y3)", "(at-robot loc-x0-y1)", "(at-robot loc-x0-y0)", "(at-robot loc-x3-y2)", "(at-robot loc-x2-y0)", "(at-robot loc-x2-y1)", "(at-robot loc-x0-y2)", "(at-robot loc-x0-y3)", "(at-robot loc-x2-y2)", "(at-robot loc-x1-y1)"], "yes": []}, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-0-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x3-y1) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 1051296040055604884, "group": "landmarks_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The only unavailable cell is loc-x0-y0. \nCurrently, the robot is in place loc-x2-y2.The following places have been visited: loc-x3-y2, loc-x2-y1, loc-x2-y2, loc-x3-y3, and loc-x3-y1. The goal is to reach a state where the following facts hold: Place loc-x3-y0 is visited, Place loc-x3-y2 is visited, Place loc-x1-y1 is visited, Place loc-x0-y3 is visited, Place loc-x2-y0 is visited, Place loc-x1-y2 is visited, Place loc-x2-y1 is visited, Place loc-x2-y2 is visited, Place loc-x0-y1 is visited, Place loc-x3-y3 is visited, Place loc-x1-y0 is visited, Place loc-x2-y3 is visited, Place loc-x3-y1 is visited, Place loc-x0-y2 is visited, and Place loc-x1-y3 is visited. The available propositions are: (at-robot ?x) - The robot is at ?x and (visited ?x) - Place ?x is visited.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at-robot loc-x3-y0)", "(at-robot loc-x1-y2)", "(at-robot loc-x2-y3)", "(at-robot loc-x1-y3)", "(at-robot loc-x1-y0)", "(at-robot loc-x3-y3)", "(at-robot loc-x3-y1)", "(at-robot loc-x0-y1)", "(at-robot loc-x3-y2)", "(at-robot loc-x2-y0)", "(at-robot loc-x2-y1)", "(at-robot loc-x0-y2)", "(at-robot loc-x0-y3)", "(at-robot loc-x1-y1)"], "yes": []}, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-1-2020)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y1 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x2-y2) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y1 loc-x3-y1) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y0 loc-x3-y1) (connected loc-x3-y1 loc-x2-y1) (connected loc-x3-y1 loc-x3-y0) (connected loc-x3-y1 loc-x3-y2) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y1) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3))\n    (:goal (and (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y1) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
{"id": 2769321894672039299, "group": "landmarks_gen", "context": "This is a visitall domain where a robot in a grid must visit all the cells or places in the grid. There are some unavailable places in the grid. \nThe grid size is 4x4, and the location cell names are of the form loc-xi-yj (e.g., loc-x0-y2 or loc-x1-y1). \nThe grid cells are connected to their available neighbors. The only unavailable cell is loc-x3-y1. \nCurrently, the robot is in place loc-x1-y1.The following places have been visited: loc-x3-y0, loc-x1-y1, loc-x0-y3, loc-x2-y0, loc-x2-y1, loc-x0-y1, loc-x0-y0, loc-x1-y0, and loc-x0-y2. The goal is to reach a state where the following facts hold: Place loc-x3-y0 is visited, Place loc-x3-y2 is visited, Place loc-x1-y1 is visited, Place loc-x0-y3 is visited, Place loc-x2-y0 is visited, Place loc-x1-y2 is visited, Place loc-x2-y1 is visited, Place loc-x2-y2 is visited, Place loc-x0-y1 is visited, Place loc-x0-y0 is visited, Place loc-x3-y3 is visited, Place loc-x1-y0 is visited, Place loc-x2-y3 is visited, Place loc-x0-y2 is visited, and Place loc-x1-y3 is visited. The available propositions are: (at-robot ?x) - The robot is in place ?x and (visited ?x) - Place ?x is visited.", "question": "Generate a non-trivial fact landmark, one that does not hold in the initial state or goal.", "answer": {"no": ["(at-robot loc-x3-y0)", "(at-robot loc-x1-y2)", "(at-robot loc-x1-y0)", "(at-robot loc-x2-y3)", "(at-robot loc-x1-y3)", "(at-robot loc-x3-y3)", "(at-robot loc-x0-y1)", "(at-robot loc-x0-y0)", "(at-robot loc-x2-y0)", "(at-robot loc-x3-y2)", "(at-robot loc-x2-y1)", "(at-robot loc-x0-y2)", "(at-robot loc-x0-y3)", "(at-robot loc-x2-y2)"], "yes": []}, "PDDL_domain": "(define (domain grid-visit-all)\n    (:requirements :typing)\n    (:types place - object)\n    (:predicates (at-robot ?x - place)  (connected ?x - place ?y - place)  (visited ?x - place))\n    (:action move\n        :parameters (?curpos - place ?nextpos - place)\n        :precondition (and (at-robot ?curpos) (connected ?curpos ?nextpos))\n        :effect (and (at-robot ?nextpos) (not (at-robot ?curpos)) (visited ?nextpos))\n    )\n)", "PDDL_problem": "(define (problem grid-4-4-1-2021)\n    (:domain grid-visit-all)\n    (:requirements :typing)\n    (:objects loc-x0-y0 loc-x0-y1 loc-x0-y2 loc-x0-y3 loc-x1-y0 loc-x1-y1 loc-x1-y2 loc-x1-y3 loc-x2-y0 loc-x2-y1 loc-x2-y2 loc-x2-y3 loc-x3-y0 loc-x3-y2 loc-x3-y3 - place)\n    (:init (at-robot loc-x1-y1) (connected loc-x0-y0 loc-x0-y1) (connected loc-x0-y0 loc-x1-y0) (connected loc-x0-y1 loc-x0-y0) (connected loc-x0-y1 loc-x0-y2) (connected loc-x0-y1 loc-x1-y1) (connected loc-x0-y2 loc-x0-y1) (connected loc-x0-y2 loc-x0-y3) (connected loc-x0-y2 loc-x1-y2) (connected loc-x0-y3 loc-x0-y2) (connected loc-x0-y3 loc-x1-y3) (connected loc-x1-y0 loc-x0-y0) (connected loc-x1-y0 loc-x1-y1) (connected loc-x1-y0 loc-x2-y0) (connected loc-x1-y1 loc-x0-y1) (connected loc-x1-y1 loc-x1-y0) (connected loc-x1-y1 loc-x1-y2) (connected loc-x1-y1 loc-x2-y1) (connected loc-x1-y2 loc-x0-y2) (connected loc-x1-y2 loc-x1-y1) (connected loc-x1-y2 loc-x1-y3) (connected loc-x1-y2 loc-x2-y2) (connected loc-x1-y3 loc-x0-y3) (connected loc-x1-y3 loc-x1-y2) (connected loc-x1-y3 loc-x2-y3) (connected loc-x2-y0 loc-x1-y0) (connected loc-x2-y0 loc-x2-y1) (connected loc-x2-y0 loc-x3-y0) (connected loc-x2-y1 loc-x1-y1) (connected loc-x2-y1 loc-x2-y0) (connected loc-x2-y1 loc-x2-y2) (connected loc-x2-y2 loc-x1-y2) (connected loc-x2-y2 loc-x2-y1) (connected loc-x2-y2 loc-x2-y3) (connected loc-x2-y2 loc-x3-y2) (connected loc-x2-y3 loc-x1-y3) (connected loc-x2-y3 loc-x2-y2) (connected loc-x2-y3 loc-x3-y3) (connected loc-x3-y0 loc-x2-y0) (connected loc-x3-y2 loc-x2-y2) (connected loc-x3-y2 loc-x3-y3) (connected loc-x3-y3 loc-x2-y3) (connected loc-x3-y3 loc-x3-y2) (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x3-y0))\n    (:goal (and (visited loc-x0-y0) (visited loc-x0-y1) (visited loc-x0-y2) (visited loc-x0-y3) (visited loc-x1-y0) (visited loc-x1-y1) (visited loc-x1-y2) (visited loc-x1-y3) (visited loc-x2-y0) (visited loc-x2-y1) (visited loc-x2-y2) (visited loc-x2-y3) (visited loc-x3-y0) (visited loc-x3-y2) (visited loc-x3-y3)))\n)"}
